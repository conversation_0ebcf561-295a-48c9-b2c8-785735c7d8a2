{"mcpServers": {"aws-docs": {"command": "uvx", "args": ["awslabs.aws-documentation-mcp-server@latest"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": []}, "python-docs": {"command": "uvx", "args": ["python-mcp-server@latest"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": ["mcp_python_docs_search"]}, "pytorch-docs": {"command": "uvx", "args": ["pytorch-mcp-server@latest"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": ["mcp_pytorch_docs_search"]}, "data-viz": {"command": "uvx", "args": ["data-visualization-mcp-server@latest"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": []}}}