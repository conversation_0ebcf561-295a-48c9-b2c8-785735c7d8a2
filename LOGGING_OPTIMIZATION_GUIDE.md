# BEV Lane Heatmap Head 日志系统优化指南


📖 优化后的日志系统使用指南:

1. 日志级别控制:
   - 通过环境变量 BEV_LANE_LOG_LEVEL 控制日志级别
   - 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
   - 默认值: WARNING (生产环境推荐)

2. 使用示例:
   # 开发调试时 - 显示所有调试信息
   export BEV_LANE_LOG_LEVEL=DEBUG
   
   # 生产环境 - 只显示警告和错误
   export BEV_LANE_LOG_LEVEL=WARNING
   
   # 完全静默 - 只显示严重错误
   export BEV_LANE_LOG_LEVEL=ERROR

3. 日志级别说明:
   - DEBUG: 详细的调试信息（开发时使用）
   - INFO: 重要的配置和状态信息
   - WARNING: 警告信息（默认级别）
   - ERROR: 错误信息
   - CRITICAL: 严重错误

4. 性能优化:
   - 生产环境建议使用 WARNING 或更高级别
   - DEBUG 级别会输出大量信息，影响性能
   - 日志格式包含时间戳，便于调试

5. 代码变更:
   - 所有 print() 语句已替换为适当的 logger 调用
   - 维度错误使用 logger.error()
   - 初始化信息使用 logger.info()
   - 调试信息使用 logger.debug()
