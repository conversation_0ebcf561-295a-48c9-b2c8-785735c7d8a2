#!/usr/bin/env python3
"""
Clustering Parameter Analysis Tool for BEV Lane Detection

This script analyzes clustering parameters and provides recommendations
for fixing the "bent lanes" issue without requiring a trained model.
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
import argparse
import yaml
from pathlib import Path

def simulate_lane_points():
    """
    Simulate typical lane detection scenario with two parallel lanes
    that might be incorrectly merged due to poor clustering parameters.
    """
    np.random.seed(42)
    
    # Lane 1: Straight lane (left)
    lane1_x = np.linspace(10, 50, 15) + np.random.normal(0, 0.1, 15)
    lane1_y = np.full(15, 20) + np.random.normal(0, 0.2, 15)
    lane1_points = np.column_stack([lane1_x, lane1_y])
    lane1_labels = np.zeros(15)  # Ground truth: lane 0
    
    # Lane 2: Parallel lane (right) - 3.5m apart (typical lane width)
    lane2_x = np.linspace(12, 52, 15) + np.random.normal(0, 0.1, 15)
    lane2_y = np.full(15, 23.5) + np.random.normal(0, 0.2, 15)
    lane2_points = np.column_stack([lane2_x, lane2_y])
    lane2_labels = np.ones(15)  # Ground truth: lane 1
    
    # Combine points
    all_points = np.vstack([lane1_points, lane2_points])
    gt_labels = np.hstack([lane1_labels, lane2_labels])
    
    # Simulate embeddings (2D for visualization)
    # Good embeddings should separate the lanes
    lane1_embeddings = np.random.normal([0, 0], 0.3, (15, 2))
    lane2_embeddings = np.random.normal([2, 0], 0.3, (15, 2))
    embeddings = np.vstack([lane1_embeddings, lane2_embeddings])
    
    return all_points, embeddings, gt_labels

def analyze_clustering_parameters(points, embeddings, gt_labels, 
                                epsilon_range, min_samples_range):
    """
    Analyze different clustering parameter combinations.
    """
    results = []
    
    for eps in epsilon_range:
        for min_samples in min_samples_range:
            # Apply DBSCAN clustering
            clustering = DBSCAN(eps=eps, min_samples=min_samples)
            cluster_labels = clustering.fit_predict(embeddings)
            
            # Calculate metrics
            n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
            n_noise = list(cluster_labels).count(-1)
            
            # Calculate clustering accuracy (simplified)
            # Check if points from same GT lane are in same cluster
            correct_clustering = 0
            total_pairs = 0
            
            for i in range(len(gt_labels)):
                for j in range(i+1, len(gt_labels)):
                    total_pairs += 1
                    # Same GT lane should be in same cluster
                    if gt_labels[i] == gt_labels[j]:
                        if cluster_labels[i] == cluster_labels[j] and cluster_labels[i] != -1:
                            correct_clustering += 1
                    # Different GT lanes should be in different clusters
                    else:
                        if cluster_labels[i] != cluster_labels[j] or cluster_labels[i] == -1:
                            correct_clustering += 1
            
            accuracy = correct_clustering / total_pairs if total_pairs > 0 else 0
            
            # Check for over-clustering (bent lanes)
            bent_lane_risk = 0
            if n_clusters == 1 and n_noise < len(points) * 0.5:
                # All points in one cluster - high risk of bent lanes
                bent_lane_risk = 1.0
            elif n_clusters < 2:
                bent_lane_risk = 0.8
            
            results.append({
                'eps': eps,
                'min_samples': min_samples,
                'n_clusters': n_clusters,
                'n_noise': n_noise,
                'accuracy': accuracy,
                'bent_lane_risk': bent_lane_risk
            })
    
    return results

def visualize_clustering_analysis(points, embeddings, gt_labels, results):
    """
    Create comprehensive visualization of clustering analysis.
    """
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('BEV Lane Clustering Parameter Analysis', fontsize=16)
    
    # 1. Ground Truth Spatial
    ax = axes[0, 0]
    colors = ['red', 'blue']
    for i, label in enumerate(np.unique(gt_labels)):
        mask = gt_labels == label
        ax.scatter(points[mask, 0], points[mask, 1], 
                  c=colors[i], label=f'GT Lane {int(label)}', s=50, alpha=0.7)
    ax.set_title('Ground Truth Lanes (Spatial)')
    ax.set_xlabel('X (BEV coordinates)')
    ax.set_ylabel('Y (BEV coordinates)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. Ground Truth Embeddings
    ax = axes[0, 1]
    for i, label in enumerate(np.unique(gt_labels)):
        mask = gt_labels == label
        ax.scatter(embeddings[mask, 0], embeddings[mask, 1], 
                  c=colors[i], label=f'GT Lane {int(label)}', s=50, alpha=0.7)
    ax.set_title('Ground Truth Lanes (Embedding Space)')
    ax.set_xlabel('Embedding Dim 0')
    ax.set_ylabel('Embedding Dim 1')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 3. Parameter Analysis Heatmap - Accuracy
    eps_values = sorted(list(set([r['eps'] for r in results])))
    min_samples_values = sorted(list(set([r['min_samples'] for r in results])))
    
    accuracy_matrix = np.zeros((len(min_samples_values), len(eps_values)))
    for result in results:
        i = min_samples_values.index(result['min_samples'])
        j = eps_values.index(result['eps'])
        accuracy_matrix[i, j] = result['accuracy']
    
    ax = axes[0, 2]
    im = ax.imshow(accuracy_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    ax.set_title('Clustering Accuracy Heatmap')
    ax.set_xlabel('Epsilon')
    ax.set_ylabel('Min Samples')
    ax.set_xticks(range(len(eps_values)))
    ax.set_xticklabels([f'{eps:.1f}' for eps in eps_values])
    ax.set_yticks(range(len(min_samples_values)))
    ax.set_yticklabels(min_samples_values)
    plt.colorbar(im, ax=ax, label='Accuracy')
    
    # 4. Bent Lane Risk Heatmap
    risk_matrix = np.zeros((len(min_samples_values), len(eps_values)))
    for result in results:
        i = min_samples_values.index(result['min_samples'])
        j = eps_values.index(result['eps'])
        risk_matrix[i, j] = result['bent_lane_risk']
    
    ax = axes[1, 0]
    im = ax.imshow(risk_matrix, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=1)
    ax.set_title('Bent Lane Risk Heatmap')
    ax.set_xlabel('Epsilon')
    ax.set_ylabel('Min Samples')
    ax.set_xticks(range(len(eps_values)))
    ax.set_xticklabels([f'{eps:.1f}' for eps in eps_values])
    ax.set_yticks(range(len(min_samples_values)))
    ax.set_yticklabels(min_samples_values)
    plt.colorbar(im, ax=ax, label='Risk (0=Low, 1=High)')
    
    # 5. Number of Clusters
    cluster_matrix = np.zeros((len(min_samples_values), len(eps_values)))
    for result in results:
        i = min_samples_values.index(result['min_samples'])
        j = eps_values.index(result['eps'])
        cluster_matrix[i, j] = result['n_clusters']
    
    ax = axes[1, 1]
    im = ax.imshow(cluster_matrix, cmap='viridis', aspect='auto')
    ax.set_title('Number of Clusters')
    ax.set_xlabel('Epsilon')
    ax.set_ylabel('Min Samples')
    ax.set_xticks(range(len(eps_values)))
    ax.set_xticklabels([f'{eps:.1f}' for eps in eps_values])
    ax.set_yticks(range(len(min_samples_values)))
    ax.set_yticklabels(min_samples_values)
    plt.colorbar(im, ax=ax, label='Number of Clusters')
    
    # 6. Recommendations
    ax = axes[1, 2]
    ax.axis('off')
    
    # Find best parameters
    best_result = max(results, key=lambda x: x['accuracy'] - x['bent_lane_risk'])
    
    recommendations = f"""
    CLUSTERING PARAMETER RECOMMENDATIONS
    
    Current Analysis Results:
    • Best Parameters: eps={best_result['eps']:.1f}, min_samples={best_result['min_samples']}
    • Accuracy: {best_result['accuracy']:.2f}
    • Bent Lane Risk: {best_result['bent_lane_risk']:.2f}
    • Clusters Found: {best_result['n_clusters']}
    
    Key Insights:
    • Red zones in risk heatmap = High bent lane risk
    • Green zones in accuracy heatmap = Good separation
    • Target: 2 clusters for this scenario
    
    Recommended Fix:
    clustering_epsilon: {best_result['eps']:.1f}
    clustering_min_points: {best_result['min_samples']}
    
    Current Config Issues:
    • epsilon=1.2 is TOO HIGH (causes over-clustering)
    • Recommended epsilon ≤ 0.5 for BEV resolution
    """
    
    ax.text(0.05, 0.95, recommendations, transform=ax.transAxes, 
            fontsize=10, verticalalignment='top', fontfamily='monospace',
            bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    return fig

def analyze_config_file(config_path):
    """
    Analyze clustering parameters from a config file.
    """
    if not Path(config_path).exists():
        print(f"Config file not found: {config_path}")
        return None
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Extract clustering parameters
    try:
        head_config = config['model']['pts_bbox_head']
        epsilon = head_config.get('clustering_epsilon', 'Not found')
        min_points = head_config.get('clustering_min_points', 'Not found')
        
        print(f"\nConfig Analysis: {config_path}")
        print(f"Clustering Epsilon: {epsilon}")
        print(f"Clustering Min Points: {min_points}")
        
        # Provide recommendations
        if isinstance(epsilon, (int, float)):
            if epsilon > 1.0:
                print(f"⚠️  WARNING: Epsilon {epsilon} is too high for BEV resolution!")
                print(f"   Recommended: ≤ 0.5 for 0.25m grid resolution")
                print(f"   Risk: High probability of bent lanes")
            elif epsilon < 0.2:
                print(f"⚠️  WARNING: Epsilon {epsilon} might be too low")
                print(f"   Risk: Over-fragmentation, missing lane connections")
            else:
                print(f"✅ Epsilon {epsilon} is in reasonable range")
        
        return {'epsilon': epsilon, 'min_points': min_points}
        
    except KeyError as e:
        print(f"Error parsing config: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Analyze BEV Lane Clustering Parameters')
    parser.add_argument('--config', type=str, help='Path to config file to analyze')
    parser.add_argument('--output', type=str, default='clustering_analysis.png',
                       help='Output path for analysis plot')
    parser.add_argument('--eps-range', nargs=2, type=float, default=[0.2, 1.5],
                       help='Epsilon range to test [min, max]')
    parser.add_argument('--eps-steps', type=int, default=8,
                       help='Number of epsilon values to test')
    
    args = parser.parse_args()
    
    print("BEV Lane Detection Clustering Parameter Analysis")
    print("=" * 50)
    
    # Analyze config file if provided
    if args.config:
        analyze_config_file(args.config)
    
    # Generate simulation data
    print("\nGenerating simulation data...")
    points, embeddings, gt_labels = simulate_lane_points()
    
    # Define parameter ranges
    epsilon_range = np.linspace(args.eps_range[0], args.eps_range[1], args.eps_steps)
    min_samples_range = [2, 3, 4, 5, 6]
    
    print(f"Testing {len(epsilon_range)} epsilon values: {epsilon_range[0]:.1f} to {epsilon_range[-1]:.1f}")
    print(f"Testing {len(min_samples_range)} min_samples values: {min_samples_range}")
    
    # Run analysis
    print("\nRunning clustering analysis...")
    results = analyze_clustering_parameters(points, embeddings, gt_labels,
                                          epsilon_range, min_samples_range)
    
    # Create visualization
    print("\nGenerating visualization...")
    fig = visualize_clustering_analysis(points, embeddings, gt_labels, results)
    
    # Save results
    fig.savefig(args.output, dpi=150, bbox_inches='tight')
    print(f"\nAnalysis saved to: {args.output}")
    
    # Print summary
    best_result = max(results, key=lambda x: x['accuracy'] - x['bent_lane_risk'])
    print(f"\nRECOMMENDED PARAMETERS:")
    print(f"clustering_epsilon: {best_result['eps']:.1f}")
    print(f"clustering_min_points: {best_result['min_samples']}")
    print(f"Expected accuracy: {best_result['accuracy']:.2f}")
    print(f"Bent lane risk: {best_result['bent_lane_risk']:.2f}")
    
    plt.show()

if __name__ == '__main__':
    main()