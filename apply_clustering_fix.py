#!/usr/bin/env python3
"""
Quick Fix Script for BEV Lane Detection Clustering Parameters

This script applies the recommended clustering parameter fixes to resolve
the "bent lanes" issue by updating configuration files.
"""

import yaml
import argparse
import shutil
from pathlib import Path
from datetime import datetime

def backup_config(config_path):
    """
    Create a backup of the original config file.
    """
    backup_path = f"{config_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(config_path, backup_path)
    print(f"✅ Backup created: {backup_path}")
    return backup_path

def apply_clustering_fix(config_path, epsilon=0.4, min_points=4, 
                        enhance_embedding_loss=False, dry_run=False):
    """
    Apply clustering parameter fixes to a config file.
    """
    if not Path(config_path).exists():
        print(f"❌ Config file not found: {config_path}")
        return False
    
    # Load config
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    changes_made = []
    
    try:
        # Update clustering parameters
        head_config = config['model']['pts_bbox_head']
        
        # Store original values
        orig_epsilon = head_config.get('clustering_epsilon', 'Not set')
        orig_min_points = head_config.get('clustering_min_points', 'Not set')
        
        # Apply fixes
        head_config['clustering_epsilon'] = epsilon
        head_config['clustering_min_points'] = min_points
        
        changes_made.append(f"clustering_epsilon: {orig_epsilon} → {epsilon}")
        changes_made.append(f"clustering_min_points: {orig_min_points} → {min_points}")
        
        # Enhanced embedding loss (optional)
        if enhance_embedding_loss:
            if 'loss_embedding' in head_config:
                loss_config = head_config['loss_embedding']
                
                # Store original values
                orig_delta_v = loss_config.get('delta_v', 'Not set')
                orig_delta_d = loss_config.get('delta_d', 'Not set')
                orig_beta = loss_config.get('beta', 'Not set')
                orig_weight = loss_config.get('loss_weight', 'Not set')
                
                # Apply enhanced parameters
                loss_config['delta_v'] = 0.3  # Tighter intra-cluster
                loss_config['delta_d'] = 2.0  # Stronger inter-cluster
                loss_config['beta'] = 1.5     # Higher push weight
                loss_config['loss_weight'] = 1.5  # Higher overall weight
                
                changes_made.extend([
                    f"loss_embedding.delta_v: {orig_delta_v} → 0.3",
                    f"loss_embedding.delta_d: {orig_delta_d} → 2.0",
                    f"loss_embedding.beta: {orig_beta} → 1.5",
                    f"loss_embedding.loss_weight: {orig_weight} → 1.5"
                ])
            else:
                print("⚠️  Warning: loss_embedding not found in config")
        
        # Enable debug clustering if requested
        if 'debug_clustering' not in head_config:
            head_config['debug_clustering'] = False
            changes_made.append("debug_clustering: Added (set to False)")
        
        # Print changes
        print(f"\n📋 Proposed changes for {config_path}:")
        for change in changes_made:
            print(f"   • {change}")
        
        if dry_run:
            print("\n🔍 DRY RUN: No changes applied")
            return True
        
        # Create backup
        backup_path = backup_config(config_path)
        
        # Write updated config
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, sort_keys=False)
        
        print(f"\n✅ Successfully applied fixes to {config_path}")
        print(f"   Backup saved as: {backup_path}")
        
        return True
        
    except KeyError as e:
        print(f"❌ Error: Could not find required config section: {e}")
        print("   Make sure this is a valid BEV lane detection config file")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def validate_fix(config_path):
    """
    Validate that the fix was applied correctly.
    """
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    try:
        head_config = config['model']['pts_bbox_head']
        epsilon = head_config.get('clustering_epsilon')
        min_points = head_config.get('clustering_min_points')
        
        print(f"\n🔍 Validation Results:")
        print(f"   clustering_epsilon: {epsilon}")
        print(f"   clustering_min_points: {min_points}")
        
        # Check if parameters are in recommended ranges
        issues = []
        if epsilon and epsilon > 0.6:
            issues.append(f"⚠️  Epsilon {epsilon} might still be too high")
        if epsilon and epsilon < 0.2:
            issues.append(f"⚠️  Epsilon {epsilon} might be too low")
        if min_points and min_points < 2:
            issues.append(f"⚠️  Min points {min_points} might be too low")
        if min_points and min_points > 6:
            issues.append(f"⚠️  Min points {min_points} might be too high")
        
        if issues:
            print("\n⚠️  Potential Issues:")
            for issue in issues:
                print(f"   {issue}")
        else:
            print("\n✅ Parameters look good!")
        
        return len(issues) == 0
        
    except KeyError as e:
        print(f"❌ Validation failed: {e}")
        return False

def find_config_files(directory):
    """
    Find all YAML config files in a directory.
    """
    config_files = []
    for pattern in ['*.yaml', '*.yml']:
        config_files.extend(Path(directory).glob(f"**/{pattern}"))
    
    # Filter for likely lane detection configs
    lane_configs = []
    for config_file in config_files:
        if any(keyword in str(config_file).lower() for keyword in 
               ['lane', 'bev', 'bevfusion', 'clustering']):
            lane_configs.append(config_file)
    
    return lane_configs

def main():
    parser = argparse.ArgumentParser(
        description='Apply clustering parameter fixes for BEV lane detection',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Apply conservative fix to specific config
  python apply_clustering_fix.py --config config.yaml
  
  # Apply enhanced fix with embedding loss improvements
  python apply_clustering_fix.py --config config.yaml --enhance-embedding
  
  # Dry run to see what would be changed
  python apply_clustering_fix.py --config config.yaml --dry-run
  
  # Find and fix all lane detection configs
  python apply_clustering_fix.py --auto-find configs/
        """)
    
    parser.add_argument('--config', type=str, help='Path to config file to fix')
    parser.add_argument('--auto-find', type=str, help='Directory to search for lane configs')
    parser.add_argument('--epsilon', type=float, default=0.4,
                       help='Clustering epsilon value (default: 0.4)')
    parser.add_argument('--min-points', type=int, default=4,
                       help='Clustering min points (default: 4)')
    parser.add_argument('--enhance-embedding', action='store_true',
                       help='Also enhance embedding loss parameters')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show changes without applying them')
    parser.add_argument('--validate', action='store_true',
                       help='Validate config after applying fixes')
    
    args = parser.parse_args()
    
    print("BEV Lane Detection Clustering Fix Tool")
    print("=" * 40)
    
    if args.config:
        # Fix specific config file
        success = apply_clustering_fix(
            args.config, 
            epsilon=args.epsilon,
            min_points=args.min_points,
            enhance_embedding_loss=args.enhance_embedding,
            dry_run=args.dry_run
        )
        
        if success and args.validate and not args.dry_run:
            validate_fix(args.config)
            
    elif args.auto_find:
        # Find and fix all lane detection configs
        config_files = find_config_files(args.auto_find)
        
        if not config_files:
            print(f"❌ No lane detection config files found in {args.auto_find}")
            return
        
        print(f"\n🔍 Found {len(config_files)} potential config files:")
        for config_file in config_files:
            print(f"   • {config_file}")
        
        if not args.dry_run:
            confirm = input("\nApply fixes to all these files? [y/N]: ")
            if confirm.lower() != 'y':
                print("❌ Aborted")
                return
        
        success_count = 0
        for config_file in config_files:
            print(f"\n{'='*50}")
            success = apply_clustering_fix(
                str(config_file),
                epsilon=args.epsilon,
                min_points=args.min_points,
                enhance_embedding_loss=args.enhance_embedding,
                dry_run=args.dry_run
            )
            if success:
                success_count += 1
        
        print(f"\n📊 Summary: {success_count}/{len(config_files)} files processed successfully")
        
    else:
        print("❌ Please specify either --config or --auto-find")
        parser.print_help()
        return
    
    if not args.dry_run:
        print("\n🎯 Next Steps:")
        print("   1. Test the updated configuration")
        print("   2. Run debug visualization: python debug_clustering_visualization.py")
        print("   3. Monitor clustering quality in production")
        print("   4. Fine-tune parameters if needed")

if __name__ == '__main__':
    main()