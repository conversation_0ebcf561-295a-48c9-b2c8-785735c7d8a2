#!/usr/bin/env python3
"""
配置一致性清理脚本

功能：
1. 分析配置文件中的reduction参数使用情况
2. 检查cls_consistency_weight配置项的一致性
3. 识别未使用的配置项
4. 生成清理建议和报告

作者：AI算法工程师
日期：2024年
"""

import os
import re
import yaml
import json
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple, Any

class ConfigConsistencyAnalyzer:
    """配置一致性分析器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.config_dir = self.project_root / "configs"
        self.code_dir = self.project_root / "mmdet3d" / "models" / "heads" / "lane"
        
        # 分析结果存储
        self.reduction_usage = defaultdict(list)
        self.cls_consistency_usage = defaultdict(list)
        self.config_inconsistencies = []
        self.unused_configs = []
        
    def analyze_reduction_usage(self) -> Dict[str, Any]:
        """分析reduction参数的使用情况"""
        print("🔍 分析reduction参数使用情况...")
        
        # 在配置文件中查找reduction
        config_files = list(self.config_dir.rglob("*.yaml"))
        reduction_patterns = []
        
        for config_file in config_files:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找reduction配置
                reduction_matches = re.findall(r'reduction:\s*(\w+)', content)
                for match in reduction_matches:
                    self.reduction_usage[match].append(str(config_file.relative_to(self.project_root)))
                    
            except Exception as e:
                print(f"⚠️  读取配置文件失败: {config_file} - {e}")
                
        # 在代码中查找reduction使用
        code_files = list(self.code_dir.rglob("*.py"))
        code_reduction_usage = defaultdict(list)
        
        for code_file in code_files:
            try:
                with open(code_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找硬编码的reduction
                hardcoded_matches = re.findall(r"reduction=['\"]([^'\"]+)['\"]", content)
                for match in hardcoded_matches:
                    code_reduction_usage[match].append(str(code_file.relative_to(self.project_root)))
                    
            except Exception as e:
                print(f"⚠️  读取代码文件失败: {code_file} - {e}")
                
        return {
            'config_usage': dict(self.reduction_usage),
            'code_usage': dict(code_reduction_usage)
        }
    
    def analyze_cls_consistency_weight(self) -> Dict[str, Any]:
        """分析cls_consistency_weight参数的使用情况"""
        print("🔍 分析cls_consistency_weight参数使用情况...")
        
        # 在配置文件中查找cls_consistency_weight
        config_files = list(self.config_dir.rglob("*.yaml"))
        config_usage = []
        
        for config_file in config_files:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if 'cls_consistency_weight' in content:
                    # 提取具体的值
                    matches = re.findall(r'cls_consistency_weight:\s*([\d\.]+)', content)
                    for match in matches:
                        config_usage.append({
                            'file': str(config_file.relative_to(self.project_root)),
                            'value': float(match)
                        })
                        
            except Exception as e:
                print(f"⚠️  读取配置文件失败: {config_file} - {e}")
        
        # 在代码中查找cls_consistency_weight使用
        code_files = list(self.code_dir.rglob("*.py"))
        code_usage = []
        
        for code_file in code_files:
            try:
                with open(code_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找参数定义和使用
                if 'cls_consistency_weight' in content:
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if 'cls_consistency_weight' in line:
                            code_usage.append({
                                'file': str(code_file.relative_to(self.project_root)),
                                'line': i,
                                'content': line.strip()
                            })
                            
            except Exception as e:
                print(f"⚠️  读取代码文件失败: {code_file} - {e}")
        
        return {
            'config_usage': config_usage,
            'code_usage': code_usage
        }
    
    def detect_inconsistencies(self, reduction_analysis: Dict, cls_analysis: Dict) -> List[Dict[str, Any]]:
        """检测配置不一致性"""
        print("🔍 检测配置不一致性...")
        
        inconsistencies = []
        
        # 检查reduction值的一致性
        reduction_values = Counter()
        for value, files in reduction_analysis['config_usage'].items():
            reduction_values[value] += len(files)
            
        if len(reduction_values) > 1:
            inconsistencies.append({
                'type': 'reduction_inconsistency',
                'description': 'reduction参数在不同配置文件中使用了不同的值',
                'details': dict(reduction_values),
                'severity': 'medium'
            })
        
        # 检查cls_consistency_weight值的一致性
        cls_values = set()
        for usage in cls_analysis['config_usage']:
            cls_values.add(usage['value'])
            
        if len(cls_values) > 1:
            inconsistencies.append({
                'type': 'cls_consistency_weight_inconsistency',
                'description': 'cls_consistency_weight参数在不同地方使用了不同的值',
                'details': list(cls_values),
                'severity': 'high'
            })
        
        # 检查配置文件中是否缺少cls_consistency_weight定义
        if not cls_analysis['config_usage']:
            inconsistencies.append({
                'type': 'missing_config',
                'description': 'cls_consistency_weight在配置文件中未定义，仅在代码中硬编码',
                'details': 'cls_consistency_weight=0.1 应该在配置文件中定义',
                'severity': 'high'
            })
        
        return inconsistencies
    
    def generate_cleanup_suggestions(self, reduction_analysis: Dict, cls_analysis: Dict, inconsistencies: List) -> List[Dict[str, Any]]:
        """生成清理建议"""
        print("💡 生成清理建议...")
        
        suggestions = []
        
        # 建议1：统一reduction参数
        if 'mean' in reduction_analysis['config_usage']:
            suggestions.append({
                'type': 'standardize_reduction',
                'title': '统一reduction参数为mean',
                'description': '所有损失函数的reduction参数建议统一使用"mean"',
                'action': 'review_and_standardize',
                'priority': 'medium'
            })
        
        # 建议2：将cls_consistency_weight移到配置文件
        if cls_analysis['code_usage'] and not cls_analysis['config_usage']:
            suggestions.append({
                'type': 'move_to_config',
                'title': '将cls_consistency_weight移到配置文件',
                'description': 'cls_consistency_weight应该在配置文件中定义，而不是硬编码在代码中',
                'action': 'add_to_config_file',
                'priority': 'high',
                'implementation': {
                    'add_to_config': 'cls_consistency_weight: 0.1',
                    'modify_code': '使用self.cls_consistency_weight = cls_consistency_weight'
                }
            })
        
        # 建议3：清理未使用的配置项
        suggestions.append({
            'type': 'cleanup_unused',
            'title': '清理未使用的配置项',
            'description': '移除配置文件中未被代码使用的配置项',
            'action': 'review_and_remove',
            'priority': 'low'
        })
        
        return suggestions
    
    def run_analysis(self) -> Dict[str, Any]:
        """运行完整的配置一致性分析"""
        print("🚀 开始配置一致性分析...")
        
        # 执行各项分析
        reduction_analysis = self.analyze_reduction_usage()
        cls_analysis = self.analyze_cls_consistency_weight()
        inconsistencies = self.detect_inconsistencies(reduction_analysis, cls_analysis)
        suggestions = self.generate_cleanup_suggestions(reduction_analysis, cls_analysis, inconsistencies)
        
        # 汇总结果
        results = {
            'analysis_summary': {
                'total_config_files': len(list(self.config_dir.rglob("*.yaml"))),
                'total_code_files': len(list(self.code_dir.rglob("*.py"))),
                'reduction_variants': len(reduction_analysis['config_usage']),
                'cls_consistency_locations': len(cls_analysis['code_usage']),
                'inconsistencies_found': len(inconsistencies),
                'suggestions_generated': len(suggestions)
            },
            'reduction_analysis': reduction_analysis,
            'cls_consistency_analysis': cls_analysis,
            'inconsistencies': inconsistencies,
            'cleanup_suggestions': suggestions
        }
        
        return results
    
    def save_report(self, results: Dict[str, Any], output_file: str = "CONFIG_CONSISTENCY_REPORT.md"):
        """保存分析报告"""
        print(f"📝 保存分析报告到 {output_file}...")
        
        report_content = f"""# 配置一致性分析报告

## 📊 分析概要

- **配置文件总数**: {results['analysis_summary']['total_config_files']}
- **代码文件总数**: {results['analysis_summary']['total_code_files']}
- **reduction参数变体数**: {results['analysis_summary']['reduction_variants']}
- **cls_consistency_weight使用位置**: {results['analysis_summary']['cls_consistency_locations']}
- **发现的不一致性**: {results['analysis_summary']['inconsistencies_found']}
- **生成的建议**: {results['analysis_summary']['suggestions_generated']}

## 🔍 Reduction参数分析

### 配置文件中的使用情况
"""
        
        for value, files in results['reduction_analysis']['config_usage'].items():
            report_content += f"\n**{value}**: {len(files)} 个文件\n"
            for file in files[:5]:  # 只显示前5个文件
                report_content += f"- {file}\n"
            if len(files) > 5:
                report_content += f"- ... 还有 {len(files) - 5} 个文件\n"
        
        report_content += "\n### 代码中的硬编码使用\n"
        for value, files in results['reduction_analysis']['code_usage'].items():
            report_content += f"\n**{value}**: {len(files)} 个文件\n"
            for file in files:
                report_content += f"- {file}\n"
        
        report_content += "\n## 🎯 cls_consistency_weight分析\n\n### 配置文件中的定义\n"
        if results['cls_consistency_analysis']['config_usage']:
            for usage in results['cls_consistency_analysis']['config_usage']:
                report_content += f"- {usage['file']}: {usage['value']}\n"
        else:
            report_content += "❌ **未在配置文件中找到定义**\n"
        
        report_content += "\n### 代码中的使用\n"
        for usage in results['cls_consistency_analysis']['code_usage']:
            report_content += f"- {usage['file']}:{usage['line']} - `{usage['content']}`\n"
        
        report_content += "\n## ⚠️ 发现的不一致性\n"
        if results['inconsistencies']:
            for i, issue in enumerate(results['inconsistencies'], 1):
                report_content += f"\n### {i}. {issue['description']}\n"
                report_content += f"**严重程度**: {issue['severity']}\n"
                report_content += f"**详情**: {issue['details']}\n"
        else:
            report_content += "\n✅ **未发现配置不一致性**\n"
        
        report_content += "\n## 💡 清理建议\n"
        for i, suggestion in enumerate(results['cleanup_suggestions'], 1):
            report_content += f"\n### {i}. {suggestion['title']}\n"
            report_content += f"**优先级**: {suggestion['priority']}\n"
            report_content += f"**描述**: {suggestion['description']}\n"
            report_content += f"**建议操作**: {suggestion['action']}\n"
            
            if 'implementation' in suggestion:
                report_content += "\n**实施方案**:\n"
                for key, value in suggestion['implementation'].items():
                    report_content += f"- {key}: `{value}`\n"
        
        report_content += f"\n## 📅 报告生成时间\n\n{__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        # 保存报告
        output_path = self.project_root / output_file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ 报告已保存到: {output_path}")

def main():
    """主函数"""
    project_root = "/pcpt/pcpt/project/liuyibo/multimodal_bevfusion"
    
    print("🔧 配置一致性清理工具")
    print("=" * 50)
    
    # 创建分析器
    analyzer = ConfigConsistencyAnalyzer(project_root)
    
    # 运行分析
    results = analyzer.run_analysis()
    
    # 保存报告
    analyzer.save_report(results)
    
    # 打印摘要
    print("\n📋 分析完成摘要:")
    print(f"- 发现 {results['analysis_summary']['inconsistencies_found']} 个不一致性")
    print(f"- 生成 {results['analysis_summary']['suggestions_generated']} 个清理建议")
    print(f"- reduction参数有 {results['analysis_summary']['reduction_variants']} 种变体")
    
    if results['inconsistencies']:
        print("\n⚠️  主要问题:")
        for issue in results['inconsistencies']:
            print(f"  - {issue['description']} ({issue['severity']})")
    
    print("\n✅ 配置一致性分析完成!")

if __name__ == "__main__":
    main()