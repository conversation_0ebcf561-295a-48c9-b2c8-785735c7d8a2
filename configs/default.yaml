seed: 0
deterministic: false

checkpoint_config:
  interval: 1
  max_keep_ckpts: 10

log_config:
  interval: 50
  hooks:
    -
      type: TextLoggerHook
    -
      type: TensorboardLoggerHook

load_from: null
resume_from: null

cudnn_benchmark: false
# fp16:
#   loss_scale: 
#     growth_interval: 2000

max_epochs: 6
runner:
  type: CustomEpochBasedRunner
  max_epochs: ${max_epochs}

optimizer:
  type: AdamW
  lr: 2.0e-4
  weight_decay: 0.01

optimizer_config:
  grad_clip:
    max_norm: 35
    norm_type: 2

lr_config:
  policy: CosineAnnealing
  warmup: linear
  warmup_iters: 500
  warmup_ratio: 0.33333333
  min_lr_ratio: 1.0e-3

momentum_config:
  policy: cyclic

voxel_size: [0.075, 0.075, 0.2]
point_cloud_range: [-54.0, -54.0, -5.0, 54.0, 54.0, 3.0]

model:
  type: BEVFusion
  encoders:
    camera:
      neck:
        type: GeneralizedLSSFPN
        in_channels: [192, 384, 768] #每个尺度的输入通道数,可以看出使用三个尺度
        out_channels: 256 #每个尺度的输出通道数
        start_level: 0
        num_outs: 3 #输出尺度的数量
        norm_cfg:
          type: BN2d
          requires_grad: true
        act_cfg:
          type: ReLU
          inplace: true
        upsample_cfg:
          mode: bilinear
          align_corners: false
      vtransform:
        type: DepthLSSTransform
        in_channels: 256
        out_channels: 80
        image_size: ${image_size} #[256, 704]
        feature_size: ${[image_size[0] // 8, image_size[1] // 8]} #[32,88]其实有三个尺度的feature_map,分别是8/16/32降采样
        xbound: [-54.0, 54.0, 0.3]
        ybound: [-54.0, 54.0, 0.3]
        zbound: [-10.0, 10.0, 20.0]
        dbound: [1.0, 60.0, 0.5]
        downsample: 2
    lidar:
      voxelize:
        max_num_points: 10
        point_cloud_range: ${point_cloud_range}
        voxel_size: ${voxel_size}
        max_voxels: [120000, 160000]
      backbone:
        type: SparseEncoder
        in_channels: 5
        sparse_shape: [1440, 1440, 41]
        output_channels: 128
        order:
          - conv
          - norm
          - act
        encoder_channels:
          - [16, 16, 32]
          - [32, 32, 64]
          - [64, 64, 128]
          - [128, 128]
        encoder_paddings:
          - [0, 0, 1]
          - [0, 0, 1]
          - [0, 0, [1, 1, 0]]
          - [0, 0]
        block_type: basicblock
  fuser:
    type: ConvFuser
    in_channels: [80, 256]
    out_channels: 256
  decoder:
    backbone:
      type: SECOND
      in_channels: 256
      out_channels: [128, 256]
      layer_nums: [5, 5]
      layer_strides: [1, 2]
      norm_cfg:
        type: BN
        eps: 1.0e-3
        momentum: 0.01
      conv_cfg:
        type: Conv2d
        bias: false
    neck:
      type: SECONDFPN
      in_channels: [128, 256]
      out_channels: [256, 256]
      upsample_strides: [1, 2]
      norm_cfg:
        type: BN
        eps: 1.0e-3
        momentum: 0.01
      upsample_cfg:
        type: deconv
        bias: false
      use_conv_for_no_stride: true
  heads:
    map: null
    object:
      type: TransFusionHead
      num_proposals: 200
      auxiliary: true
      in_channels: 512
      hidden_channel: 128
      num_classes: 10
      num_decoder_layers: 1
      num_heads: 8
      nms_kernel_size: 3
      ffn_channel: 256
      dropout: 0.1
      bn_momentum: 0.1
      activation: relu
      train_cfg:
        dataset: nuScenes
        point_cloud_range: ${point_cloud_range}
        grid_size: [1440, 1440, 41]
        voxel_size: ${voxel_size}
        out_size_factor: 8
        gaussian_overlap: 0.1
        min_radius: 2
        pos_weight: -1
        code_weights: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
        assigner:
          type: HungarianAssigner3D
          iou_calculator:
            type: BboxOverlaps3D
            coordinate: lidar
          cls_cost:
            type: FocalLossCost
            gamma: 2.0
            alpha: 0.25
            weight: 0.15
          reg_cost:
            type: BBoxBEVL1Cost
            weight: 0.25
          iou_cost:
            type: IoU3DCost
            weight: 0.25
      test_cfg:
        dataset: nuScenes
        grid_size: [1440, 1440, 41]
        out_size_factor: 8
        voxel_size: ${voxel_size[:2]}
        pc_range: ${point_cloud_range[:2]}
        nms_type: null
      common_heads:
        center: [2, 2]
        height: [1, 2]
        dim: [3, 2]
        # rot: [2, 2]
        # vel: [2, 2]
      bbox_coder:
        type: TransFusionBBoxCoder
        pc_range: ${point_cloud_range[:2]}
        post_center_range: ${point_cloud_range}
        score_threshold: 0.1
        out_size_factor: 8
        voxel_size: ${voxel_size[:2]}
        code_size: 8
      loss_cls: 
        type: FocalLoss
        use_sigmoid: true
        gamma: 2.0
        alpha: 0.25
        reduction: mean
        loss_weight: 1.0
      loss_heatmap:
        type: GaussianFocalLoss
        reduction: mean
        loss_weight: 1.0
      loss_bbox:
        type: L1Loss
        reduction: mean
        loss_weight: 0.25

dist_params:
  backend: nccl
