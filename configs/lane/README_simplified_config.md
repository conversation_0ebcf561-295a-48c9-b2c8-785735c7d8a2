# Simplified 3D Lane Detection Configuration

## Overview

This directory contains a simplified configuration for 3D lane detection based on the standard BEVFusion framework. The main configuration file `config_3d_lane_detection_task_based_bevfusion.yaml` removes the dense depth map processing and uses the standard BEVFusion components.

## Key Changes from Dense Depth Configuration

1. **Removed Dense Depth Map Processing**:
   - No `LoadDenseDepthMapFromFile` in data pipeline
   - No `depth_map` and `depth_map_valid` in data collection
   - Uses standard `DepthLSSTransform` instead of `DepthLSSTransformWithDepthMap`

2. **Simplified Data Augmentation** (for debugging):
   - No random resize (resize_lim = [1.0, 1.0])
   - No random crop (bot_pct_lim = [0.0, 0.0])
   - No random rotation (rot_lim = [0.0, 0.0])
   - No random flip (rand_flip = false)
   - No global transformation

3. **Debugging-Friendly Settings**:
   - Single GPU training (samples_per_gpu: 1)
   - No data loading workers (workers_per_gpu: 0)
   - Lower learning rate (0.0001)
   - Shorter warmup (100 iterations)
   - Logging every iteration
   - Deterministic mode enabled
   - Embeddings disabled for initial debugging

## Configuration Structure

### Model Architecture
```
BEVFusionForLanes
├── Camera Encoder
│   ├── Backbone: GPUNet-1 (optimized for NVIDIA Orin)
│   ├── Neck: GeneralizedLSSFPN
│   └── VTransform: DepthLSSTransform (standard)
├── LiDAR Encoder
│   ├── Voxelization
│   └── Backbone: SparseEncoder
├── Fuser: ConvFuser
├── Decoder
│   ├── Backbone: SECOND
│   └── Neck: SECONDFPN
└── Heads
    └── Lane: BEVLaneHeatmapHead
```

### Data Pipeline
1. Load multi-view images (6 cameras)
2. Load LiDAR points
3. Load lane annotations
4. Apply minimal augmentations
5. Filter points by range
6. Normalize images
7. Generate BEV lane heatmap targets
8. Format and collect data

## Usage

### Testing Configuration
```bash
python test_simplified_config.py
```

### Training
```bash
python tools/train.py configs/lane/config_3d_lane_detection_task_based_bevfusion.yaml
```

### Single Sample Debugging
```bash
python debug_training_process.py --config configs/lane/config_3d_lane_detection_task_based_bevfusion.yaml
```

## Dataset Requirements

The configuration expects:
- Dataset root: `/turbo/pcpt/project/liuyibo/dataset_demo_path/lane_3d_dataset_mini`
- Annotation files: `train_annotations.pkl`, `val_annotations.pkl`, `test_info.pkl`
- 6 cameras: '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back'
- 12 lane classes (various line types)

## Next Steps

Once the basic configuration is working:
1. Re-enable data augmentations gradually
2. Enable instance embeddings for lane grouping
3. Increase batch size and enable multi-GPU training
4. Fine-tune hyperparameters
5. Consider adding dense depth maps back if needed

## Troubleshooting

If you encounter issues:
1. Check that all required modules are registered in the registry
2. Verify dataset paths and annotation files exist
3. Ensure CUDA is available for GPU operations
4. Check that the pretrained GPUNet weights exist at the specified path
5. Monitor GPU memory usage - reduce batch size if needed 