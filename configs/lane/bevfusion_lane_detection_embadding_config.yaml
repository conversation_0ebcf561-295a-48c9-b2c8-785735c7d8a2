# === BEVFusion Single-Task 3D Lane Detection Configuration ===
# Aligned with bevfusion_3d_detection_segmentation_config.yaml architecture

dataset_type: Custom3DLaneDataset
dataset_root: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames
cam_list: ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
reduce_beams: 32
load_dim: 4
use_dim: 4
load_augmented: null

image_size: [256, 704]
max_epochs: 30

fp16:
  loss_scale:
    growth_interval: 2000

point_cloud_range: [-81.6, -48, -1, 97.6, 48, 3.0]
voxel_size: [0.1, 0.1, 0.2]
camera_num: 7
augment2d:
  resize_train: [[0.184, 0.187], [0.38, 0.42], [0.38, 0.42], [0.38, 0.42], [0.38, 0.55], [0.38, 0.42], [0.38, 0.42]]
  resize_test: [[0.185, 0.185], [0.4, 0.4], [0.4, 0.4], [0.4, 0.4], [0.47, 0.47], [0.4, 0.4], [0.4, 0.4]]
  bot_pct_lim_train: [[0.0, 0.0], [0.12, 0.15], [0.2, 0.25], [0.2, 0.25], [0.15, 0.20], [0.2, 0.25], [0.2, 0.25]]
  bot_pct_lim_test: [[0.0, 0.0], [0.14, 0.14], [0.22, 0.22], [0.22, 0.22], [0.17, 0.17], [0.22, 0.22], [0.22, 0.22]]
lane_classes:
  - background
  - white-solid
  - white-dashed
  - white-double-solid
  - white-solid-dashed
  - white-dashed-solid
  - white-double-dashed
  - yellow-solid
  - yellow-dashed
  - yellow-double-solid
  - yellow-solid-dashed
  - left-yellow-right-white-double-solid
  - road-edge-dashed

input_modality:
  use_lidar: true
  use_camera: true
  use_radar: false
  use_map: false
  use_external: false

train_pipeline:
  - type: LoadMultiViewImageFromFiles
    to_float32: true
  - type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams}
    with_lidarid: False
  - type: LoadLaneAnnotations3D
    with_lane_3d: true
    with_lane_label_3d: true
  - type: ImageAug3D
    final_dim: ${image_size}
    camera_num: ${camera_num}
    resize_lim: ${augment2d.resize_train}
    bot_pct_lim: ${augment2d.bot_pct_lim_train}
    rot_lim: [0.0, 0.0]
    rand_flip: false
    is_train: true
  - type: GlobalRotScaleTrans
    resize_lim: [1.0, 1.0]
    rot_lim: [0.0, 0.0]
    trans_lim: 0.0
    is_train: true
  
  - type: PointsRangeFilter
    point_cloud_range: ${point_cloud_range}
  
  - type: ImageNormalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
  
  - type: GenerateBEVLaneHeatmapTargets
    point_cloud_range: ${point_cloud_range}
    grid_conf:
      xbound: [-81.6, 97.6, 0.4]  # CRITICAL: Match BEV head resolution
      ybound: [-48.0, 48.0, 0.4]  # CRITICAL: Match BEV head resolution
    lane_classes: ${lane_classes}
    enable_visualization: false
    visualization_output_dir: ${dataset_root + "/lane_visualization"}
    target_config:
      gaussian_sigma: 1.0
      heatmap_radius: 2
      cls_radius: 1
      reg_radius: 0
      max_lanes: 40
      num_points: 120
      generate_instance_ids: true
      vis_threshold: 0.5
  
  - type: DefaultFormatBundle3D
    classes: ${lane_classes}
  
  - type: Collect3D
    keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
      - lane_targets
    meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix

test_pipeline:
  - type: LoadMultiViewImageFromFiles
    to_float32: true
  - type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams}
    with_lidarid: False
  - type: LoadLaneAnnotations3D
    with_lane_3d: true
    with_lane_label_3d: true
  - type: ImageAug3D
    final_dim: ${image_size}
    camera_num: ${camera_num}
    resize_lim: ${augment2d.resize_test}
    bot_pct_lim: ${augment2d.bot_pct_lim_test}
    rot_lim: [0.0, 0.0]
    rand_flip: false
    is_train: false
  - type: GlobalRotScaleTrans
    resize_lim: [1.0, 1.0]
    rot_lim: [0.0, 0.0]
    trans_lim: 0.0
    is_train: false
  
  - type: PointsRangeFilter
    point_cloud_range: ${point_cloud_range}
  
  - type: ImageNormalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
  
  - type: GenerateBEVLaneHeatmapTargets
    point_cloud_range: ${point_cloud_range}
    grid_conf:
      xbound: [-81.6, 97.6, 0.4]  # CRITICAL: Match BEV head resolution
      ybound: [-48.0, 48.0, 0.4]  # CRITICAL: Match BEV head resolution
    lane_classes: ${lane_classes}
    enable_visualization: false
    visualization_output_dir: ${dataset_root + "/lane_visualization"}
    target_config:
      gaussian_sigma: 1.0
      heatmap_radius: 2
      cls_radius: 1
      reg_radius: 0
      max_lanes: 40
      num_points: 120
      generate_instance_ids: true
      vis_threshold: 0.5
  
  - type: DefaultFormatBundle3D
    classes: ${lane_classes}
  
  - type: Collect3D
    keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
    meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix

data:
  samples_per_gpu: 1
  workers_per_gpu: 4
  train:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/train_annotations_skip30_without_depth_maps.pkl"}
    pipeline: ${train_pipeline}
    modality: ${input_modality}
    test_mode: false
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}
  val:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/val_annotations_skip30_without_depth_maps.pkl"}
    pipeline: ${test_pipeline}
    modality: ${input_modality}
    test_mode: false
    box_type_3d: LiDAR
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}
  test:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/val_annotations_100frames.pkl"}
    pipeline: ${test_pipeline}
    modality: ${input_modality}
    test_mode: true
    box_type_3d: LiDAR
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}

evaluation:
  interval: 1
  pipeline: ${test_pipeline}
  metric: 'OpenLane'
  eval_params:
    metric_list: ['f1_score', 'precision', 'recall', 'x_error_near', 'x_error_far', 'z_error']
    iou_threshold: 0.5
model:
  type: BEVFusionForLanes
  encoders:
    camera:
      backbone:
        type: GPUNetWrapper
        model_type: GPUNet-1
        precision: fp16
        img_height: ${image_size[0]}
        img_width: ${image_size[1]}
        out_indices: [5, 10, 14]
        latency: 0.85ms
        gpuType: orin
        batch: 1
        pretrained: /configs/batch1/GV100/0.85ms.pth.tar
      neck:
        in_channels: [96, 288, 448]
        out_channels: 256
      vtransform:
        type: DepthLSSTransform
        in_channels: 256
        out_channels: 80
        xbound: [-81.6, 97.6, 0.4]
        ybound: [-48.0, 48.0, 0.4]
        zbound: [-1.0, 3.0, 4.0]
        dbound: [1.0, 90.0, 0.5]
    lidar:
      voxelize:
        point_cloud_range: ${point_cloud_range}
        voxel_size: ${voxel_size}
        max_voxels: [230000, 280000]
      backbone:
        type: SparseEncoder
        in_channels: ${use_dim}
        sparse_shape: [1792, 960, 21]
        output_channels: 128
        encoder_paddings:
          - [0, 0, 1]
          - [0, 0, [1, 1, 0]]
          - [0, 0, 1]
          - [0, 0]
        encoder_strides:
          - [0, 0, 2]
          - [0, 0, 2]
          - [0, 0, [2,2,1]]
          - [0, 0]

  # fuser:
  #   type: ConvFuser
  #   in_channels: [80, 128]
  #   out_channels: 256
  decoder:
    backbone:
      type: SECOND
      in_channels: 256
      out_channels: [128, 64, 128, 256]
      layer_nums: [1, 2, 3, 1]
      layer_strides: [1, 1, 2, 2]
    neck:
      type: SECONDFPN
      in_channels: [128, 64, 128, 256]
      out_channels: [128, 128, 128, 128]
      out_channel: 128
      upsample_strides: [1, 1, 2, 2]
      use_conv_for_no_stride: true

  heads:
    lane:
      type: BEVLaneHeatmapHead
      in_channels: 256
      feat_channels: 64
      num_classes: ${len(lane_classes)}
      task_area_scope:
        x_range: [0.0, 60.0]
        y_range: [-15.0, 15.0]
        z_range: [-1.0, 3.0]
        enable_crop: false
        crop_method: "slice"
      
      grid_conf:
        xbound: [-81.6, 97.6, 0.4]
        ybound: [-48.0, 48.0, 0.4]
      
      row_points: 120
      z_range: [-1.0, 3.0]
      max_lanes: 40
      hm_thres: 0.25
      nms_kernel_size: 5
      use_sigmoid: true
      use_embedding: true
      embedding_dim: 16
      group_lanes: true
      clustering_method: 'dbscan'
      clustering_epsilon: 0.4
      clustering_min_points: 4
      lane_group_min_distance: 1.0
      loss_heatmap:
        type: GaussianFocalLoss
        alpha: 2.0
        gamma: 4.0
        reduction: mean
        loss_weight: 8.0
      loss_offset:
        type: L1Loss
        reduction: mean
        loss_weight: 6.0
      loss_z:
        type: L1Loss
        reduction: mean
        loss_weight: 2.0
      loss_cls:
        type: CrossEntropyLoss
        use_sigmoid: false
        reduction: mean
        loss_weight: 0.5
      loss_embedding:
        type: DiscriminativeLoss
        delta_v: 0.3
        delta_d: 2.0
        norm: 2
        alpha: 1.0
        beta: 1.5
        gamma: 0.001
        reduction: mean
        loss_weight: 1.0

optimizer:
  type: AdamW
  lr: 0.0001
  weight_decay: 0.01

lr_config:
  policy: CosineAnnealing
  warmup: linear
  warmup_iters: 500
  warmup_ratio: 0.33333333
  min_lr_ratio: 1.0e-4
runner:
  type: EpochBasedRunner
  max_epochs: ${max_epochs}

checkpoint_config:
  interval: 5
  max_keep_ckpts: 10

# === Distributed Training Support ===
find_unused_parameters: false

# === Load Pretrained Model (optional) ===
# load_from: /path/to/pretrained/bevfusion/model.pth