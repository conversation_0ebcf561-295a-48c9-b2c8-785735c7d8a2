# Debug configuration for clustering visualization
# Based on config_3d_lane_detection_task_based_bevfusion_embadding_new_params.yaml
# with debug clustering enabled and conservative parameters

_base_: ['config_3d_lane_detection_task_based_bevfusion_embadding_new_params.yaml']

# Override model configuration for debugging
model:
  pts_bbox_head:
    type: BEVLaneHeatmapHead
    in_channels: 256
    feat_channels: 256
    num_classes: 13
    
    # Grid configuration
    grid_conf:
      xbound: [-25.0, 25.0, 0.25]  # [min, max, resolution] in meters
      ybound: [3.0, 103.0, 0.25]   # [min, max, resolution] in meters
    
    # Detection parameters
    max_lanes: 20
    hm_thres: 0.3  # Lower threshold to get more points for debugging
    nms_kernel_size: 5
    use_sigmoid: true
    
    # === DEBUG CLUSTERING CONFIGURATION ===
    use_embedding: true
    embedding_dim: 16
    group_lanes: true
    clustering_method: 'dbscan'
    debug_clustering: true  # ENABLE DEBUG VISUALIZATION
    
    # CONSERVATIVE CLUSTERING PARAMETERS (to reduce over-clustering)
    clustering_epsilon: 0.4  # REDUCED from 1.2 - more strict clustering
    clustering_min_points: 4  # Slightly reduced for more sensitivity
    lane_group_min_distance: 1.0  # Geometric fallback distance
    
    # Loss configuration
    loss_heatmap:
      type: GaussianFocalLoss
      alpha: 2.0
      gamma: 4.0
      reduction: mean
      loss_weight: 2.0
    
    loss_offset:
      type: L1Loss
      reduction: mean
      loss_weight: 1.5
    
    loss_z:
      type: L1Loss
      reduction: mean
      loss_weight: 1.5
    
    loss_cls:
      type: CrossEntropyLoss
      use_sigmoid: false
      reduction: mean
      loss_weight: 1.0
    
    # ENHANCED EMBEDDING LOSS for better discrimination
    loss_embedding:
      type: DiscriminativeLoss
      delta_v: 0.3    # REDUCED - tighter intra-cluster constraint
      delta_d: 2.0    # INCREASED - stronger inter-cluster separation
      alpha: 1.0      # Pull loss weight
      beta: 1.5       # INCREASED push loss weight
      gamma: 0.001    # Regularization weight
      loss_weight: 1.5  # INCREASED overall embedding loss weight

# Test configuration for debugging
test_cfg:
  use_rotate_nms: false
  nms_across_levels: false
  nms_pre: 1000
  nms_thr: 0.8
  score_thr: 0.1  # Lower score threshold for debugging
  min_bbox_size: 0
  max_per_img: 20

# Data configuration - use smaller batch for debugging
data:
  samples_per_gpu: 1  # Single sample for easier debugging
  workers_per_gpu: 1
  
  test:
    type: Custom3DDataset
    data_root: data/openlane/
    ann_file: data/openlane/openlane_infos_test.pkl
    pipeline:
      - type: LoadMultiViewImageFromFiles
        to_float32: true
      - type: LoadAnnotations3D
        with_bbox_3d: false
        with_label_3d: false
        with_lane_3d: true
      - type: MultiScaleFlipAug3D
        img_scale: [1920, 1280]
        pts_scale_ratio: 1
        flip: false
        transforms:
          - type: ResizeCropFlipImage
            data_aug_conf:
              resize_lim: [1.0, 1.0]
              final_dim: [640, 1600]
              rot_lim: [0.0, 0.0]
              H: 1280
              W: 1920
              rand_flip: false
              bot_pct_lim: [0.0, 0.0]
              cams: ['ring_front_center', 'ring_front_left', 'ring_front_right', 
                     'ring_side_left', 'ring_side_right', 'ring_rear_left', 'ring_rear_right']
              Ncams: 7
          - type: GlobalRotScaleTrans
            rot_range: [0, 0]
            scale_ratio_range: [1., 1.]
            translation_std: [0, 0, 0]
          - type: NormalizeMultiviewImage
            mean: [123.675, 116.28, 103.53]
            std: [58.395, 57.12, 57.375]
            to_rgb: true
          - type: PadMultiViewImage
            size_divisor: 32
          - type: DefaultFormatBundle3D
            class_names: ['white-dash', 'white-solid', 'yellow-dash', 'yellow-solid', 
                         'blue-solid', 'construction', 'red-solid', 'barrier', 
                         'trafficcone', 'bicycle', 'motorcycle', 'pedestrian', 'background']
          - type: Collect3D
            keys: ['img']
            meta_keys: ['filename', 'ori_shape', 'img_shape', 'lidar2img', 
                       'depth2img', 'cam2img', 'pad_shape', 'scale_factor', 'flip', 
                       'pcd_horizontal_flip', 'pcd_vertical_flip', 'box_mode_3d', 
                       'box_type_3d', 'img_norm_cfg', 'pcd_trans', 'sample_idx', 
                       'pcd_scale_factor', 'pcd_rotation', 'pts_filename', 
                       'transformation_3d_flow', 'cam_intrinsic', 'lidar2cam']

# Evaluation configuration
evaluation:
  interval: 1
  pipeline:
    - type: LoadMultiViewImageFromFiles
      to_float32: true
    - type: LoadAnnotations3D
      with_bbox_3d: false
      with_label_3d: false
      with_lane_3d: true
    - type: MultiScaleFlipAug3D
      img_scale: [1920, 1280]
      pts_scale_ratio: 1
      flip: false
      transforms:
        - type: ResizeCropFlipImage
          data_aug_conf:
            resize_lim: [1.0, 1.0]
            final_dim: [640, 1600]
            rot_lim: [0.0, 0.0]
            H: 1280
            W: 1920
            rand_flip: false
            bot_pct_lim: [0.0, 0.0]
            cams: ['ring_front_center', 'ring_front_left', 'ring_front_right', 
                   'ring_side_left', 'ring_side_right', 'ring_rear_left', 'ring_rear_right']
            Ncams: 7
        - type: GlobalRotScaleTrans
          rot_range: [0, 0]
          scale_ratio_range: [1., 1.]
          translation_std: [0, 0, 0]
        - type: NormalizeMultiviewImage
          mean: [123.675, 116.28, 103.53]
          std: [58.395, 57.12, 57.375]
          to_rgb: true
        - type: PadMultiViewImage
          size_divisor: 32
        - type: DefaultFormatBundle3D
          class_names: ['white-dash', 'white-solid', 'yellow-dash', 'yellow-solid', 
                       'blue-solid', 'construction', 'red-solid', 'barrier', 
                       'trafficcone', 'bicycle', 'motorcycle', 'pedestrian', 'background']
        - type: Collect3D
          keys: ['img']
          meta_keys: ['filename', 'ori_shape', 'img_shape', 'lidar2img', 
                     'depth2img', 'cam2img', 'pad_shape', 'scale_factor', 'flip', 
                     'pcd_horizontal_flip', 'pcd_vertical_flip', 'box_mode_3d', 
                     'box_type_3d', 'img_norm_cfg', 'pcd_trans', 'sample_idx', 
                     'pcd_scale_factor', 'pcd_rotation', 'pts_filename', 
                     'transformation_3d_flow', 'cam_intrinsic', 'lidar2cam']

# Logging configuration
log_config:
  interval: 10
  hooks:
    - type: TextLoggerHook
    - type: TensorboardLoggerHook

# Checkpoint configuration
checkpoint_config:
  interval: 1
  max_keep_ckpts: 3

# Runtime configuration
dist_params:
  backend: nccl

log_level: INFO
load_from: null
resume_from: null
workflow: [('train', 1)]

# GPU configuration
gpu_ids: [0]