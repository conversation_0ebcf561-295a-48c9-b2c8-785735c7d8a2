# === Basic Settings 3D Lane Detection with Embeddings Based on BEVFusion ===
dataset_type: Custom3DLaneDataset
dataset_root: /turbo/pcpt/project/liuyibo/dataset_demo_path/lane_3d_dataset_mini
cam_list: ['120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
reduce_beams: 32
load_dim: 4
use_dim: 4
load_augmented: null

image_size: [256, 704]
max_epochs: 20
fp16:
  loss_scale:
    growth_interval: 2000

# === Removed Augmentations ===
# augment2d and augment3d removed

# === Core BEV & Voxel Settings ===
voxel_size: [0.1, 0.1, 0.2]
point_cloud_range: [0.0, -30, -1, 60, 30, 3.0]
camera_num: 6

# === Lane Task Specific ===
lane_classes:
  - white_solid_line
  - white_dashed_line
  - white_double_solid_line
  - white_solid_dashed_line
  - white_dashed_solid_line
  - white_double_dashed_line
  - yellow_solid_line
  - yellow_dashed_line
  - yellow_double_solid_line
  - yellow_solid_dashed_line
  - left_yellow_right_white_double_solid_line
  - road_edge_dashed_marking

# === Input Modality ===
input_modality:
  use_lidar: true
  use_camera: true
  use_radar: false
  use_map: false
  use_external: false

# === Data Pipelines ===
train_pipeline:
  - type: LoadMultiViewImageFromFiles
    to_float32: true
  - type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams}
    with_lidarid: False
  - type: LoadLaneAnnotations3D
    with_lane_3d: true
    with_lane_label_3d: true
  - type: LoadDenseDepthMapFromFile
    to_float32: true
    color_type: unchanged
    depth_scale: 256.0
    default_depth: 0.0
  
  - type: ImageAug3D
    final_dim: ${image_size}
    camera_num: ${camera_num}
    resize_lim: [[0.95, 1.05], [0.95, 1.05], [0.95, 1.05], [0.95, 1.05], [0.95, 1.05], [0.95, 1.05]]
    bot_pct_lim: [[0.0, 0.05], [0.0, 0.05], [0.0, 0.05], [0.0, 0.05], [0.0, 0.05], [0.0, 0.05]]
    rot_lim: [-2.0, 2.0]
    rand_flip: true
    is_train: true
  
  - type: GlobalRotScaleTrans
    resize_lim: [0.98, 1.02]
    rot_lim: [-0.1, 0.1]
    trans_lim: 0.1
    is_train: true
  
  - type: PointsRangeFilter
    point_cloud_range: ${point_cloud_range}
  
  - type: ImageNormalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
  
  - type: GenerateBEVLaneHeatmapTargets
    point_cloud_range: ${point_cloud_range}
    voxel_size: ${voxel_size}
    grid_size: [600, 600]
    lane_classes: ${lane_classes}
    target_config:
      gaussian_sigma: 1.5
      heatmap_radius: 3
      max_lanes: 40
      num_points: 120
      generate_instance_ids: true
  - type: DefaultFormatBundle3D
    classes: ${lane_classes}
  - type: Collect3D
    keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
      - lane_targets
      - depth_map
      - depth_map_valid
    meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix

test_pipeline:
  - type: LoadMultiViewImageFromFiles
    to_float32: true
  - type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: 4
    use_dim: 4
    reduce_beams: 32
  - type: LoadLaneAnnotations3D
    with_lane_3d: true
    with_lane_label_3d: true
  - type: LoadDenseDepthMapFromFile
    to_float32: true
    color_type: unchanged
    depth_scale: 256.0
  - type: DefaultFormatBundle3D
    classes: ${lane_classes}
  - type: Collect3D
    keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
      - depth_map
      - depth_map_valid
    meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix

# --- Data Loading Config ---
data:
  samples_per_gpu: 1
  workers_per_gpu: 0
  train:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/train_annotations.pkl"}
    pipeline: ${train_pipeline}
    modality: ${input_modality}
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}
  val:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/val_annotations.pkl"}
    pipeline: ${test_pipeline}
    modality: ${input_modality}
    test_mode: false
    box_type_3d: LiDAR
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}
  test:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/test_info.pkl"}
    pipeline: ${test_pipeline}
    modality: ${input_modality}
    test_mode: true
    box_type_3d: LiDAR
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}

# --- Evaluation ---
evaluation:
  interval: 1
  pipeline: ${test_pipeline}
  metric: 'OpenLane'
  eval_params:
     metric_list: ['f1_score', 'precision', 'recall', 'x_error_near', 'x_error_far', 'z_error']
     iou_threshold: 0.5

# --- Model Definition ---
model:
  type: BEVFusionForLanes
  encoders:
    camera:
      backbone:
        type: GPUNetWrapper
        model_type: GPUNet-1
        precision: fp16
        img_height: ${image_size[0]}
        img_width: ${image_size[1]}
        out_indices: [5, 10, 14]
        latency: 0.85ms
        gpuType: orin
        batch: 1
        pretrained: /configs/batch1/GV100/0.85ms.pth.tar
      neck:
        type: GeneralizedLSSFPN
        in_channels: [192, 384, 768]
        out_channels: 256
        start_level: 0
        num_outs: 3
        norm_cfg:
          type: BN2d
          requires_grad: true
        act_cfg:
          type: ReLU
          inplace: true
        upsample_cfg:
          mode: bilinear
          align_corners: false
      vtransform:
        # Use depth map-based transform for front camera
        type: DepthLSSTransformWithDepthMap
        in_channels: 256
        out_channels: 80
        image_size: ${image_size}
        feature_size: ${[image_size[0] // 8, image_size[1] // 8]}
        xbound: [-54.0, 54.0, 0.3]
        ybound: [-54.0, 54.0, 0.3]
        zbound: [-10.0, 10.0, 20.0]
        dbound: [1.0, 60.0, 0.5]
        downsample: 2
    lidar:
      voxelize:
        max_num_points: 10
        point_cloud_range: ${point_cloud_range}
        voxel_size: ${voxel_size}
        max_voxels: [120000, 160000]
      backbone:
        type: SparseEncoder
        in_channels: 5
        sparse_shape: [1440, 1440, 41]
        output_channels: 128
        order:
          - conv
          - norm
          - act
        encoder_channels:
          - [16, 16, 32]
          - [32, 32, 64]
          - [64, 64, 128]
          - [128, 128]
        encoder_paddings:
          - [0, 0, 1]
          - [0, 0, 1]
          - [0, 0, [1, 1, 0]]
          - [0, 0]
        block_type: basicblock

  fuser:
    type: ConvFuser
    in_channels: [80, 256]
    out_channels: 256

  fuser_for_seg:
    type: ConvFuser
    in_channels: [128, 256]  # Input from lidar features and decoder features
    out_channels: 256

  decoder:
    backbone:
      type: SECOND
      in_channels: 256
      out_channels: [128, 256]
      layer_nums: [5, 5]
      layer_strides: [1, 2]
      norm_cfg:
        type: BN
        eps: 1.0e-3
        momentum: 0.01
      conv_cfg:
        type: Conv2d
        bias: false
    neck:
      type: SECONDFPN
      in_channels: [128, 256]
      out_channels: [256, 256]
      upsample_strides: [1, 2]
      norm_cfg:
        type: BN
        eps: 1.0e-3
        momentum: 0.01
      upsample_cfg:
        type: deconv
        bias: false
      use_conv_for_no_stride: true

  heads:
    lane:
      type: BEVLaneHeatmapHead
      in_channels: 256
      feat_channels: 64
      num_classes: ${len(lane_classes)}
      grid_conf:
        xbound: [-81.6, 97.6, 0.1]
        ybound: [-48.0, 48.0, 0.1]
      row_points: 120
      z_range: [-1.0, 3.0]
      max_lanes: 40
      hm_thres: 0.25
      nms_kernel_size: 5
      use_sigmoid: true
      # Enable embedding-based lane grouping
      use_embedding: true
      embedding_dim: 8
      group_lanes: true
      lane_group_min_distance: 1.0
      clustering_method: 'dbscan'
      clustering_epsilon: 0.8
      clustering_min_points: 5
      loss_heatmap:
        type: GaussianFocalLoss
        alpha: 2.0
        gamma: 4.0
        reduction: mean
        loss_weight: 2.0
      loss_offset:
        type: L1Loss
        reduction: mean
        loss_weight: 1.5
      loss_z:
        type: L1Loss
        reduction: mean
        loss_weight: 1.5
      loss_cls:
        type: CrossEntropyLoss
        use_sigmoid: true
        reduction: mean
        loss_weight: 1.0
      loss_embedding:
        delta_v: 0.5
        delta_d: 2.0
        norm: 2
        alpha: 1.5
        beta: 1.0
        gamma: 0.001

# --- Training Settings ---
optimizer:
  type: AdamW
  lr: 0.0002
  weight_decay: 0.01
  betas: [0.9, 0.999]

# Dynamic loss weighting could be implemented here with GradNorm
# Reference: https://arxiv.org/abs/1711.02257

lr_config:
  policy: CosineAnnealing
  warmup: linear
  warmup_iters: 500
  warmup_ratio: 0.33333333
  min_lr_ratio: 1.0e-6