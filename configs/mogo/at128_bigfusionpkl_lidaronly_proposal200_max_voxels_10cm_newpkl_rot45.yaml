dataset_type: MogoAT128Dataset
dataset_root: /rtx/tuchaoping/data/mogo128/
calib_path: /rtx/tuchaoping/data/mogo128/vehicle_config_at128.json
cam_list: ['60_front', '120_front', '120_left', '120_right', '120_back']
gt_paste_stop_epoch: -1
reduce_beams: 32
load_dim: 4
use_dim: 4
load_augmented: null

image_size: [256, 704] #mogo原始图片尺寸,60_front', '120_front':H2160*W3840, '120_left', '120_right', '120_back':H1080*W1920
max_epochs: 30

augment2d:
  resize_train: [[0.183, 0.185],[0.183, 0.185],[0.38, 0.42],[0.38, 0.42],[0.38, 0.55]]
  resize_test: [[0.185, 0.185],[0.185, 0.185],[0.4, 0.4],[0.4, 0.4],[0.48, 0.48]]
  rotate: [-5.4, 5.4] # 度
  gridmask:
    prob: 0.0
    fixed_prob: true

augment3d: #点云数据增强 GlobalRotScaleTrans
  scale: [0.9, 1.1]
  rotate: [-0.78539816, 0.78539816] # 弧度,10du
  translate: 0.5

voxel_size: [0.1, 0.1, 0.1]
point_cloud_range: [-81.6, -48, -1, 96.0, 48, 3.0]
camera_num: 5

object_classes:
  - person
  - bike
  - rider
  - car
  - truck
  - bus

input_modality:
  use_lidar: true
  use_camera: false
  use_radar: false
  use_map: false
  use_external: false

train_pipeline:
  # -
  #   type: LoadMultiViewImageFromFiles
  #   to_float32: true
  -
    type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams} #只有在load_augmented有效的情况下才有用
    load_augmented: ${load_augmented} #null
  -
    type: LoadAnnotations3D
    with_bbox_3d: true
    with_label_3d: true
    with_attr_label: False
  # -
  #   type: ImageAug3D #图像的3D增强
  #   final_dim: ${image_size}
  #   camera_num: ${camera_num}
  #   resize_lim: ${augment2d.resize_train} #resize_lim、bot_pct_lim参数分camera
  #   bot_pct_lim: [[0.12, 0.15], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0]]
  #   rot_lim: ${augment2d.rotate}
  #   rand_flip: true
  #   is_train: true
  -
    # type: GlobalRotScaleTrans #点云数据增强,对data["points"]以及data["gt_bboxes_3d"]进行缩放、旋转、平移操作，并保存转换矩阵
    # resize_lim: [1.0, 1.0] #缩放区间
    # rot_lim: [0.0, 0.0] #旋转区间
    # trans_lim: 0.0 #平移
    # is_train: false #表示不使用点云数据增强了
    type: GlobalRotScaleTrans
    resize_lim: ${augment3d.scale}
    rot_lim: ${augment3d.rotate}
    trans_lim: ${augment3d.translate}
    is_train: true
  -
    type: RandomFlipHorizontal3D
  -
    type: PointsRangeFilter
    point_cloud_range: ${point_cloud_range}
  -
    type: ObjectRangeFilter
    point_cloud_range: ${point_cloud_range}
  -
    type: ObjectNameFilter
    classes: ${object_classes}
  # -
  #   type: ImageNormalize
  #   mean: [0.485, 0.456, 0.406]
  #   std: [0.229, 0.224, 0.225]
  -
    type: PointShuffle
  -
    type: DefaultFormatBundle3D
    classes: ${object_classes}
  -
    type: Collect3D
    keys:
      # - img
      - points
      - gt_bboxes_3d
      - gt_labels_3d
    meta_keys:
      # - camera_intrinsics
      # - camera2ego
      - lidar2ego
      # - lidar2camera
      # - camera2lidar
      # - lidar2image
      # - img_aug_matrix
      - lidar_aug_matrix

test_pipeline:
  # -
  #   type: LoadMultiViewImageFromFiles
  #   to_float32: true
  -
    type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams}
    load_augmented: ${load_augmented}
  -
    type: LoadAnnotations3D
    with_bbox_3d: true
    with_label_3d: true
    with_attr_label: False
  # -
  #   type: ImageAug3D
  #   final_dim: ${image_size} #camera数据增强之后，大小变成:[256, 704]
  #   camera_num: ${camera_num}
  #   resize_lim: ${augment2d.resize_test} #resize_lim、bot_pct_lim参数分camera
  #   bot_pct_lim: [[0.12, 0.15], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0]]
  #   rot_lim: [0.0, 0.0]
  #   rand_flip: false
  #   is_train: false
  -
    type: GlobalRotScaleTrans
    resize_lim: [1.0, 1.0]
    rot_lim: [0.0, 0.0]
    trans_lim: 0.0
    is_train: false
  -
    type: PointsRangeFilter
    point_cloud_range: ${point_cloud_range}
  -
    type: ObjectRangeFilter
    point_cloud_range: ${point_cloud_range}
  # -
  #   type: ImageNormalize
  #   mean: [0.485, 0.456, 0.406]
  #   std: [0.229, 0.224, 0.225]
  -
    type: DefaultFormatBundle3D
    classes: ${object_classes}
  -
    type: Collect3D
    keys:
      # - img
      - points
      - gt_bboxes_3d
      - gt_labels_3d
    meta_keys:
      # - camera_intrinsics #camera内参
      # - camera2ego #camera frame到ego frame坐标转换
      - lidar2ego #lidar frame到ego frame坐标转换
      # - lidar2camera #lidar到camera的坐标转换
      # - camera2lidar #camera到lidar的坐标转换
      # - lidar2image #lidar到像素坐标系的坐标转换
      # - img_aug_matrix #
      - lidar_aug_matrix

data:
  samples_per_gpu: 6
  workers_per_gpu: 2
  train:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "at128_filtered_pkl_hrn/at128_train_infos_0311_clsnamev4_ptsnum.pkl"}
    pipeline: ${train_pipeline}
    object_classes: ${object_classes}
    modality: ${input_modality}
    test_mode: false
    box_type_3d: LiDAR
    calib_path: ${calib_path}
    cam_list: ${cam_list}
    label_map_pth: configs/mogo_21.yaml
    point_cloud_range: ${point_cloud_range}
    min_pts_num: 5
  val:
    type: ${dataset_type} #
    dataset_root: ${dataset_root} #数据路径
    ann_file: ${dataset_root + "at128_filtered_pkl_hrn/at128_val_infos_0311_new_clsnamev4_ptsnum.pkl"}
    # ann_file: /rbs/houjiayue/data/at128_seg/at128_filtered_pkl_hrn/at128_val_infos_0311_new_clsnamev4_ptsnum_test4_.pkl
    pipeline: ${test_pipeline} #加载pipeline list
    object_classes: ${object_classes} #类型列表
    modality: ${input_modality} #输入模式，具体使用哪些传感器
    test_mode: false
    post_center_range: ${point_cloud_range}
    box_type_3d: LiDAR
    calib_path: ${calib_path}
    cam_list: ${cam_list}
    label_map_pth: configs/mogo_21.yaml
    point_cloud_range: ${point_cloud_range}
    min_pts_num: 5
    # load_interval: 100
  test:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "at128_filtered_pkl_hrn/at128_val_infos_0311_new_clsnamev4_ptsnum.pkl"}
    # ann_file: /rbs/houjiayue/data/at128_seg/at128_filtered_pkl_hrn/at128_val_infos_0311_new_clsnamev4_ptsnum_test1.pkl
    # ann_file: /rbs/houjiayue/data/at128_seg/at128_filtered_pkl_hrn/at128_val_infos_0311_new_clsnamev4_ptsnum_test4_.pkl
    pipeline: ${test_pipeline}
    object_classes: ${object_classes}
    modality: ${input_modality}
    test_mode: true
    post_center_range: ${point_cloud_range}
    box_type_3d: LiDAR
    calib_path: ${calib_path}
    cam_list: ${cam_list}
    label_map_pth: configs/mogo_21.yaml
    point_cloud_range: ${point_cloud_range}
    min_pts_num: 5
    # load_interval: 100

evaluation:
  interval: 1000
  pipeline: ${test_pipeline}

model:
  encoders:
    camera: null
    lidar:
      voxelize:
        point_cloud_range: ${point_cloud_range}
        voxel_size: ${voxel_size}
        max_voxels: [200000, 280000]
      backbone:
        in_channels: ${use_dim}
        sparse_shape: [1776, 960, 41] #先x后y

  fuser: null

  heads:
    object:
      num_proposals: 200
      num_rotcls: 24
      common_heads:
        center: [2, 2]
        height: [1, 2]
        dim: [3, 2]
        rotres: [1, 2]
        # vel: [2, 2]
      num_classes: 6
      train_cfg:
        dataset: mogo_at128
        grid_size: [960, 1776, 41]
        code_weights: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
      test_cfg:
        dataset: mogo_at128
        grid_size: [960, 1776, 41] #先y后x
        nms_type: rotate
        nms_thresh: 0.02
        pre_maxsize: 4096
        post_maxsize: 500
      bbox_coder:
        type: TransFusionBBoxCoder
        pc_range: ${point_cloud_range[:2]}
        post_center_range: ${point_cloud_range}
        score_threshold: 0.1
        out_size_factor: 8
        voxel_size: ${voxel_size[:2]}
        code_size: 8
      loss_cls: 
        type: FocalLoss
        use_sigmoid: true
        gamma: 2.0
        alpha: 0.25
        reduction: mean
        loss_weight: 1.0
      loss_heatmap:
        type: GaussianFocalLoss
        reduction: mean
        loss_weight: 1.0
      loss_bbox:
        type: L1Loss
        reduction: mean
        loss_weight: 1.0
      loss_rotres:
        type: SmoothL1Loss
        reduction: mean
        loss_weight: 2.0
      loss_rotcls:
        type: MultiClassFocalLossWithAlpha
        gamma: 2.0
        alpha: [1.0, 1.0,1.0,1.0,1.0,1.0, 1.0, 1.0,1.0,1.0,1.0,1.0, 1.0, 1.0,1.0,1.0,1.0,1.0, 1.0, 1.0,1.0,1.0,1.0,1.0]
        reduction: mean
        loss_weight: 1.0


lr_config:
  policy: CosineAnnealing
  warmup: linear
  warmup_iters: 500
  warmup_ratio: 0.33333333
  # min_lr_ratio: 1.0e-4
  min_lr_ratio: 1.0e-3
 
find_unused_parameters: True