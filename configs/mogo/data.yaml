dataset_type: MogoDataset
dataset_root: data/train_yt_rsp_falcon_230616_7587D_20/
gt_paste_stop_epoch: -1
reduce_beams: 32
load_dim: 5
use_dim: 5
load_augmented: null

image_size: [256, 704]

augment2d:
  resize: [[0.38, 0.55], [0.48, 0.48]]
  rotate: [-5.4, 5.4] # 度
  gridmask:
    prob: 0.0
    fixed_prob: true

augment3d:
  scale: [0.9, 1.1]
  rotate: [-0.78539816, 0.78539816] # 弧度
  translate: 0.5

object_classes:
  - person
  - pushing
  - bike
  - rider
  - car
  - truck
  - bus
  - obstacle #unknown
  - sign
  - noise

input_modality:
  use_lidar: true
  use_camera: true
  use_radar: false
  use_map: false
  use_external: false

train_pipeline:
  -
    type: LoadMultiViewImageFromFiles
    to_float32: true
  -
    type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams}
    load_augmented: ${load_augmented}
  -
    type: LoadAnnotations3D
    with_bbox_3d: true
    with_label_3d: true
    with_attr_label: False
  -
    type: ImageAug3D
    final_dim: ${image_size}
    resize_lim: ${augment2d.resize[0]}
    bot_pct_lim: [0.0, 0.0]
    rot_lim: ${augment2d.rotate}
    rand_flip: true
    is_train: true
  -
    type: GlobalRotScaleTrans
    resize_lim: ${augment3d.scale}
    rot_lim: ${augment3d.rotate}
    trans_lim: ${augment3d.translate}
    is_train: true
  -
    type: RandomFlip3D
  -
    type: PointsRangeFilter
    point_cloud_range: ${point_cloud_range}
  -
    type: ObjectRangeFilter
    point_cloud_range: ${point_cloud_range}
  -
    type: ObjectNameFilter
    classes: ${object_classes}
  -
    type: ImageNormalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
  -
    type: PointShuffle
  -
    type: DefaultFormatBundle3D
    classes: ${object_classes}
  -
    type: Collect3D
    keys:
      - img
      - points
      - gt_bboxes_3d
      - gt_labels_3d
    meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix

test_pipeline:
  -
    type: LoadMultiViewImageFromFiles
    to_float32: true
  -
    type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams}
    load_augmented: ${load_augmented}
  -
    type: LoadAnnotations3D
    with_bbox_3d: true
    with_label_3d: true
    with_attr_label: False
  -
    type: ImageAug3D
    final_dim: ${image_size} #camera数据增强之后，大小变成:[256, 704]
    resize_lim: ${augment2d.resize[1]} #[0.48, 0.48]
    bot_pct_lim: [0.0, 0.0]
    rot_lim: [0.0, 0.0]
    rand_flip: false
    is_train: false
  -
    type: GlobalRotScaleTrans
    resize_lim: [1.0, 1.0]
    rot_lim: [0.0, 0.0]
    trans_lim: 0.0
    is_train: false
  -
    type: PointsRangeFilter
    point_cloud_range: ${point_cloud_range}
  -
    type: ImageNormalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
  -
    type: DefaultFormatBundle3D
    classes: ${object_classes}
  -
    type: Collect3D
    keys:
      - img
      - points
      - gt_bboxes_3d
      - gt_labels_3d
    meta_keys:
      - camera_intrinsics #camera内参
      - camera2ego #camera frame到ego frame坐标转换
      - lidar2ego #lidar frame到ego frame坐标转换
      - lidar2camera #lidar到camera的坐标转换
      - camera2lidar #camera到lidar的坐标转换
      - lidar2image #lidar到像素坐标系的坐标转换
      - img_aug_matrix #
      - lidar_aug_matrix

data:
  samples_per_gpu: 2
  workers_per_gpu: 2
  train:
    type: CBGSDataset
    dataset:
      type: ${dataset_type}
      dataset_root: ${dataset_root}
      ann_file: ${dataset_root + "mogo_infos_train.pkl"}
      pipeline: ${train_pipeline}
      object_classes: ${object_classes}
      modality: ${input_modality}
      test_mode: false
      use_valid_flag: true
      box_type_3d: LiDAR
  val:
    type: ${dataset_type} #
    dataset_root: ${dataset_root} #数据路径
    ann_file: ${dataset_root + "mogo_infos_train.pkl"} #验证集处理数据
    pipeline: ${test_pipeline} #加载pipeline list
    object_classes: ${object_classes} #类型列表
    modality: ${input_modality} #输入模式，具体使用哪些传感器
    test_mode: false
    box_type_3d: LiDAR
  test:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "mogo_infos_train.pkl"}
    pipeline: ${test_pipeline}
    object_classes: ${object_classes}
    modality: ${input_modality}
    test_mode: true
    box_type_3d: LiDAR

evaluation:
  interval: 10
  pipeline: ${test_pipeline}
 
