name: "mogo"
label_map:
  "ignore": 0
  "road": 101 
  "sidewalk": 102 
  "curb": 103
  "parking": 104
  "other_flat": 105
  "building": 201
  "other_structure": 202
  "vegetation": 301
  "trunk": 302
  "terrain": 303 
  "fence": 401
  "pole": 402
  "other_nothings": 403
  "traffic_light": 404
  "ego": 405
  "upright_person": 1001
  "deformation": 1002
  "person_with_things": 1003
  "person_inside": 1004
  "pushing": 2001
  "bicycle": 3001
  "motorcycle": 3002
  "tricycle": 3003
  "crowds": 3004
  "other_bike": 3005
  "bicyclist": 4001
  "motorcyclist": 4002
  "tricyclist": 4003
  "other_rider": 4004
  "shed_car": 5001
  "mini_car": 5002
  "car": 5003
  "suv": 5004
  "minibus": 5005
  "lorry": 6001
  "machineshop": 6002
  "container": 6003
  "mini_bus": 7001
  "bus": 7002
  "removable_obstacle": 8001
  "resting_obstacle": 8002
  "micro_car": 8003
  "conebucket": 9001
  "waterhorse": 9002
  "bullbarrel": 9003
  "construction": 9004
  "warning": 9005
  "signage": 9006
  "rain": 10001
  "snow": 10002
  "dust": 10003
  "fog": 10004
  "water": 10005
  "other_noise": 10006
  "other_object": 11001

label_map_inv:
  0: "ignore"
  1: "person"
  2: "bike"
  3: "rider"
  4: "car"
  5: "truck"
  6: "bus"
  7: "obstacle"
  8: "traffic_sign"
  9: "noise"
  10: "road"
  11: "sidewalk"
  12: "curb"
  13: "other_flat"
  14: "building"
  15: "vegetation"
  16: "pole"
  17: "terrain"
  18: "fence"
  19: "other_nothings"
  20: "ego"

learning_map:
  0: 0
  101: 10
  102: 11
  103: 12
  104: 13
  105: 13
  201: 14
  202: 14
  301: 15
  302: 16
  303: 17
  401: 18
  402: 16
  403: 19
  404: 19
  405: 20
  1001: 1
  1002: 1
  1003: 1
  1004: 1
  2001: 3
  3001: 2
  3002: 2
  3003: 2
  3004: 2
  3005: 2
  4001: 3
  4002: 3
  4003: 3
  4004: 3
  5001: 4
  5002: 4
  5003: 4
  5004: 4
  5005: 4
  6001: 5
  6002: 5
  6003: 5
  7001: 6
  7002: 6
  8001: 7
  8002: 7
  8003: 7
  9001: 8
  9002: 8
  9003: 8
  9004: 8
  9005: 8
  9006: 8
  10001: 9
  10002: 9
  10003: 9
  10004: 9
  10005: 9
  10006: 9
  10007: 9
  11001: 19
  9999: 0