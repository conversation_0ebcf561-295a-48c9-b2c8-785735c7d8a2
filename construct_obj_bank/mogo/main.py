import os
import yaml
import pickle as pkl
import numpy as np

import utils
from multi_process import MultiProcess

import pdb
import glob
import tqdm
fpath_mogo = '/rtx/lijun/datasets/mogoB2/'
fpath_mogo_bank = os.path.join(fpath_mogo, 'bankB22024_250310')
class_set = set(("person", "bike", "rider", "car", "truck", "bus", "obstacle", "traffic_sign", "noise"))

for cate in class_set:
    fpath_cate = os.path.join(fpath_mogo_bank, cate)
    os.system("mkdir -p {}".format(fpath_cate))


with open('/cfs/.tmp/chaoping_detsegflow/multimodal_bevfusion/configs/mogo/mogo_21.yaml', 'r') as f:
    task_cfg = yaml.load(f, Loader=yaml.Loader)


common_infos=dict(class_set=class_set, fpath_mogo_bank=fpath_mogo_bank, task_cfg=task_cfg)

# flist = []
# for bag_id in os.listdir(os.path.join(fpath_mogo, "dataB22024_250220")):
#     fpath_bag = os.path.join(fpath_mogo, "dataB22024_250220", bag_id)
#     for fn in os.listdir(fpath_bag):
#         if fn.endswith('.npz'):
#             fname_npz = os.path.join(fpath_bag, fn)
#             flist.append(fname_npz)
data_root = os.path.join(fpath_mogo, "dataB22024_250220")
flist = []
for fn in os.listdir(data_root):
    # print('root:{}'.format(fn))
    fn=os.path.join(data_root,fn,'3d_npz')
    npz_files_in_dir = glob.glob(os.path.join(fn, '*.npz'))
    flist.extend(npz_files_in_dir)

for i, fname_npz in tqdm.tqdm(enumerate(flist)):
    utils.process_single(fname_npz, common_infos=common_infos)
# # multi-process
# multi_obj = MultiProcess(flist, utils.process_single, num_workers=40, common_infos=common_infos)
# for i, data in enumerate(multi_obj.run()):
#     if i % 5000 == 0:
#         print("Samples: {}".format(i))