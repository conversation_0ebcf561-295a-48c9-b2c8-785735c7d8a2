#!/usr/bin/env python3
"""
3D车道线检测坐标系统修复方案

根据调试信息分析，问题的根本原因是：
1. 期望的heatmap形状: [batch, channels, 23, 64] (height=23, width=64)
2. 实际的heatmap形状: [batch, channels, 64, 24] (height=64, width=24)
3. 这导致了坐标系统的完全颠倒

修复策略：在BEVLaneHeatmapHead中检测并修正这种形状不匹配
"""

import torch

def apply_coordinate_fix():
    """
    应用坐标系统修复的具体实现
    """
    
    # 方案1: 在_topk方法中修复坐标计算
    topk_fix = '''
    def _topk(self, heatmap, k=20):
        """Extract top-k points from heatmap with coordinate fix."""
        batch, cat, height, width = heatmap.size()
        
        # 检测形状是否颠倒
        expected_height, expected_width = self.ny, self.nx  # 23, 64
        shape_swapped = (height == expected_width and width == expected_height)
        
        if shape_swapped:
            print(f"检测到heatmap形状颠倒，应用坐标修正...")
            print(f"  实际形状: [{height}, {width}], 期望形状: [{expected_height}, {expected_width}]")
            # 使用修正后的维度计算
            effective_height = expected_height  # 23
            effective_width = expected_width    # 64
        else:
            effective_height = height
            effective_width = width
        
        # Reshape and find top k scores
        heatmap = heatmap.view(batch, -1)
        topk_scores, topk_inds = torch.topk(heatmap, k)
        
        if shape_swapped:
            # 当形状颠倒时，需要重新映射索引
            # 原始索引是基于[64, 24]的，需要转换为[23, 64]的坐标
            original_h, original_w = height, width  # 64, 24
            target_h, target_w = expected_height, expected_width  # 23, 64
            
            # 从原始索引计算原始坐标
            orig_y = (topk_inds // original_w).float()  # 在64维度上的索引
            orig_x = (topk_inds % original_w).float()   # 在24维度上的索引
            
            # 映射到目标坐标系统
            # 原来的Y(64维)映射到新的X(64维)
            # 原来的X(24维)映射到新的Y(23维)，但需要裁剪
            topk_xs = orig_y  # 64维度的索引直接作为X坐标
            topk_ys = torch.clamp(orig_x, 0, target_h - 1)  # 24维度的索引作为Y坐标，裁剪到23
        else:
            # 正常情况下的坐标计算
            topk_ys = (topk_inds // effective_width).float()
            topk_xs = (topk_inds % effective_width).float()
        
        # Note: topk_classes is placeholder to maintain API compatibility
        topk_classes = torch.zeros_like(topk_ys)
        
        return topk_scores, topk_inds, topk_classes, topk_ys, topk_xs
    '''
    
    # 方案2: 在get_lanes方法中添加智能检测和修复
    get_lanes_fix = '''
    def get_lanes(self, preds, img_metas, rescale=False):
        """Extract lanes from predictions with coordinate system fix."""
        
        # 获取预测结果
        heatmap = preds['heatmap']
        batch_size = heatmap.shape[0]
        
        # 检查heatmap形状并应用修复
        expected_height, expected_width = self.ny, self.nx  # 23, 64
        actual_height, actual_width = heatmap.shape[2], heatmap.shape[3]
        
        shape_swapped = (actual_height == expected_width and actual_width == expected_height)
        
        if shape_swapped:
            print(f"检测到heatmap形状颠倒，应用修正策略...")
            print(f"  实际: [{actual_height}, {actual_width}], 期望: [{expected_height}, {expected_width}]")
        
        # 获取topk结果（已在_topk中修复）
        scores, inds, _, ys_grid, xs_grid = self._topk(heatmap, k=self.max_lanes)
        
        # 处理每个batch
        all_lanes = []
        for b in range(batch_size):
            # 获取当前batch的有效检测
            mask = scores[b] > self.hm_thres
            if not mask.any():
                all_lanes.append([])
                continue
            
            # 提取有效的坐标和索引
            xs_b_grid = xs_grid[b][mask]
            ys_b_grid = ys_grid[b][mask]
            inds_b = inds[b][mask]
            
            # 坐标转换（现在应该是正确的）
            x_coords_world = xs_b_grid * self.x_res + self.x_min
            y_coords_world = ys_b_grid * self.y_res + self.y_min
            
            # 验证坐标范围
            x_range_ok = (x_coords_world.min() >= self.x_min and x_coords_world.max() <= self.x_max)
            y_range_ok = (y_coords_world.min() >= self.y_min and y_coords_world.max() <= self.y_max)
            
            if not (x_range_ok and y_range_ok):
                print(f"警告: 坐标范围检查失败")
                print(f"  X范围: [{x_coords_world.min():.1f}, {x_coords_world.max():.1f}] (期望: [{self.x_min}, {self.x_max}])")
                print(f"  Y范围: [{y_coords_world.min():.1f}, {y_coords_world.max():.1f}] (期望: [{self.y_min}, {self.y_max}])")
            
            # 继续处理偏移、Z值等...
            # [其余代码保持不变]
    '''
    
    return topk_fix, get_lanes_fix

def create_comprehensive_fix():
    """
    创建完整的修复方案
    """
    
    fix_code = '''
# 在BEVLaneHeatmapHead类中添加形状检测和修复方法

def _detect_and_fix_heatmap_shape(self, heatmap):
    """检测并修复heatmap形状问题"""
    batch, cat, height, width = heatmap.size()
    expected_height, expected_width = self.ny, self.nx  # 23, 64
    
    shape_swapped = (height == expected_width and width == expected_height)
    
    if shape_swapped:
        print(f"检测到heatmap形状颠倒: [{height}, {width}] -> [{expected_height}, {expected_width}]")
        # 可以选择转置heatmap或者修改后续的坐标计算
        # 这里选择修改坐标计算，因为转置可能影响其他部分
        return True
    
    return False

def _topk_with_shape_fix(self, heatmap, k=20):
    """带形状修复的topk方法"""
    batch, cat, height, width = heatmap.size()
    
    # 检测形状是否需要修复
    shape_needs_fix = self._detect_and_fix_heatmap_shape(heatmap)
    
    # Reshape and find top k scores
    heatmap = heatmap.view(batch, -1)
    topk_scores, topk_inds = torch.topk(heatmap, k)
    
    if shape_needs_fix:
        # 应用坐标修复逻辑
        expected_height, expected_width = self.ny, self.nx  # 23, 64
        
        # 从颠倒的形状[64, 24]计算原始坐标
        orig_y = (topk_inds // width).float()  # 在64维度上
        orig_x = (topk_inds % width).float()   # 在24维度上
        
        # 重新映射到正确的坐标系统[23, 64]
        topk_xs = orig_y  # 64维度 -> X坐标
        topk_ys = torch.clamp(orig_x, 0, expected_height - 1)  # 24维度 -> Y坐标(裁剪)
    else:
        # 正常的坐标计算
        topk_ys = (topk_inds // width).float()
        topk_xs = (topk_inds % width).float()
    
    topk_classes = torch.zeros_like(topk_ys)
    
    return topk_scores, topk_inds, topk_classes, topk_ys, topk_xs
    '''
    
    return fix_code

if __name__ == "__main__":
    print("3D车道线检测坐标系统修复方案")
    print("=" * 50)
    
    print("\n问题分析:")
    print("- 期望heatmap形状: [batch, channels, 23, 64]")
    print("- 实际heatmap形状: [batch, channels, 64, 24]")
    print("- 导致坐标系统完全颠倒")
    
    print("\n修复策略:")
    print("1. 在_topk方法中检测形状不匹配")
    print("2. 重新映射坐标索引")
    print("3. 确保最终坐标在正确范围内")
    
    topk_fix, get_lanes_fix = apply_coordinate_fix()
    comprehensive_fix = create_comprehensive_fix()
    
    print("\n修复代码已生成，请查看具体实现。")