#!/usr/bin/env python3
"""
Detect Hardcoded Constants Analysis
检测代码中的硬编码常量，特别是可能在启用裁剪时出现问题的魔法数字
"""

import os
import re
from pathlib import Path

def analyze_hardcoded_constants():
    """
    分析代码中的硬编码常量
    """
    print("="*80)
    print("HARDCODED CONSTANTS ANALYSIS")
    print("="*80)
    
    # 要分析的文件列表
    files_to_analyze = [
        "mmdet3d/models/heads/lane/bev_lane_heatmap_head.py",
        "mmdet3d/datasets/pipelines/lane_processing.py",
        "configs/lane/bevfusion_lane_detection_embadding_config.yaml"
    ]
    
    # 要检测的常量模式
    constant_patterns = {
        'downsample_factor': r'downsample_factor\s*=\s*(\d+)',
        'cos_45_threshold': r'(0\.707|COS_45_THRESHOLD)',
        'magic_numbers': r'\b(0\.4|0\.8|2\.0|4\.0)\b',
        'hardcoded_dimensions': r'\b(447|240|223|120|150|75)\b',
        'hardcoded_ranges': r'\[(-?\d+\.\d+),\s*(-?\d+\.\d+)\]',
        'downsample_rate': r'downsample_rate["\']?\s*:\s*(\d+)',
    }
    
    all_constants = {}
    
    for file_path in files_to_analyze:
        if not os.path.exists(file_path):
            continue
            
        print(f"\n{'='*60}")
        print(f"分析文件: {file_path}")
        print(f"{'='*60}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
        file_constants = {}
        
        for pattern_name, pattern in constant_patterns.items():
            matches = []
            for i, line in enumerate(lines, 1):
                if re.search(pattern, line):
                    matches.append((i, line.strip()))
            
            if matches:
                file_constants[pattern_name] = matches
                print(f"\n{pattern_name.upper()}:")
                print("-" * 40)
                for line_num, line_content in matches:
                    print(f"  Line {line_num}: {line_content}")
        
        all_constants[file_path] = file_constants
    
    # 特殊分析：downsample_factor的影响
    print(f"\n\n{'='*80}")
    print("DOWNSAMPLE_FACTOR 影响分析")
    print(f"{'='*80}")
    
    lane_processing_file = "mmdet3d/datasets/pipelines/lane_processing.py"
    if os.path.exists(lane_processing_file):
        with open(lane_processing_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
        print("\n1. downsample_factor的定义和使用：")
        print("-" * 50)
        for i, line in enumerate(lines, 1):
            if 'downsample_factor' in line.lower():
                print(f"  Line {i}: {line.strip()}")
                
        print("\n2. 分辨率调整相关代码：")
        print("-" * 50)
        for i, line in enumerate(lines, 1):
            if 'adjusted_' in line and ('res' in line or 'resolution' in line):
                print(f"  Line {i}: {line.strip()}")
    
    # 特殊分析：COS_45_THRESHOLD
    print(f"\n\n{'='*80}")
    print("COS_45_THRESHOLD (0.707) 分析")
    print(f"{'='*80}")
    
    bev_head_file = "mmdet3d/models/heads/lane/bev_lane_heatmap_head.py"
    if os.path.exists(bev_head_file):
        with open(bev_head_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
        print("\n1. COS_45_THRESHOLD的使用：")
        print("-" * 50)
        for i, line in enumerate(lines, 1):
            if '0.707' in line or 'COS_45_THRESHOLD' in line:
                print(f"  Line {i}: {line.strip()}")
                
        print("\n2. direction_consistent相关代码：")
        print("-" * 50)
        for i, line in enumerate(lines, 1):
            if 'direction_consistent' in line.lower():
                print(f"  Line {i}: {line.strip()}")
    
    # 配置文件中的硬编码值
    print(f"\n\n{'='*80}")
    print("配置文件中的硬编码值")
    print(f"{'='*80}")
    
    config_file = "configs/lane/bevfusion_lane_detection_embadding_config.yaml"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
        print("\n1. 分辨率相关配置：")
        print("-" * 50)
        for i, line in enumerate(lines, 1):
            if re.search(r'0\.4|0\.8', line):
                print(f"  Line {i}: {line.strip()}")
                
        print("\n2. 范围相关配置：")
        print("-" * 50)
        for i, line in enumerate(lines, 1):
            if re.search(r'\[.*\d+\.\d+.*\]', line) and ('range' in line or 'bound' in line):
                print(f"  Line {i}: {line.strip()}")
                
        print("\n3. downsample_rate配置：")
        print("-" * 50)
        for i, line in enumerate(lines, 1):
            if 'downsample_rate' in line:
                print(f"  Line {i}: {line.strip()}")
    
    # 风险评估
    print(f"\n\n{'='*80}")
    print("风险评估：启用裁剪时可能出现的问题")
    print(f"{'='*80}")
    
    print("\n1. 高风险常量：")
    print("-" * 50)
    print("  ❌ downsample_factor=2 (lane_processing.py)")
    print("     - 硬编码在GT生成器中")
    print("     - 影响分辨率计算：0.4m → 0.8m")
    print("     - 启用裁剪时可能导致维度不匹配")
    print("")
    print("  ⚠️  0.707 (COS_45_THRESHOLD)")
    print("     - 用于车道方向一致性检查")
    print("     - 应该提取为类常量")
    print("     - 裁剪不会直接影响，但代码可维护性差")
    
    print("\n2. 中等风险常量：")
    print("-" * 50)
    print("  ⚠️  硬编码维度 [447, 240, 223, 120]")
    print("     - 可能在测试或调试代码中出现")
    print("     - 启用裁剪后这些维度会改变")
    print("")
    print("  ⚠️  配置文件中的范围值")
    print("     - xbound: [-81.6, 97.6] (全局范围)")
    print("     - x_range: [0.0, 60.0] (任务范围)")
    print("     - 需要确保配置一致性")
    
    print("\n3. 低风险常量：")
    print("-" * 50)
    print("  ✅ downsample_rate=4 (配置文件)")
    print("     - 用于backbone/neck配置")
    print("     - 不直接影响BEV特征裁剪")
    print("")
    print("  ✅ 分辨率值 0.4m")
    print("     - 在配置文件中明确定义")
    print("     - 通过grid_conf传递，相对安全")
    
    print(f"\n\n{'='*80}")
    print("修复建议")
    print(f"{'='*80}")
    
    print("\n1. 立即修复（高优先级）：")
    print("-" * 50)
    print("  🔧 在GenerateBEVLaneHeatmapTargets中添加task_area_scope支持")
    print("  🔧 移除硬编码的downsample_factor=2，从配置中获取")
    print("  🔧 确保GT生成器的分辨率计算与BEV Head一致")
    
    print("\n2. 代码质量改进（中优先级）：")
    print("-" * 50)
    print("  🔧 将0.707提取为类常量COS_45_THRESHOLD")
    print("  🔧 添加维度一致性断言")
    print("  🔧 用logger.debug替换print语句")
    
    print("\n3. 长期维护（低优先级）：")
    print("-" * 50)
    print("  🔧 统一配置管理，避免重复定义")
    print("  🔧 添加配置验证逻辑")
    print("  🔧 改进错误消息和调试信息")
    
    print(f"\n{'='*80}")
    print("总结：需要重点关注downsample_factor硬编码问题")
    print(f"{'='*80}")

if __name__ == "__main__":
    analyze_hardcoded_constants()