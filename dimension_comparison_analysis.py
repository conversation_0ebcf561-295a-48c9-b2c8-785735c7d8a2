#!/usr/bin/env python3
"""
Dimension Comparison Analysis for BEVFusion Lane Detection

This script compares the dimensions calculated by BEVLaneHeatmapHead vs GenerateBEVLaneHeatmapTargets
to identify the root cause of dimension mismatch when task_area_scope cropping is enabled.
"""

import sys
import os
sys.path.append('/pcpt/pcpt/project/liuyibo/multimodal_bevfusion')

def analyze_bev_head_dimensions():
    """Analyze BEVLaneHeatmapHead dimension calculations"""
    print("=" * 80)
    print("BEVLaneHeatmapHead Dimension Analysis")
    print("=" * 80)
    
    # Configuration from bevfusion_lane_detection_embadding_config.yaml
    grid_conf = {
        'xbound': [-81.6, 97.6, 0.4],
        'ybound': [-48.0, 48.0, 0.4]
    }
    
    task_area_scope = {
        'x_range': [0.0, 60.0],
        'y_range': [-15.0, 15.0],
        'z_range': [-1.0, 3.0],
        'enable_crop': False,  # Currently disabled
        'crop_method': 'slice'
    }
    
    # Calculate global BEV dimensions (same as BEVLaneHeatmapHead)
    x_min, x_max, x_res = grid_conf['xbound']
    y_min, y_max, y_res = grid_conf['ybound']
    
    global_nx = int((x_max - x_min) / x_res)
    global_ny = int((y_max - y_min) / y_res)
    
    print(f"Global BEV Grid Configuration:")
    print(f"  X: [{x_min}, {x_max}] with resolution {x_res}m -> nx = {global_nx}")
    print(f"  Y: [{y_min}, {y_max}] with resolution {y_res}m -> ny = {global_ny}")
    print(f"  Global BEV size: [{global_nx}, {global_ny}]")
    
    # Calculate task area dimensions if cropping were enabled
    task_x_min, task_x_max = task_area_scope['x_range']
    task_y_min, task_y_max = task_area_scope['y_range']
    
    # Convert task area to grid indices
    task_nx = int((task_x_max - task_x_min) / x_res)
    task_ny = int((task_y_max - task_y_min) / y_res)
    
    print(f"\nTask Area Configuration:")
    print(f"  X: [{task_x_min}, {task_x_max}] -> task_nx = {task_nx}")
    print(f"  Y: [{task_y_min}, {task_y_max}] -> task_ny = {task_ny}")
    print(f"  Task area size: [{task_nx}, {task_ny}]")
    print(f"  Cropping enabled: {task_area_scope['enable_crop']}")
    
    # Calculate crop indices
    crop_x_start = int((task_x_min - x_min) / x_res)
    crop_x_end = int((task_x_max - x_min) / x_res)
    crop_y_start = int((task_y_min - y_min) / y_res)
    crop_y_end = int((task_y_max - y_min) / y_res)
    
    print(f"\nCrop Indices (if enabled):")
    print(f"  X: [{crop_x_start}:{crop_x_end}] (size: {crop_x_end - crop_x_start})")
    print(f"  Y: [{crop_y_start}:{crop_y_end}] (size: {crop_y_end - crop_y_start})")
    
    return {
        'global_nx': global_nx,
        'global_ny': global_ny,
        'task_nx': task_nx,
        'task_ny': task_ny,
        'crop_enabled': task_area_scope['enable_crop']
    }

def analyze_gt_generator_dimensions():
    """Analyze GenerateBEVLaneHeatmapTargets dimension calculations"""
    print("\n" + "=" * 80)
    print("GenerateBEVLaneHeatmapTargets Dimension Analysis")
    print("=" * 80)
    
    # Configuration from bevfusion_lane_detection_embadding_config.yaml
    # GT generator uses grid_conf from BEV head
    grid_conf = {
        'xbound': [-81.6, 97.6, 0.4],
        'ybound': [-48.0, 48.0, 0.4]
    }
    
    # GT generator calculations (from lane_processing.py)
    x_min, x_max, x_res = grid_conf['xbound']
    y_min, y_max, y_res = grid_conf['ybound']
    
    # Downsample factor (hardcoded in lane_processing.py)
    downsample_factor = 2
    adjusted_x_res = x_res * downsample_factor  # 0.4 * 2 = 0.8
    adjusted_y_res = y_res * downsample_factor  # 0.4 * 2 = 0.8
    
    # Grid size calculation
    grid_w = int((x_max - x_min) / adjusted_x_res)
    grid_h = int((y_max - y_min) / adjusted_y_res)
    
    print(f"GT Generator Configuration:")
    print(f"  Original resolution: X={x_res}m, Y={y_res}m")
    print(f"  Downsample factor: {downsample_factor}")
    print(f"  Adjusted resolution: X={adjusted_x_res}m, Y={adjusted_y_res}m")
    print(f"  Grid size: [{grid_w}, {grid_h}]")
    
    # Note: GT generator does NOT support task_area_scope cropping
    print(f"\nTask Area Support:")
    print(f"  task_area_scope support: NO")
    print(f"  Always generates full grid: [{grid_w}, {grid_h}]")
    
    return {
        'grid_w': grid_w,
        'grid_h': grid_h,
        'downsample_factor': downsample_factor,
        'adjusted_x_res': adjusted_x_res,
        'adjusted_y_res': adjusted_y_res,
        'supports_cropping': False
    }

def compare_dimensions():
    """Compare dimensions and identify mismatches"""
    print("\n" + "=" * 80)
    print("DIMENSION MISMATCH ANALYSIS")
    print("=" * 80)
    
    bev_dims = analyze_bev_head_dimensions()
    gt_dims = analyze_gt_generator_dimensions()
    
    print(f"\nCurrent State (enable_crop=False):")
    print(f"  BEV Head output: [{bev_dims['global_nx']}, {bev_dims['global_ny']}]")
    print(f"  GT Target size:  [{gt_dims['grid_w']}, {gt_dims['grid_h']}]")
    
    # Check current compatibility
    current_match = (bev_dims['global_nx'] == gt_dims['grid_w'] and 
                    bev_dims['global_ny'] == gt_dims['grid_h'])
    
    if current_match:
        print(f"  ✅ Current dimensions MATCH")
    else:
        print(f"  ❌ Current dimensions MISMATCH")
        print(f"     Difference: BEV [{bev_dims['global_nx'] - gt_dims['grid_w']}, {bev_dims['global_ny'] - gt_dims['grid_h']}]")
    
    print(f"\nIf enable_crop=True (PROBLEMATIC):")
    print(f"  BEV Head output: [{bev_dims['task_nx']}, {bev_dims['task_ny']}] (cropped)")
    print(f"  GT Target size:  [{gt_dims['grid_w']}, {gt_dims['grid_h']}] (unchanged)")
    
    # Check cropped compatibility
    cropped_match = (bev_dims['task_nx'] == gt_dims['grid_w'] and 
                    bev_dims['task_ny'] == gt_dims['grid_h'])
    
    if cropped_match:
        print(f"  ✅ Cropped dimensions would MATCH")
    else:
        print(f"  ❌ Cropped dimensions would MISMATCH")
        print(f"     Difference: BEV [{bev_dims['task_nx'] - gt_dims['grid_w']}, {bev_dims['task_ny'] - gt_dims['grid_h']}]")
    
    print(f"\nROOT CAUSE ANALYSIS:")
    print(f"  1. BEV Head supports task_area_scope cropping")
    print(f"  2. GT Generator does NOT support task_area_scope")
    print(f"  3. When cropping is enabled:")
    print(f"     - BEV Head outputs cropped features: [{bev_dims['task_nx']}, {bev_dims['task_ny']}]")
    print(f"     - GT Generator still produces full targets: [{gt_dims['grid_w']}, {gt_dims['grid_h']}]")
    print(f"  4. Loss function receives mismatched tensors -> CRASH")
    
    return {
        'current_match': current_match,
        'cropped_match': cropped_match,
        'bev_dims': bev_dims,
        'gt_dims': gt_dims
    }

def calculate_required_gt_dimensions():
    """Calculate what GT dimensions should be if cropping is enabled"""
    print(f"\n" + "=" * 80)
    print("REQUIRED GT GENERATOR MODIFICATIONS")
    print("=" * 80)
    
    # Task area configuration
    task_x_range = [0.0, 60.0]
    task_y_range = [-15.0, 15.0]
    
    # GT generator's adjusted resolution
    downsample_factor = 2
    base_res = 0.4
    adjusted_res = base_res * downsample_factor  # 0.8m
    
    # Calculate required GT dimensions for task area
    task_width = task_x_range[1] - task_x_range[0]  # 60.0m
    task_height = task_y_range[1] - task_y_range[0]  # 30.0m
    
    required_gt_w = int(task_width / adjusted_res)  # 60.0 / 0.8 = 75
    required_gt_h = int(task_height / adjusted_res)  # 30.0 / 0.8 = 37.5 -> 37
    
    print(f"Required GT Generator Changes:")
    print(f"  Task area: X[{task_x_range[0]}, {task_x_range[1]}] Y[{task_y_range[0]}, {task_y_range[1]}]")
    print(f"  Task size: {task_width}m x {task_height}m")
    print(f"  GT resolution: {adjusted_res}m (with downsample_factor={downsample_factor})")
    print(f"  Required GT size: [{required_gt_w}, {required_gt_h}]")
    
    print(f"\nImplementation Requirements:")
    print(f"  1. Add task_area_scope parameter to GenerateBEVLaneHeatmapTargets.__init__()")
    print(f"  2. Implement cropping logic in GT generation")
    print(f"  3. Ensure GT grid_size matches BEV Head's cropped output")
    print(f"  4. Update configuration to pass task_area_scope to GT generator")
    
    return {
        'required_gt_w': required_gt_w,
        'required_gt_h': required_gt_h,
        'task_area': {'x_range': task_x_range, 'y_range': task_y_range}
    }

if __name__ == "__main__":
    print("BEVFusion Lane Detection - Dimension Mismatch Analysis")
    print("=" * 80)
    
    comparison_result = compare_dimensions()
    required_dims = calculate_required_gt_dimensions()
    
    print(f"\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    print(f"Current configuration works: {comparison_result['current_match']}")
    print(f"Cropping would cause mismatch: {not comparison_result['cropped_match']}")
    print(f"Solution: Implement task_area_scope support in GT generator")
    print(f"Required GT output size when cropped: [{required_dims['required_gt_w']}, {required_dims['required_gt_h']}]")