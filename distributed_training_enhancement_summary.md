# 分布式训练脚本完善总结

## 概述

本文档总结了对 `multi_gpu_distributed_training.sh` 分布式训练脚本的完善工作，参考了 `train_distributed.sh` 的最佳实践，确保分布式训练的稳定性和性能。

## 主要改进点

### 1. 环境变量优化

#### 新增的关键环境变量：
```bash
# OpenMP 线程优化
export OMP_NUM_THREADS=4

# NCCL 通信优化
export NCCL_IB_DISABLE=0          # 启用 InfiniBand（如果可用）
export NCCL_IB_HCA=mlx5           # 指定 InfiniBand HCA
export NCCL_MIN_NCHANNELS=4       # 最小通道数

# CUDA 性能优化
export CUDA_LAUNCH_BLOCKING=0     # 禁用 CUDA 启动阻塞以提高性能
export CUDA_DEVICE_MAX_CONNECTIONS=1  # 限制每个设备的最大连接数
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512  # 优化 CUDA 内存分配
```

#### 环境变量说明：
- **OMP_NUM_THREADS**: 控制 OpenMP 线程数，避免过度订阅
- **NCCL_IB_***: InfiniBand 相关设置，提高多节点通信性能
- **CUDA_LAUNCH_BLOCKING**: 异步 CUDA 操作，提高 GPU 利用率
- **PYTORCH_CUDA_ALLOC_CONF**: 优化内存分配策略，减少碎片

### 2. GPU 检查和验证

#### 新增 `check_gpus()` 函数：
- 验证 NVIDIA 驱动是否可用
- 检查可用 GPU 数量是否满足要求
- 验证每个 GPU 的内存容量（最低 16GB）
- 提供详细的错误信息和建议

```bash
check_gpus() {
    # 检查驱动
    if ! command -v nvidia-smi &> /dev/null; then
        echo "Error: NVIDIA driver not found"
        exit 1
    fi
    
    # 检查 GPU 数量和内存
    # ...
}
```

### 3. 日志系统

#### 新增 `setup_logging()` 函数：
- 创建带时间戳的日志文件
- 同时输出到控制台和文件
- 按版本组织日志目录

```bash
setup_logging() {
    local version=$1
    LOG_DIR="logs/${version}"
    mkdir -p ${LOG_DIR}
    exec &> >(tee -a "${LOG_DIR}/training_$(date +%Y%m%d_%H%M%S).log")
}
```

### 4. 错误处理机制

#### 新增 `handle_error()` 函数：
- 统一的错误处理和日志记录
- 错误信息写入专门的错误日志文件
- 提供清晰的错误追踪

#### 训练退出码检查：
- 检查 `torchrun` 的退出状态
- 根据退出码记录成功或失败状态
- 提供详细的状态日志

### 5. 配置和参数优化

#### 新增配置项：
- `VERSION`: 用于日志组织和版本管理
- 更清晰的变量命名和注释
- 改进的命令行参数格式

## 使用方法

### 1. 直接运行分布式训练
```bash
./multi_gpu_distributed_training.sh
```

### 2. 验证配置（推荐先运行）
```bash
./verify_distributed_training.sh
```

验证脚本会：
- 检查配置文件是否存在
- 验证 `find_unused_parameters` 设置
- 测试 GPU 可用性
- 运行短时间的训练测试（1个迭代）

### 3. 自定义配置

可以通过修改脚本开头的配置变量来自定义：
```bash
GPUS=4          # GPU 数量
PORT=29501      # 主节点端口
CONFIG=...      # 配置文件路径
RUN_DIR=...     # 输出目录
```

## 故障排除

### 常见问题和解决方案：

1. **GPU 内存不足**
   - 检查每个 GPU 是否有至少 16GB 内存
   - 考虑减少批次大小或模型复杂度

2. **端口冲突**
   - 修改 `PORT` 变量使用不同端口
   - 验证脚本会自动选择随机端口

3. **NCCL 通信错误**
   - 检查网络配置和 `NCCL_SOCKET_IFNAME` 设置
   - 如果没有 InfiniBand，设置 `NCCL_IB_DISABLE=1`

4. **未使用参数错误**
   - 确保配置文件中设置了 `find_unused_parameters: true`
   - 检查模型中是否有条件性使用的参数

## 性能优化建议

1. **内存优化**
   - 调整 `PYTORCH_CUDA_ALLOC_CONF` 参数
   - 监控 GPU 内存使用情况

2. **通信优化**
   - 使用高速网络连接（InfiniBand 或 10GbE+）
   - 优化 NCCL 参数设置

3. **计算优化**
   - 使用混合精度训练（FP16）
   - 优化数据加载和预处理流水线

## 文件结构

完善后的项目包含以下关键文件：
- `multi_gpu_distributed_training.sh`: 主要的分布式训练脚本
- `verify_distributed_training.sh`: 配置验证脚本
- `distributed_training_enhancement_summary.md`: 本文档
- `logs/`: 训练日志目录（自动创建）

## 总结

通过参考 `train_distributed.sh` 的最佳实践，完善后的 `multi_gpu_distributed_training.sh` 脚本具备了：
- 更强的错误处理能力
- 更好的性能优化
- 更完善的日志系统
- 更可靠的环境检查

这些改进确保了分布式训练的稳定性和可维护性，为 BEVFusion 3D 车道线检测项目提供了可靠的训练基础设施。