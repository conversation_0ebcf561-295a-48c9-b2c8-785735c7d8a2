#!/usr/bin/env python3
"""
推理性能评估脚本
评估3D车道线检测模型的推理速度和内存使用
"""

import os
import sys
import time
import torch
import numpy as np
import argparse
from pathlib import Path
import psutil
import GPUtil
import json
from contextlib import contextmanager

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from mmcv import Config
from mmdet3d.models import build_model
from mmdet3d.datasets import build_dataset
from mmdet3d.datasets.pipelines import Compose


@contextmanager
def timer():
    """计时器上下文管理器"""
    start = time.perf_counter()
    yield
    end = time.perf_counter()
    return end - start


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self, model, device='cuda'):
        self.model = model
        self.device = device
        self.results = {
            'inference_times': [],
            'gpu_memory_usage': [],
            'cpu_memory_usage': [],
            'batch_sizes': [],
            'model_parameters': 0,
            'model_size_mb': 0
        }
        
        # 计算模型参数数量
        self.results['model_parameters'] = sum(p.numel() for p in model.parameters())
        
        # 计算模型大小
        param_size = sum(p.numel() * p.element_size() for p in model.parameters())
        buffer_size = sum(b.numel() * b.element_size() for b in model.buffers())
        self.results['model_size_mb'] = (param_size + buffer_size) / 1024**2
    
    def profile_single_inference(self, data_batch):
        """分析单次推理性能"""
        # 预热
        with torch.no_grad():
            _ = self.model(**data_batch)
        
        # 清理缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        # 记录初始状态
        initial_gpu_memory = 0
        if torch.cuda.is_available():
            initial_gpu_memory = torch.cuda.memory_allocated() / 1024**2
        
        initial_cpu_memory = psutil.virtual_memory().used / 1024**2
        
        # 推理计时
        start_time = time.perf_counter()
        
        with torch.no_grad():
            if torch.cuda.is_available():
                torch.cuda.synchronize()
            
            output = self.model(**data_batch)
            
            if torch.cuda.is_available():
                torch.cuda.synchronize()
        
        end_time = time.perf_counter()
        
        # 记录性能指标
        inference_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        peak_gpu_memory = 0
        if torch.cuda.is_available():
            peak_gpu_memory = torch.cuda.max_memory_allocated() / 1024**2
        
        peak_cpu_memory = psutil.virtual_memory().used / 1024**2
        
        self.results['inference_times'].append(inference_time)
        self.results['gpu_memory_usage'].append(peak_gpu_memory - initial_gpu_memory)
        self.results['cpu_memory_usage'].append(peak_cpu_memory - initial_cpu_memory)
        
        return {
            'inference_time_ms': inference_time,
            'gpu_memory_mb': peak_gpu_memory - initial_gpu_memory,
            'cpu_memory_mb': peak_cpu_memory - initial_cpu_memory
        }
    
    def profile_throughput(self, data_batch, duration_seconds=30):
        """分析吞吐量性能"""
        print(f"🔄 开始吞吐量测试 ({duration_seconds}秒)...")
        
        # 预热
        for _ in range(10):
            with torch.no_grad():
                _ = self.model(**data_batch)
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        start_time = time.time()
        inference_count = 0
        
        while time.time() - start_time < duration_seconds:
            with torch.no_grad():
                _ = self.model(**data_batch)
                inference_count += 1
        
        actual_duration = time.time() - start_time
        throughput = inference_count / actual_duration
        
        return {
            'throughput_fps': throughput,
            'total_inferences': inference_count,
            'duration_seconds': actual_duration
        }
    
    def generate_report(self):
        """生成性能报告"""
        if not self.results['inference_times']:
            print("❌ 没有性能数据可用于生成报告")
            return
        
        print("\n" + "="*80)
        print("🚀 3D车道线检测模型性能报告")
        print("="*80)
        
        # 模型基本信息
        print(f"\n📋 模型信息:")
        print(f"   参数数量: {self.results['model_parameters']:,}")
        print(f"   模型大小: {self.results['model_size_mb']:.2f} MB")
        print(f"   运行设备: {self.device}")
        
        # 推理时间统计
        times = np.array(self.results['inference_times'])
        print(f"\n⏱️  推理时间统计 (ms):")
        print(f"   平均时间: {np.mean(times):.2f}")
        print(f"   最小时间: {np.min(times):.2f}")
        print(f"   最大时间: {np.max(times):.2f}")
        print(f"   标准差:   {np.std(times):.2f}")
        print(f"   95%分位: {np.percentile(times, 95):.2f}")
        
        # FPS计算
        avg_fps = 1000 / np.mean(times)
        print(f"   平均FPS: {avg_fps:.2f}")
        
        # 内存使用统计
        if self.results['gpu_memory_usage']:
            gpu_mem = np.array(self.results['gpu_memory_usage'])
            print(f"\n🎮 GPU内存使用 (MB):")
            print(f"   平均使用: {np.mean(gpu_mem):.2f}")
            print(f"   峰值使用: {np.max(gpu_mem):.2f}")
        
        cpu_mem = np.array(self.results['cpu_memory_usage'])
        print(f"\n💻 CPU内存使用 (MB):")
        print(f"   平均使用: {np.mean(cpu_mem):.2f}")
        print(f"   峰值使用: {np.max(cpu_mem):.2f}")
        
        # 性能评估
        print(f"\n🎯 性能评估:")
        if avg_fps >= 30:
            print("   ✅ 实时性能优秀 (≥30 FPS)")
        elif avg_fps >= 10:
            print("   ⚠️  实时性能良好 (≥10 FPS)")
        else:
            print("   ❌ 实时性能需要优化 (<10 FPS)")
        
        # Orin设备兼容性评估
        print(f"\n🔌 NVIDIA Orin兼容性:")
        if avg_fps >= 20 and np.max(gpu_mem) <= 4096:  # Orin内存限制
            print("   ✅ 适合NVIDIA Orin部署")
        elif avg_fps >= 10:
            print("   ⚠️  可考虑Orin部署，需要优化")
        else:
            print("   ❌ 不建议Orin部署，需要大幅优化")
        
        return {
            'avg_inference_time_ms': float(np.mean(times)),
            'avg_fps': float(avg_fps),
            'peak_gpu_memory_mb': float(np.max(gpu_mem)) if self.results['gpu_memory_usage'] else 0,
            'model_parameters': self.results['model_parameters'],
            'model_size_mb': self.results['model_size_mb']
        }


def create_dummy_data(config):
    """创建虚拟测试数据"""
    cam_num = config.get('camera_num', 6)
    image_size = config.get('image_size', [256, 704])
    point_cloud_range = config.get('point_cloud_range', [-81.6, -48, -1, 97.6, 48, 3.0])
    
    # 虚拟图像数据
    img = torch.randn(1, cam_num, 3, image_size[0], image_size[1])
    
    # 虚拟点云数据
    num_points = 10000
    points = torch.randn(num_points, 4)  # x, y, z, intensity
    points[:, 0] = points[:, 0] * (point_cloud_range[3] - point_cloud_range[0]) / 2 + (point_cloud_range[3] + point_cloud_range[0]) / 2
    points[:, 1] = points[:, 1] * (point_cloud_range[4] - point_cloud_range[1]) / 2 + (point_cloud_range[4] + point_cloud_range[1]) / 2
    points[:, 2] = points[:, 2] * (point_cloud_range[5] - point_cloud_range[2]) / 2 + (point_cloud_range[5] + point_cloud_range[2]) / 2
    
    # 虚拟标定矩阵
    camera2ego = torch.eye(4).unsqueeze(0).repeat(cam_num, 1, 1)
    lidar2ego = torch.eye(4)
    lidar2camera = torch.eye(4).unsqueeze(0).repeat(cam_num, 1, 1)
    lidar2image = torch.eye(4).unsqueeze(0).repeat(cam_num, 1, 1)
    camera_intrinsics = torch.eye(4).unsqueeze(0).repeat(cam_num, 1, 1)
    camera2lidar = torch.eye(4).unsqueeze(0).repeat(cam_num, 1, 1)
    img_aug_matrix = torch.eye(4).unsqueeze(0).repeat(cam_num, 1, 1)
    lidar_aug_matrix = torch.eye(4)
    
    # 虚拟深度图
    depth_map = torch.rand(image_size[0], image_size[1]) * 60  # 0-60m深度
    
    return {
        'img': [img],
        'points': [points],
        'camera2ego': [camera2ego],
        'lidar2ego': [lidar2ego],
        'lidar2camera': [lidar2camera],
        'lidar2image': [lidar2image],
        'camera_intrinsics': [camera_intrinsics],
        'camera2lidar': [camera2lidar],
        'img_aug_matrix': [img_aug_matrix],
        'lidar_aug_matrix': [lidar_aug_matrix],
        'metas': [{}],
        'depth_map': [depth_map],
    }


def main():
    parser = argparse.ArgumentParser(description='评估3D车道线检测模型推理性能')
    parser.add_argument('--config', type=str, required=True,
                       help='模型配置文件路径')
    parser.add_argument('--checkpoint', type=str, 
                       help='模型权重文件路径')
    parser.add_argument('--device', type=str, default='cuda',
                       help='推理设备 (cuda/cpu)')
    parser.add_argument('--num-runs', type=int, default=100,
                       help='推理测试次数')
    parser.add_argument('--throughput-test', action='store_true',
                       help='是否进行吞吐量测试')
    parser.add_argument('--save-results', type=str,
                       help='保存结果的JSON文件路径')
    
    args = parser.parse_args()
    
    # 加载配置
    print(f"📖 加载配置文件: {args.config}")
    config = Config.fromfile(args.config)
    
    # 构建模型
    print("🏗️  构建模型...")
    model = build_model(config.model)
    
    if args.checkpoint and os.path.exists(args.checkpoint):
        print(f"📥 加载权重: {args.checkpoint}")
        checkpoint = torch.load(args.checkpoint, map_location='cpu')
        model.load_state_dict(checkpoint['state_dict'], strict=False)
    
    # 设置设备
    device = torch.device(args.device)
    model = model.to(device)
    model.eval()
    
    print(f"🎯 使用设备: {device}")
    
    # 创建性能分析器
    profiler = PerformanceProfiler(model, device)
    
    # 创建测试数据
    print("🔧 创建测试数据...")
    test_data = create_dummy_data(config)
    
    # 移动数据到设备
    for key in test_data:
        if isinstance(test_data[key], list):
            test_data[key] = [item.to(device) if torch.is_tensor(item) else item 
                             for item in test_data[key]]
    
    # 执行性能测试
    print(f"🚀 开始推理性能测试 ({args.num_runs} 次)...")
    
    for i in range(args.num_runs):
        if (i + 1) % 20 == 0:
            print(f"   进度: {i+1}/{args.num_runs}")
        
        result = profiler.profile_single_inference(test_data)
    
    # 吞吐量测试
    throughput_result = None
    if args.throughput_test:
        throughput_result = profiler.profile_throughput(test_data)
        print(f"\n📊 吞吐量测试结果:")
        print(f"   平均FPS: {throughput_result['throughput_fps']:.2f}")
        print(f"   总推理次数: {throughput_result['total_inferences']}")
    
    # 生成报告
    performance_summary = profiler.generate_report()
    
    # 保存结果
    if args.save_results:
        results = {
            'config_file': args.config,
            'checkpoint_file': args.checkpoint,
            'device': args.device,
            'num_runs': args.num_runs,
            'performance_summary': performance_summary,
            'detailed_results': profiler.results
        }
        
        if throughput_result:
            results['throughput_result'] = throughput_result
        
        with open(args.save_results, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 结果已保存至: {args.save_results}")


if __name__ == "__main__":
    main() 