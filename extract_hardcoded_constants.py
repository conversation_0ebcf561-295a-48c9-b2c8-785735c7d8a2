#!/usr/bin/env python3
"""
提取硬编码常量

这个脚本分析BEV Lane Heatmap Head中的硬编码数值常量，
并将它们提取为类常量，提高代码的可维护性和可读性。

主要功能：
1. 识别硬编码的数值常量
2. 分析常量的用途和含义
3. 将常量提取为类常量
4. 更新代码中的引用
"""

import os
import re
import sys
from pathlib import Path

def analyze_hardcoded_constants():
    """
    分析BEV Lane Heatmap Head中的硬编码常量
    """
    print("=" * 80)
    print("硬编码常量分析")
    print("=" * 80)
    
    # 目标文件
    target_file = "/pcpt/pcpt/project/liuyibo/multimodal_bevfusion/mmdet3d/models/heads/lane/bev_lane_heatmap_head.py"
    
    if not os.path.exists(target_file):
        print(f"❌ 文件不存在: {target_file}")
        return None
    
    print(f"📁 分析文件: {target_file}")
    
    # 读取文件内容
    with open(target_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义要查找的硬编码常量模式
    constant_patterns = [
        # 数学常量
        {
            'pattern': r'\b0\.707\b',
            'name': 'COS_45_DEGREES',
            'value': '0.707',
            'description': 'cos(45°) 用于角度一致性检查',
            'category': 'mathematical'
        },
        {
            'pattern': r'\b0\.5\b',
            'name': 'THRESHOLD_HALF',
            'value': '0.5',
            'description': '阈值常量，用于掩码转换和有效性检查',
            'category': 'threshold'
        },
        {
            'pattern': r'\b1\.5\b',
            'name': 'DELTA_D_DEFAULT',
            'value': '1.5',
            'description': 'Discriminative Loss中的默认delta_d参数',
            'category': 'loss_parameter'
        },
        {
            'pattern': r'\b1e-8\b',
            'name': 'EPSILON_SMALL',
            'value': '1e-8',
            'description': '小的epsilon值，用于防止除零错误',
            'category': 'numerical_stability'
        },
        {
            'pattern': r'\b0\.001\b',
            'name': 'GAMMA_DEFAULT',
            'value': '0.001',
            'description': 'Discriminative Loss中的默认gamma参数',
            'category': 'loss_parameter'
        },
        {
            'pattern': r'\b100\b',
            'name': 'CLASS_MULTIPLIER',
            'value': '100',
            'description': '类别感知实例ID的乘数',
            'category': 'encoding'
        },
        # 维度相关常量
        {
            'pattern': r'\b1000\b',
            'name': 'DEFAULT_LINE_LIMIT',
            'value': '1000',
            'description': '默认行数限制',
            'category': 'configuration'
        },
        # 百分比常量
        {
            'pattern': r'\b100\.0\b',
            'name': 'PERCENT_MULTIPLIER',
            'value': '100.0',
            'description': '百分比计算乘数',
            'category': 'calculation'
        },
        # 索引和维度常量
        {
            'pattern': r'\b0\.1\b',
            'name': 'TOLERANCE_SMALL',
            'value': '0.1',
            'description': '小的容差值，用于比较操作',
            'category': 'tolerance'
        },
    ]
    
    # 查找所有硬编码常量
    found_constants = []
    
    for const_info in constant_patterns:
        pattern = const_info['pattern']
        matches = list(re.finditer(pattern, content))
        
        for match in matches:
            line_num = content[:match.start()].count('\n') + 1
            
            # 获取上下文（前后各50个字符）
            start_context = max(0, match.start() - 50)
            end_context = min(len(content), match.end() + 50)
            context = content[start_context:end_context].replace('\n', ' ')
            
            found_constants.append({
                'line': line_num,
                'value': match.group(0),
                'context': context,
                'start_pos': match.start(),
                'end_pos': match.end(),
                **const_info
            })
    
    # 按行号排序
    found_constants.sort(key=lambda x: x['line'])
    
    print(f"\n📊 发现 {len(found_constants)} 个硬编码常量:")
    
    # 按类别分组显示
    categories = {}
    for const in found_constants:
        category = const['category']
        if category not in categories:
            categories[category] = []
        categories[category].append(const)
    
    for category, constants in categories.items():
        print(f"\n🔧 {category.upper()} 类别:")
        for const in constants:
            print(f"   第{const['line']}行: {const['value']} - {const['description']}")
            print(f"      上下文: ...{const['context']}...")
    
    return {
        'content': content,
        'constants': found_constants,
        'categories': categories
    }

def design_constants_class():
    """
    设计常量类结构
    """
    print("\n" + "=" * 80)
    print("设计常量类结构")
    print("=" * 80)
    
    constants_class = '''
    # === 类常量定义 ===
    # 数学常量
    COS_45_DEGREES = 0.707  # cos(45°) 用于角度一致性检查
    
    # 阈值常量
    THRESHOLD_HALF = 0.5  # 阈值常量，用于掩码转换和有效性检查
    TOLERANCE_SMALL = 0.1  # 小的容差值，用于比较操作
    
    # 数值稳定性常量
    EPSILON_SMALL = 1e-8  # 小的epsilon值，用于防止除零错误
    
    # Discriminative Loss 参数
    DELTA_V_DEFAULT = 0.5   # 默认delta_v参数
    DELTA_D_DEFAULT = 1.5   # 默认delta_d参数
    GAMMA_DEFAULT = 0.001   # 默认gamma参数
    ALPHA_DEFAULT = 1.0     # 默认alpha参数
    BETA_DEFAULT = 1.0      # 默认beta参数
    NORM_DEFAULT = 1        # 默认norm参数
    LOSS_WEIGHT_DEFAULT = 1.0  # 默认损失权重
    
    # 编码常量
    CLASS_MULTIPLIER = 100  # 类别感知实例ID的乘数
    
    # 配置常量
    DEFAULT_LINE_LIMIT = 1000  # 默认行数限制
    
    # 计算常量
    PERCENT_MULTIPLIER = 100.0  # 百分比计算乘数
    '''
    
    print("📋 设计的常量类:")
    print(constants_class)
    
    return constants_class

def generate_replacement_rules(analysis_result):
    """
    生成替换规则
    """
    print("\n" + "=" * 80)
    print("生成替换规则")
    print("=" * 80)
    
    constants = analysis_result['constants']
    
    # 定义替换规则
    replacement_rules = [
        {
            'pattern': r'\b0\.707\b',
            'replacement': 'self.COS_45_DEGREES',
            'description': '替换cos(45°)常量',
            'contexts': ['角度一致性', 'grouping', 'clustering']
        },
        {
            'pattern': r'(?<!\.)\b0\.5\b(?!\s*#.*delta_v)',  # 排除delta_v注释中的0.5
            'replacement': 'self.THRESHOLD_HALF',
            'description': '替换阈值常量0.5',
            'contexts': ['valid_mask', 'threshold', '> 0.5']
        },
        {
            'pattern': r'\b1\.5\b(?=.*delta_d)',
            'replacement': 'self.DELTA_D_DEFAULT',
            'description': '替换Discriminative Loss的delta_d默认值',
            'contexts': ['delta_d', 'DiscriminativeLoss']
        },
        {
            'pattern': r'\b1e-8\b',
            'replacement': 'self.EPSILON_SMALL',
            'description': '替换小的epsilon值',
            'contexts': ['+ 1e-8', 'epsilon', 'numerical stability']
        },
        {
            'pattern': r'\b0\.001\b(?=.*gamma)',
            'replacement': 'self.GAMMA_DEFAULT',
            'description': '替换Discriminative Loss的gamma默认值',
            'contexts': ['gamma', 'DiscriminativeLoss']
        },
        {
            'pattern': r'\b100\b(?=.*class_aware)',
            'replacement': 'self.CLASS_MULTIPLIER',
            'description': '替换类别感知实例ID乘数',
            'contexts': ['class_aware_instance_ids', '* 100']
        },
        {
            'pattern': r'\b0\.1\b(?=.*tolerance)',
            'replacement': 'self.TOLERANCE_SMALL',
            'description': '替换小的容差值',
            'contexts': ['tolerance', 'abs(', 'comparison']
        },
        {
            'pattern': r'\b100\.0\b(?=.*percent)',
            'replacement': 'self.PERCENT_MULTIPLIER',
            'description': '替换百分比计算乘数',
            'contexts': ['percent', '%', '*100']
        }
    ]
    
    print("📝 替换规则:")
    for i, rule in enumerate(replacement_rules, 1):
        print(f"\n{i}. {rule['description']}:")
        print(f"   模式: {rule['pattern']}")
        print(f"   替换: {rule['replacement']}")
        print(f"   上下文: {', '.join(rule['contexts'])}")
    
    return replacement_rules

def apply_constant_extraction(analysis_result, constants_class, replacement_rules):
    """
    应用常量提取
    """
    print("\n" + "=" * 80)
    print("应用常量提取")
    print("=" * 80)
    
    content = analysis_result['content']
    
    # 1. 在类定义后添加常量定义
    class_start = content.find('class BEVLaneHeatmapHead(BaseModule):')
    if class_start == -1:
        print("❌ 无法找到类定义")
        return None
    
    # 找到类定义行的结束
    class_line_end = content.find('\n', class_start)
    if class_line_end == -1:
        print("❌ 无法找到类定义行结束")
        return None
    
    # 在类定义后插入常量
    new_content = content[:class_line_end] + '\n' + constants_class + content[class_line_end:]
    
    print("✅ 已添加常量定义到类中")
    
    # 2. 应用替换规则
    replacements_made = 0
    
    for rule in replacement_rules:
        pattern = rule['pattern']
        replacement = rule['replacement']
        
        # 计算替换次数
        matches = list(re.finditer(pattern, new_content))
        
        # 进行替换，但要小心上下文
        for match in reversed(matches):  # 从后往前替换，避免位置偏移
            # 获取上下文来验证是否应该替换
            start_context = max(0, match.start() - 100)
            end_context = min(len(new_content), match.end() + 100)
            context = new_content[start_context:end_context]
            
            # 检查是否在合适的上下文中
            should_replace = False
            for ctx in rule['contexts']:
                if ctx.lower() in context.lower():
                    should_replace = True
                    break
            
            # 特殊处理：避免在注释或字符串中替换
            line_start = new_content.rfind('\n', 0, match.start()) + 1
            line_end = new_content.find('\n', match.end())
            if line_end == -1:
                line_end = len(new_content)
            line_content = new_content[line_start:line_end]
            
            # 跳过注释行和字符串
            if line_content.strip().startswith('#') or '"' in line_content or "'" in line_content:
                continue
            
            if should_replace:
                new_content = new_content[:match.start()] + replacement + new_content[match.end():]
                replacements_made += 1
        
        print(f"   ✅ {rule['description']}: 替换了 {len([m for m in matches if any(ctx.lower() in new_content[max(0, m.start()-100):m.end()+100].lower() for ctx in rule['contexts'])])} 处")
    
    print(f"\n📊 总计进行了 {replacements_made} 次常量提取")
    
    return new_content

def create_constants_file(analysis_result, refactored_content):
    """
    创建提取常量后的文件
    """
    print("\n" + "=" * 80)
    print("创建提取常量后的文件")
    print("=" * 80)
    
    # 原文件路径
    original_file = "/pcpt/pcpt/project/liuyibo/multimodal_bevfusion/mmdet3d/models/heads/lane/bev_lane_heatmap_head.py"
    
    # 备份原文件
    backup_file = original_file + ".backup_before_constants_extraction"
    
    try:
        # 创建备份
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(analysis_result['content'])
        print(f"✅ 创建备份文件: {backup_file}")
        
        # 写入提取常量后的内容
        with open(original_file, 'w', encoding='utf-8') as f:
            f.write(refactored_content)
        print(f"✅ 写入提取常量后的文件: {original_file}")
        
        # 统计信息
        original_lines = analysis_result['content'].count('\n')
        refactored_lines = refactored_content.count('\n')
        
        print(f"\n📊 文件统计:")
        print(f"   原文件行数: {original_lines}")
        print(f"   提取常量后行数: {refactored_lines}")
        print(f"   行数变化: {refactored_lines - original_lines:+d}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")
        return False

def create_constants_summary(analysis_result):
    """
    创建常量提取总结
    """
    print("\n" + "=" * 80)
    print("常量提取总结")
    print("=" * 80)
    
    constants = analysis_result['constants']
    categories = analysis_result['categories']
    
    summary = f'''
📖 硬编码常量提取总结:

1. 发现的硬编码常量:
   - 总数: {len(constants)} 个
   - 类别数: {len(categories)} 个

2. 按类别统计:
'''
    
    for category, const_list in categories.items():
        summary += f"   - {category}: {len(const_list)} 个\n"
    
    summary += '''
3. 新增类常量:
   - COS_45_DEGREES: cos(45°) 用于角度一致性检查
   - THRESHOLD_HALF: 阈值常量，用于掩码转换
   - EPSILON_SMALL: 防止除零错误的小值
   - DELTA_D_DEFAULT: Discriminative Loss默认参数
   - GAMMA_DEFAULT: Discriminative Loss默认参数
   - CLASS_MULTIPLIER: 类别感知实例ID乘数
   - TOLERANCE_SMALL: 比较操作的容差值
   - PERCENT_MULTIPLIER: 百分比计算乘数

4. 代码质量提升:
   - 提高了代码的可读性和可维护性
   - 集中管理了数值常量
   - 减少了魔法数字的使用
   - 便于后续的参数调优

5. 维护性改进:
   - 常量集中定义，便于修改
   - 清晰的常量命名和注释
   - 更好的代码文档化
   - 降低了出错的可能性
'''
    
    print(summary)
    
    # 保存常量提取总结到文件
    summary_file = "/pcpt/pcpt/project/liuyibo/multimodal_bevfusion/CONSTANTS_EXTRACTION_SUMMARY.md"
    try:
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("# BEV Lane Heatmap Head 硬编码常量提取总结\n\n")
            f.write(summary)
        print(f"\n✅ 常量提取总结已保存到: {summary_file}")
    except Exception as e:
        print(f"\n❌ 保存常量提取总结失败: {e}")

def main():
    """
    主函数
    """
    print("🔧 开始提取硬编码常量...")
    
    try:
        # 1. 分析硬编码常量
        analysis_result = analyze_hardcoded_constants()
        if analysis_result is None:
            return False
        
        # 2. 设计常量类
        constants_class = design_constants_class()
        
        # 3. 生成替换规则
        replacement_rules = generate_replacement_rules(analysis_result)
        
        # 4. 应用常量提取
        refactored_content = apply_constant_extraction(analysis_result, constants_class, replacement_rules)
        
        if refactored_content is None:
            return False
        
        # 5. 创建提取常量后的文件
        success = create_constants_file(analysis_result, refactored_content)
        
        if not success:
            return False
        
        # 6. 创建常量提取总结
        create_constants_summary(analysis_result)
        
        print("\n✅ 硬编码常量提取完成!")
        print("\n📋 提取总结:")
        print(f"   - 分析了 {len(analysis_result['constants'])} 个硬编码常量")
        print(f"   - 涉及 {len(analysis_result['categories'])} 个类别")
        print(f"   - 添加了 8+ 个类常量")
        print("   - 提高了代码的可维护性")
        print("\n🎯 建议下一步:")
        print("   1. 测试提取常量后的代码")
        print("   2. 验证功能的正确性")
        print("   3. 检查是否还有其他硬编码常量")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 常量提取过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)