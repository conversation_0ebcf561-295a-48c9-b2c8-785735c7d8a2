#!/usr/bin/env python3

import sys
import os
sys.path.append('/pcpt/pcpt/project/liuyibo/multimodal_bevfusion')

from mmdet3d.datasets import Custom3DLaneDataset
import numpy as np

def test_complete_dataset_functionality():
    """完整测试数据集的所有功能"""
    
    # 数据集配置
    dataset_root = '/pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames'
    ann_file = '/pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/train_annotations_skip30_without_depth_maps.pkl'
    cam_list = ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
    
    print("=== Custom3DLaneDataset 完整功能测试 ===")
    print(f"数据集根目录: {dataset_root}")
    print(f"标注文件: {ann_file}")
    print(f"相机列表: {cam_list}")
    print()
    
    # 创建数据集实例
    try:
        dataset = Custom3DLaneDataset(
            dataset_root=dataset_root,
            ann_file=ann_file,
            cam_list=cam_list,
            pipeline=[],  # 暂时不使用pipeline
            test_mode=False
        )
        
        print(f"✅ 数据集加载成功")
        print(f"数据集大小: {len(dataset)}")
        print()
        
        # 测试多个样本
        test_indices = [0, 1, 2] if len(dataset) >= 3 else list(range(len(dataset)))
        
        for idx in test_indices:
            print(f"=== 测试样本 {idx} ===")
            
            # 1. 测试get_data_info
            try:
                data_info = dataset.get_data_info(idx)
                print(f"✅ get_data_info成功")
                print(f"  - sample_idx: {data_info.get('sample_idx', 'N/A')}")
                print(f"  - timestamp: {data_info.get('timestamp', 'N/A')}")
                print(f"  - segment_id: {data_info.get('segment_id', 'N/A')}")
                
                # 检查图像路径
                if 'image_paths' in data_info:
                    print(f"  - 图像路径数量: {len(data_info['image_paths'])}")
                    valid_paths = sum(1 for p in data_info['image_paths'] if p and os.path.exists(p))
                    print(f"  - 有效图像路径: {valid_paths}/{len(data_info['image_paths'])}")
                
                # 检查LiDAR路径
                if 'lidar_path' in data_info:
                    lidar_exists = os.path.exists(data_info['lidar_path']) if data_info['lidar_path'] else False
                    print(f"  - LiDAR路径存在: {lidar_exists}")
                
                # 检查标定数据
                if 'camera_intrinsics' in data_info:
                    print(f"  - 相机内参数量: {len(data_info['camera_intrinsics'])}")
                if 'lidar2camera' in data_info:
                    print(f"  - LiDAR到相机变换数量: {len(data_info['lidar2camera'])}")
                
            except Exception as e:
                print(f"❌ get_data_info失败: {e}")
                continue
            
            # 2. 测试get_ann_info
            try:
                ann_info = dataset.get_ann_info(idx)
                print(f"✅ get_ann_info成功")
                
                if 'gt_lanes_3d' in ann_info:
                    print(f"  - 3D车道线数量: {len(ann_info['gt_lanes_3d'])}")
                    if len(ann_info['gt_lanes_3d']) > 0:
                        first_lane = ann_info['gt_lanes_3d'][0]
                        print(f"  - 第一条车道线点数: {len(first_lane)}")
                        print(f"  - 第一条车道线坐标范围: X[{first_lane[:, 0].min():.2f}, {first_lane[:, 0].max():.2f}], Y[{first_lane[:, 1].min():.2f}, {first_lane[:, 1].max():.2f}], Z[{first_lane[:, 2].min():.2f}, {first_lane[:, 2].max():.2f}]")
                
                if 'gt_lane_labels' in ann_info:
                    print(f"  - 车道线标签数量: {len(ann_info['gt_lane_labels'])}")
                    if len(ann_info['gt_lane_labels']) > 0:
                        unique_labels = np.unique(ann_info['gt_lane_labels'])
                        print(f"  - 唯一标签: {unique_labels}")
                
                if 'gt_lane_points_uvs' in ann_info:
                    print(f"  - 2D UV坐标数量: {len(ann_info['gt_lane_points_uvs'])}")
                
            except Exception as e:
                print(f"❌ get_ann_info失败: {e}")
            
            print()
        
        # 3. 测试数据集统计信息
        print("=== 数据集统计信息 ===")
        
        # 统计有效样本数量
        valid_samples = 0
        total_lanes = 0
        
        for i in range(min(10, len(dataset))):  # 只检查前10个样本
            try:
                data_info = dataset.get_data_info(i)
                ann_info = dataset.get_ann_info(i)
                
                if data_info and 'image_paths' in data_info and len(data_info['image_paths']) > 0:
                    valid_samples += 1
                    if 'gt_lanes_3d' in ann_info:
                        total_lanes += len(ann_info['gt_lanes_3d'])
                        
            except Exception:
                continue
        
        print(f"前10个样本中有效样本数: {valid_samples}/10")
        print(f"前10个样本中总车道线数: {total_lanes}")
        print(f"平均每个样本车道线数: {total_lanes/valid_samples:.2f}" if valid_samples > 0 else "无有效样本")
        
        print("\n🎉 数据集功能测试完成！")
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_complete_dataset_functionality()