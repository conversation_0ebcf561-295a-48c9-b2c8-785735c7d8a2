#!/bin/bash

# 脚本名称: update_symlinks.sh
# 功能: 更新指定目录下的所有符号链接，将旧路径替换为新路径
# 使用方法: ./update_symlinks.sh [目录路径]

# 设置默认目录
TARGET_DIR="/turbo/pcpt/data/B22024/dataB22024_250313test/"

# 如果提供了命令行参数，则使用提供的目录
if [ $# -gt 0 ]; then
    TARGET_DIR="$1"
fi

# 检查目录是否存在
if [ ! -d "$TARGET_DIR" ]; then
    echo "错误: 目录 $TARGET_DIR 不存在!"
    exit 1
fi

# 显示开始信息
echo "开始更新目录 $TARGET_DIR 中的符号链接..."
echo "将路径从 /turbo/pcpt/data/B22024/ 替换为 /pcpt/pcpt/data/B22024/"
echo "---------------------------------------------------"

# 计数器
updated=0
skipped=0
failed=0

# 进入目标目录
cd "$TARGET_DIR" || { echo "无法进入目录 $TARGET_DIR"; exit 1; }

# 遍历目录中的所有文件
for link in *; do
    # 检查是否为符号链接
    if [ -L "$link" ]; then
        # 获取当前链接目标
        target=$(readlink "$link")
        
        # 检查是否包含旧路径
        if [[ "$target" == *"/turbo/pcpt/data/B22024/"* ]]; then
            # 创建新的目标路径
            new_target=$(echo "$target" | sed 's|/turbo/pcpt/data/B22024/|/pcpt/pcpt/data/B22024/|')
            
            # 检查新目标是否存在
            # if [ -e "$new_target" ] || [ -L "$new_target" ]; then
            #     # 更新符号链接
            #     if ln -sf "$new_target" "$link"; then
            #         echo "已更新: $link"
            #         echo "  旧路径: $target"
            #         echo "  新路径: $new_target"
            #         ((updated++))
            #     else
            #         echo "更新失败: $link"
            #         ((failed++))
            #     fi
            # else
            #     echo "警告: 新目标路径不存在，跳过更新: $link"
            #     echo "  目标路径: $new_target"
            #     ((skipped++))
            # fi
            # 更新符号链接
            if ln -sf "$new_target" "$link"; then
                echo "已更新: $link"
                echo "  旧路径: $target"
                echo "  新路径: $new_target"
                ((updated++))
            else
                echo "更新失败: $link"
                ((failed++))
            fi
        else
            echo "跳过: $link (不包含需要替换的路径)"
            ((skipped++))
        fi
    fi
done

# 显示统计信息
echo "---------------------------------------------------"
echo "更新完成!"
echo "已更新: $updated 个符号链接"
echo "已跳过: $skipped 个符号链接"
echo "失败: $failed 个符号链接"

exit 0