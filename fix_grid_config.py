#!/usr/bin/env python3
"""
Fix grid configuration to match the updated point cloud range.
"""

def fix_grid_config():
    config_file = "configs/lane/bevfusion_lane_detection_embadding_config.yaml"
    
    # Read the file
    with open(config_file, 'r') as f:
        content = f.read()
    
    # Replace all occurrences of the old grid bounds
    content = content.replace(
        "xbound: [-81.6, 97.6, 0.4]  # CRITICAL: Match BEV head resolution",
        "xbound: [0.0, 60.0, 0.4]    # UPDATED: Match lane-focused point cloud range"
    )
    
    content = content.replace(
        "ybound: [-48.0, 48.0, 0.4]  # CRITICAL: Match BEV head resolution", 
        "ybound: [-15.0, 15.0, 0.4]  # UPDATED: Match lane-focused point cloud range"
    )
    
    content = content.replace(
        "xbound: [-81.6, 97.6, 0.4]",
        "xbound: [0.0, 60.0, 0.4]"
    )
    
    content = content.replace(
        "ybound: [-48.0, 48.0, 0.4]",
        "ybound: [-15.0, 15.0, 0.4]"
    )
    
    # Write back the file
    with open(config_file, 'w') as f:
        f.write(content)
    
    print("✓ Grid configuration updated to match lane-focused point cloud range")
    print("  - xbound: [0.0, 60.0, 0.4] (60m forward)")
    print("  - ybound: [-15.0, 15.0, 0.4] (±15m lateral)")

if __name__ == "__main__":
    fix_grid_config()