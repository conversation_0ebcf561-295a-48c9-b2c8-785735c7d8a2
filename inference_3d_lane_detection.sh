#!/bin/bash

# 3D Lane Detection Inference Script

# Default values
CONFIG="configs/lane/config_dense_depth_embedding_lane_detection.yaml"
CHECKPOINT=""
OUTPUT_DIR="results/3d_lane_detection"
SHOW=false
FPS=false
SHOW_SCORE=0.3
LAUNCHER="none"

# Function to display usage
usage() {
    echo "Usage: $0 --input <test_data_path> --checkpoint <checkpoint_path> [options]"
    echo "Options:"
    echo "  --input <path>        Path to test data directory (optional, can override dataset_root in config)"
    echo "  --checkpoint <path>   Path to model checkpoint file (required)"
    echo "  --config <path>       Path to config file (default: $CONFIG)"
    echo "  --output-dir <path>   Output directory for results (default: $OUTPUT_DIR)"
    echo "  --show                Enable visualization display"
    echo "  --show-dir <path>     Directory to save visualization results"
    echo "  --show-score <float>  Minimum score threshold for visualization (default: $SHOW_SCORE)"
    echo "  --fps                 Measure and report FPS"
    echo "  --launcher <type>     Job launcher ('none', 'pytorch', 'slurm', 'mpi') (default: none)"
    echo "  --fuse-conv-bn        Fuse conv and bn layers for faster inference"
    echo "  --help                Display this help message"
    exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        --input)
            INPUT_PATH="$2"
            shift 2
            ;;
        --checkpoint)
            CHECKPOINT="$2"
            shift 2
            ;;
        --config)
            CONFIG="$2"
            shift 2
            ;;
        --output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --show)
            SHOW=true
            shift
            ;;
        --show-dir)
            SHOW_DIR="$2"
            shift 2
            ;;
        --show-score)
            SHOW_SCORE="$2"
            shift 2
            ;;
        --fps)
            FPS=true
            shift
            ;;
        --launcher)
            LAUNCHER="$2"
            shift 2
            ;;
        --fuse-conv-bn)
            FUSE_CONV_BN=true
            shift
            ;;
        --help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Check required arguments
if [ -z "$CHECKPOINT" ]; then
    echo "Error: Checkpoint path is required"
    usage
fi

# Build command
CMD="python tools/inference_3d_lane_detection.py --config $CONFIG --checkpoint $CHECKPOINT --output-dir $OUTPUT_DIR --launcher $LAUNCHER"

# Add optional flags
if [ ! -z "$INPUT_PATH" ]; then
    CMD="$CMD --input $INPUT_PATH"
fi

if [ "$SHOW" = true ]; then
    CMD="$CMD --show"
fi

if [ ! -z "$SHOW_DIR" ]; then
    CMD="$CMD --show-dir $SHOW_DIR"
fi

if [ "$FPS" = true ]; then
    CMD="$CMD --fps"
fi

if [ "$FUSE_CONV_BN" = true ]; then
    CMD="$CMD --fuse-conv-bn"
fi

CMD="$CMD --show-score-thr $SHOW_SCORE"

# Print command
echo "Running: $CMD"

# Execute command
$CMD 