augment2d:
  bot_pct_lim_test:
  - - 0.0
    - 0.0
  - - 0.14
    - 0.14
  - - 0.22
    - 0.22
  - - 0.22
    - 0.22
  - - 0.17
    - 0.17
  - - 0.22
    - 0.22
  - - 0.22
    - 0.22
  bot_pct_lim_train:
  - - 0.0
    - 0.0
  - - 0.12
    - 0.15
  - - 0.2
    - 0.25
  - - 0.2
    - 0.25
  - - 0.15
    - 0.2
  - - 0.2
    - 0.25
  - - 0.2
    - 0.25
  resize_test:
  - - 0.185
    - 0.185
  - - 0.4
    - 0.4
  - - 0.4
    - 0.4
  - - 0.4
    - 0.4
  - - 0.47
    - 0.47
  - - 0.4
    - 0.4
  - - 0.4
    - 0.4
  resize_train:
  - - 0.184
    - 0.187
  - - 0.38
    - 0.42
  - - 0.38
    - 0.42
  - - 0.38
    - 0.42
  - - 0.38
    - 0.55
  - - 0.38
    - 0.42
  - - 0.38
    - 0.42
cam_list:
- 60_front
- 120_front
- 120_left
- 120_right
- 120_back
- right_back
- left_back
camera_num: 7
checkpoint_config:
  interval: 5
  max_keep_ckpts: 10
cudnn_benchmark: false
data:
  samples_per_gpu: 1
  test:
    ann_file: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/val_annotations_100frames.pkl
    box_type_3d: LiDAR
    cam_list:
    - 60_front
    - 120_front
    - 120_left
    - 120_right
    - 120_back
    - right_back
    - left_back
    dataset_root: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames
    lane_classes:
    - background
    - white-solid
    - white-dashed
    - white-double-solid
    - white-solid-dashed
    - white-dashed-solid
    - white-double-dashed
    - yellow-solid
    - yellow-dashed
    - yellow-double-solid
    - yellow-solid-dashed
    - left-yellow-right-white-double-solid
    - road-edge-dashed
    modality:
      use_camera: true
      use_external: false
      use_lidar: true
      use_map: false
      use_radar: false
    pipeline:
    - to_float32: true
      type: LoadMultiViewImageFromFiles
    - coord_type: LIDAR
      load_dim: 4
      reduce_beams: 32
      type: LoadPointsFromFile
      use_dim: 4
      with_lidarid: false
    - type: LoadLaneAnnotations3D
      with_lane_3d: true
      with_lane_label_3d: true
    - bot_pct_lim:
      - - 0.0
        - 0.0
      - - 0.14
        - 0.14
      - - 0.22
        - 0.22
      - - 0.22
        - 0.22
      - - 0.17
        - 0.17
      - - 0.22
        - 0.22
      - - 0.22
        - 0.22
      camera_num: 7
      final_dim:
      - 256
      - 704
      is_train: false
      rand_flip: false
      resize_lim:
      - - 0.185
        - 0.185
      - - 0.4
        - 0.4
      - - 0.4
        - 0.4
      - - 0.4
        - 0.4
      - - 0.47
        - 0.47
      - - 0.4
        - 0.4
      - - 0.4
        - 0.4
      rot_lim:
      - 0.0
      - 0.0
      type: ImageAug3D
    - is_train: false
      resize_lim:
      - 1.0
      - 1.0
      rot_lim:
      - 0.0
      - 0.0
      trans_lim: 0.0
      type: GlobalRotScaleTrans
    - point_cloud_range:
      - -81.6
      - -48
      - -1
      - 97.6
      - 48
      - 3.0
      type: PointsRangeFilter
    - mean:
      - 0.485
      - 0.456
      - 0.406
      std:
      - 0.229
      - 0.224
      - 0.225
      type: ImageNormalize
    - enable_visualization: false
      grid_conf:
        xbound:
        - -81.6
        - 97.6
        - 0.4
        ybound:
        - -48.0
        - 48.0
        - 0.4
      lane_classes:
      - background
      - white-solid
      - white-dashed
      - white-double-solid
      - white-solid-dashed
      - white-dashed-solid
      - white-double-dashed
      - yellow-solid
      - yellow-dashed
      - yellow-double-solid
      - yellow-solid-dashed
      - left-yellow-right-white-double-solid
      - road-edge-dashed
      point_cloud_range:
      - -81.6
      - -48
      - -1
      - 97.6
      - 48
      - 3.0
      target_config:
        cls_radius: 1
        gaussian_sigma: 1.0
        generate_instance_ids: true
        heatmap_radius: 2
        max_lanes: 40
        num_points: 120
        reg_radius: 0
        vis_threshold: 0.5
      type: GenerateBEVLaneHeatmapTargets
      visualization_output_dir: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/lane_visualization
    - classes:
      - background
      - white-solid
      - white-dashed
      - white-double-solid
      - white-solid-dashed
      - white-dashed-solid
      - white-double-dashed
      - yellow-solid
      - yellow-dashed
      - yellow-double-solid
      - yellow-solid-dashed
      - left-yellow-right-white-double-solid
      - road-edge-dashed
      type: DefaultFormatBundle3D
    - keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
      meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix
      type: Collect3D
    point_cloud_range:
    - -81.6
    - -48
    - -1
    - 97.6
    - 48
    - 3.0
    test_mode: true
    type: Custom3DLaneDataset
  train:
    ann_file: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/train_annotations_skip30_without_depth_maps.pkl
    cam_list:
    - 60_front
    - 120_front
    - 120_left
    - 120_right
    - 120_back
    - right_back
    - left_back
    dataset_root: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames
    lane_classes:
    - background
    - white-solid
    - white-dashed
    - white-double-solid
    - white-solid-dashed
    - white-dashed-solid
    - white-double-dashed
    - yellow-solid
    - yellow-dashed
    - yellow-double-solid
    - yellow-solid-dashed
    - left-yellow-right-white-double-solid
    - road-edge-dashed
    modality:
      use_camera: true
      use_external: false
      use_lidar: true
      use_map: false
      use_radar: false
    pipeline:
    - to_float32: true
      type: LoadMultiViewImageFromFiles
    - coord_type: LIDAR
      load_dim: 4
      reduce_beams: 32
      type: LoadPointsFromFile
      use_dim: 4
      with_lidarid: false
    - type: LoadLaneAnnotations3D
      with_lane_3d: true
      with_lane_label_3d: true
    - bot_pct_lim:
      - - 0.0
        - 0.0
      - - 0.12
        - 0.15
      - - 0.2
        - 0.25
      - - 0.2
        - 0.25
      - - 0.15
        - 0.2
      - - 0.2
        - 0.25
      - - 0.2
        - 0.25
      camera_num: 7
      final_dim:
      - 256
      - 704
      is_train: true
      rand_flip: false
      resize_lim:
      - - 0.184
        - 0.187
      - - 0.38
        - 0.42
      - - 0.38
        - 0.42
      - - 0.38
        - 0.42
      - - 0.38
        - 0.55
      - - 0.38
        - 0.42
      - - 0.38
        - 0.42
      rot_lim:
      - 0.0
      - 0.0
      type: ImageAug3D
    - is_train: true
      resize_lim:
      - 1.0
      - 1.0
      rot_lim:
      - 0.0
      - 0.0
      trans_lim: 0.0
      type: GlobalRotScaleTrans
    - point_cloud_range:
      - -81.6
      - -48
      - -1
      - 97.6
      - 48
      - 3.0
      type: PointsRangeFilter
    - mean:
      - 0.485
      - 0.456
      - 0.406
      std:
      - 0.229
      - 0.224
      - 0.225
      type: ImageNormalize
    - enable_visualization: false
      grid_conf:
        xbound:
        - -81.6
        - 97.6
        - 0.4
        ybound:
        - -48.0
        - 48.0
        - 0.4
      lane_classes:
      - background
      - white-solid
      - white-dashed
      - white-double-solid
      - white-solid-dashed
      - white-dashed-solid
      - white-double-dashed
      - yellow-solid
      - yellow-dashed
      - yellow-double-solid
      - yellow-solid-dashed
      - left-yellow-right-white-double-solid
      - road-edge-dashed
      point_cloud_range:
      - -81.6
      - -48
      - -1
      - 97.6
      - 48
      - 3.0
      target_config:
        cls_radius: 1
        gaussian_sigma: 1.0
        generate_instance_ids: true
        heatmap_radius: 2
        max_lanes: 40
        num_points: 120
        reg_radius: 0
        vis_threshold: 0.5
      type: GenerateBEVLaneHeatmapTargets
      visualization_output_dir: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/lane_visualization
    - classes:
      - background
      - white-solid
      - white-dashed
      - white-double-solid
      - white-solid-dashed
      - white-dashed-solid
      - white-double-dashed
      - yellow-solid
      - yellow-dashed
      - yellow-double-solid
      - yellow-solid-dashed
      - left-yellow-right-white-double-solid
      - road-edge-dashed
      type: DefaultFormatBundle3D
    - keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
      - lane_targets
      meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix
      type: Collect3D
    point_cloud_range:
    - -81.6
    - -48
    - -1
    - 97.6
    - 48
    - 3.0
    test_mode: false
    type: Custom3DLaneDataset
  val:
    ann_file: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/val_annotations_skip30_without_depth_maps.pkl
    box_type_3d: LiDAR
    cam_list:
    - 60_front
    - 120_front
    - 120_left
    - 120_right
    - 120_back
    - right_back
    - left_back
    dataset_root: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames
    lane_classes:
    - background
    - white-solid
    - white-dashed
    - white-double-solid
    - white-solid-dashed
    - white-dashed-solid
    - white-double-dashed
    - yellow-solid
    - yellow-dashed
    - yellow-double-solid
    - yellow-solid-dashed
    - left-yellow-right-white-double-solid
    - road-edge-dashed
    modality:
      use_camera: true
      use_external: false
      use_lidar: true
      use_map: false
      use_radar: false
    pipeline:
    - to_float32: true
      type: LoadMultiViewImageFromFiles
    - coord_type: LIDAR
      load_dim: 4
      reduce_beams: 32
      type: LoadPointsFromFile
      use_dim: 4
      with_lidarid: false
    - type: LoadLaneAnnotations3D
      with_lane_3d: true
      with_lane_label_3d: true
    - bot_pct_lim:
      - - 0.0
        - 0.0
      - - 0.14
        - 0.14
      - - 0.22
        - 0.22
      - - 0.22
        - 0.22
      - - 0.17
        - 0.17
      - - 0.22
        - 0.22
      - - 0.22
        - 0.22
      camera_num: 7
      final_dim:
      - 256
      - 704
      is_train: false
      rand_flip: false
      resize_lim:
      - - 0.185
        - 0.185
      - - 0.4
        - 0.4
      - - 0.4
        - 0.4
      - - 0.4
        - 0.4
      - - 0.47
        - 0.47
      - - 0.4
        - 0.4
      - - 0.4
        - 0.4
      rot_lim:
      - 0.0
      - 0.0
      type: ImageAug3D
    - is_train: false
      resize_lim:
      - 1.0
      - 1.0
      rot_lim:
      - 0.0
      - 0.0
      trans_lim: 0.0
      type: GlobalRotScaleTrans
    - point_cloud_range:
      - -81.6
      - -48
      - -1
      - 97.6
      - 48
      - 3.0
      type: PointsRangeFilter
    - mean:
      - 0.485
      - 0.456
      - 0.406
      std:
      - 0.229
      - 0.224
      - 0.225
      type: ImageNormalize
    - enable_visualization: false
      grid_conf:
        xbound:
        - -81.6
        - 97.6
        - 0.4
        ybound:
        - -48.0
        - 48.0
        - 0.4
      lane_classes:
      - background
      - white-solid
      - white-dashed
      - white-double-solid
      - white-solid-dashed
      - white-dashed-solid
      - white-double-dashed
      - yellow-solid
      - yellow-dashed
      - yellow-double-solid
      - yellow-solid-dashed
      - left-yellow-right-white-double-solid
      - road-edge-dashed
      point_cloud_range:
      - -81.6
      - -48
      - -1
      - 97.6
      - 48
      - 3.0
      target_config:
        cls_radius: 1
        gaussian_sigma: 1.0
        generate_instance_ids: true
        heatmap_radius: 2
        max_lanes: 40
        num_points: 120
        reg_radius: 0
        vis_threshold: 0.5
      type: GenerateBEVLaneHeatmapTargets
      visualization_output_dir: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/lane_visualization
    - classes:
      - background
      - white-solid
      - white-dashed
      - white-double-solid
      - white-solid-dashed
      - white-dashed-solid
      - white-double-dashed
      - yellow-solid
      - yellow-dashed
      - yellow-double-solid
      - yellow-solid-dashed
      - left-yellow-right-white-double-solid
      - road-edge-dashed
      type: DefaultFormatBundle3D
    - keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
      meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix
      type: Collect3D
    point_cloud_range:
    - -81.6
    - -48
    - -1
    - 97.6
    - 48
    - 3.0
    test_mode: false
    type: Custom3DLaneDataset
  workers_per_gpu: 4
dataset_root: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames
dataset_type: Custom3DLaneDataset
deterministic: false
dist_params:
  backend: nccl
evaluation:
  eval_params:
    iou_threshold: 0.5
    metric_list:
    - f1_score
    - precision
    - recall
    - x_error_near
    - x_error_far
    - z_error
  interval: 1
  metric: OpenLane
  pipeline:
  - to_float32: true
    type: LoadMultiViewImageFromFiles
  - coord_type: LIDAR
    load_dim: 4
    reduce_beams: 32
    type: LoadPointsFromFile
    use_dim: 4
    with_lidarid: false
  - type: LoadLaneAnnotations3D
    with_lane_3d: true
    with_lane_label_3d: true
  - bot_pct_lim:
    - - 0.0
      - 0.0
    - - 0.14
      - 0.14
    - - 0.22
      - 0.22
    - - 0.22
      - 0.22
    - - 0.17
      - 0.17
    - - 0.22
      - 0.22
    - - 0.22
      - 0.22
    camera_num: 7
    final_dim:
    - 256
    - 704
    is_train: false
    rand_flip: false
    resize_lim:
    - - 0.185
      - 0.185
    - - 0.4
      - 0.4
    - - 0.4
      - 0.4
    - - 0.4
      - 0.4
    - - 0.47
      - 0.47
    - - 0.4
      - 0.4
    - - 0.4
      - 0.4
    rot_lim:
    - 0.0
    - 0.0
    type: ImageAug3D
  - is_train: false
    resize_lim:
    - 1.0
    - 1.0
    rot_lim:
    - 0.0
    - 0.0
    trans_lim: 0.0
    type: GlobalRotScaleTrans
  - point_cloud_range:
    - -81.6
    - -48
    - -1
    - 97.6
    - 48
    - 3.0
    type: PointsRangeFilter
  - mean:
    - 0.485
    - 0.456
    - 0.406
    std:
    - 0.229
    - 0.224
    - 0.225
    type: ImageNormalize
  - enable_visualization: false
    grid_conf:
      xbound:
      - -81.6
      - 97.6
      - 0.4
      ybound:
      - -48.0
      - 48.0
      - 0.4
    lane_classes:
    - background
    - white-solid
    - white-dashed
    - white-double-solid
    - white-solid-dashed
    - white-dashed-solid
    - white-double-dashed
    - yellow-solid
    - yellow-dashed
    - yellow-double-solid
    - yellow-solid-dashed
    - left-yellow-right-white-double-solid
    - road-edge-dashed
    point_cloud_range:
    - -81.6
    - -48
    - -1
    - 97.6
    - 48
    - 3.0
    target_config:
      cls_radius: 1
      gaussian_sigma: 1.0
      generate_instance_ids: true
      heatmap_radius: 2
      max_lanes: 40
      num_points: 120
      reg_radius: 0
      vis_threshold: 0.5
    type: GenerateBEVLaneHeatmapTargets
    visualization_output_dir: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/lane_visualization
  - classes:
    - background
    - white-solid
    - white-dashed
    - white-double-solid
    - white-solid-dashed
    - white-dashed-solid
    - white-double-dashed
    - yellow-solid
    - yellow-dashed
    - yellow-double-solid
    - yellow-solid-dashed
    - left-yellow-right-white-double-solid
    - road-edge-dashed
    type: DefaultFormatBundle3D
  - keys:
    - img
    - points
    - gt_lanes_3d
    - gt_lane_labels
    meta_keys:
    - camera_intrinsics
    - camera2ego
    - lidar2ego
    - lidar2camera
    - camera2lidar
    - lidar2image
    - img_aug_matrix
    - lidar_aug_matrix
    type: Collect3D
find_unused_parameters: false
fp16:
  loss_scale:
    growth_interval: 2000
image_size:
- 256
- 704
input_modality:
  use_camera: true
  use_external: false
  use_lidar: true
  use_map: false
  use_radar: false
lane_classes:
- background
- white-solid
- white-dashed
- white-double-solid
- white-solid-dashed
- white-dashed-solid
- white-double-dashed
- yellow-solid
- yellow-dashed
- yellow-double-solid
- yellow-solid-dashed
- left-yellow-right-white-double-solid
- road-edge-dashed
load_augmented: null
load_dim: 4
load_from: null
log_config:
  hooks:
  - type: TextLoggerHook
  - type: TensorboardLoggerHook
  interval: 50
lr_config:
  min_lr_ratio: 0.0001
  policy: CosineAnnealing
  warmup: linear
  warmup_iters: 500
  warmup_ratio: 0.33333333
max_epochs: 30
model:
  decoder:
    backbone:
      conv_cfg:
        bias: false
        type: Conv2d
      in_channels: 256
      layer_nums:
      - 1
      - 2
      - 3
      - 1
      layer_strides:
      - 1
      - 1
      - 2
      - 2
      norm_cfg:
        eps: 0.001
        momentum: 0.01
        type: BN
      out_channels:
      - 128
      - 64
      - 128
      - 256
      type: SECOND
    neck:
      in_channels:
      - 128
      - 64
      - 128
      - 256
      norm_cfg:
        eps: 0.001
        momentum: 0.01
        type: BN
      out_channel: 128
      out_channels:
      - 128
      - 128
      - 128
      - 128
      type: SECONDFPN
      upsample_cfg:
        bias: false
        type: deconv
      upsample_strides:
      - 1
      - 1
      - 2
      - 2
      use_conv_for_no_stride: true
  encoders:
    camera:
      backbone:
        batch: 1
        gpuType: orin
        img_height: 256
        img_width: 704
        latency: 0.85ms
        model_type: GPUNet-1
        out_indices:
        - 5
        - 10
        - 14
        precision: fp16
        pretrained: /configs/batch1/GV100/0.85ms.pth.tar
        type: GPUNetWrapper
      neck:
        act_cfg:
          inplace: true
          type: ReLU
        in_channels:
        - 96
        - 288
        - 448
        norm_cfg:
          requires_grad: true
          type: BN2d
        num_outs: 3
        out_channels: 256
        start_level: 0
        type: GeneralizedLSSFPN
        upsample_cfg:
          align_corners: false
          mode: bilinear
      vtransform:
        dbound:
        - 1.0
        - 90.0
        - 0.5
        downsample: 2
        feature_size:
        - 32
        - 88
        image_size:
        - 256
        - 704
        in_channels: 256
        out_channels: 80
        type: DepthLSSTransform
        xbound:
        - -81.6
        - 97.6
        - 0.4
        ybound:
        - -48.0
        - 48.0
        - 0.4
        zbound:
        - -1.0
        - 3.0
        - 4.0
    lidar:
      backbone:
        block_type: basicblock
        encoder_channels:
        - - 16
          - 16
          - 32
        - - 32
          - 32
          - 64
        - - 64
          - 64
          - 128
        - - 128
          - 128
        encoder_paddings:
        - - 0
          - 0
          - 1
        - - 0
          - 0
          - - 1
            - 1
            - 0
        - - 0
          - 0
          - 1
        - - 0
          - 0
        encoder_strides:
        - - 0
          - 0
          - 2
        - - 0
          - 0
          - 2
        - - 0
          - 0
          - - 2
            - 2
            - 1
        - - 0
          - 0
        in_channels: 4
        order:
        - conv
        - norm
        - act
        output_channels: 128
        sparse_shape:
        - 1792
        - 960
        - 21
        type: SparseEncoder
      voxelize:
        max_num_points: 10
        max_voxels:
        - 230000
        - 280000
        point_cloud_range:
        - -81.6
        - -48
        - -1
        - 97.6
        - 48
        - 3.0
        voxel_size:
        - 0.1
        - 0.1
        - 0.2
  fuser:
    in_channels:
    - 80
    - 256
    out_channels: 256
    type: ConvFuser
  heads:
    lane:
      clustering_epsilon: 0.4
      clustering_method: dbscan
      clustering_min_points: 4
      embedding_dim: 16
      feat_channels: 64
      grid_conf:
        xbound:
        - -81.6
        - 97.6
        - 0.4
        ybound:
        - -48.0
        - 48.0
        - 0.4
      group_lanes: true
      hm_thres: 0.25
      in_channels: 256
      lane_group_min_distance: 1.0
      loss_cls:
        loss_weight: 0.5
        reduction: mean
        type: CrossEntropyLoss
        use_sigmoid: false
      loss_embedding:
        alpha: 1.0
        beta: 1.5
        delta_d: 2.0
        delta_v: 0.3
        gamma: 0.001
        loss_weight: 1.0
        norm: 2
        reduction: mean
        type: DiscriminativeLoss
      loss_heatmap:
        alpha: 2.0
        gamma: 4.0
        loss_weight: 8.0
        reduction: mean
        type: GaussianFocalLoss
      loss_offset:
        loss_weight: 6.0
        reduction: mean
        type: L1Loss
      loss_z:
        loss_weight: 2.0
        reduction: mean
        type: L1Loss
      max_lanes: 40
      nms_kernel_size: 5
      num_classes: 13
      row_points: 120
      task_area_scope:
        crop_method: slice
        enable_crop: false
        x_range:
        - 0.0
        - 60.0
        y_range:
        - -15.0
        - 15.0
        z_range:
        - -1.0
        - 3.0
      type: BEVLaneHeatmapHead
      use_embedding: true
      use_sigmoid: true
      z_range:
      - -1.0
      - 3.0
    map: null
    object:
      activation: relu
      auxiliary: true
      bbox_coder:
        code_size: 8
        out_size_factor: 8
        pc_range:
        - -81.6
        - -48
        post_center_range:
        - -81.6
        - -48
        - -1
        - 97.6
        - 48
        - 3.0
        score_threshold: 0.1
        type: TransFusionBBoxCoder
        voxel_size:
        - 0.1
        - 0.1
      bn_momentum: 0.1
      common_heads:
        center:
        - 2
        - 2
        dim:
        - 3
        - 2
        height:
        - 1
        - 2
      dropout: 0.1
      ffn_channel: 256
      hidden_channel: 128
      in_channels: 512
      loss_bbox:
        loss_weight: 0.25
        reduction: mean
        type: L1Loss
      loss_cls:
        alpha: 0.25
        gamma: 2.0
        loss_weight: 1.0
        reduction: mean
        type: FocalLoss
        use_sigmoid: true
      loss_heatmap:
        loss_weight: 1.0
        reduction: mean
        type: GaussianFocalLoss
      nms_kernel_size: 3
      num_classes: 10
      num_decoder_layers: 1
      num_heads: 8
      num_proposals: 200
      test_cfg:
        dataset: nuScenes
        grid_size:
        - 1440
        - 1440
        - 41
        nms_type: null
        out_size_factor: 8
        pc_range:
        - -81.6
        - -48
        voxel_size:
        - 0.1
        - 0.1
      train_cfg:
        assigner:
          cls_cost:
            alpha: 0.25
            gamma: 2.0
            type: FocalLossCost
            weight: 0.15
          iou_calculator:
            coordinate: lidar
            type: BboxOverlaps3D
          iou_cost:
            type: IoU3DCost
            weight: 0.25
          reg_cost:
            type: BBoxBEVL1Cost
            weight: 0.25
          type: HungarianAssigner3D
        code_weights:
        - 1.0
        - 1.0
        - 1.0
        - 1.0
        - 1.0
        - 1.0
        dataset: nuScenes
        gaussian_overlap: 0.1
        grid_size:
        - 1440
        - 1440
        - 41
        min_radius: 2
        out_size_factor: 8
        point_cloud_range:
        - -81.6
        - -48
        - -1
        - 97.6
        - 48
        - 3.0
        pos_weight: -1
        voxel_size:
        - 0.1
        - 0.1
        - 0.2
      type: TransFusionHead
  type: BEVFusionForLanes
momentum_config:
  policy: cyclic
optimizer:
  lr: 0.0001
  type: AdamW
  weight_decay: 0.01
optimizer_config:
  grad_clip:
    max_norm: 35
    norm_type: 2
point_cloud_range:
- -81.6
- -48
- -1
- 97.6
- 48
- 3.0
reduce_beams: 32
resume_from: null
run_dir: logs/test_fix
runner:
  max_epochs: 30
  type: EpochBasedRunner
seed: 0
test_pipeline:
- to_float32: true
  type: LoadMultiViewImageFromFiles
- coord_type: LIDAR
  load_dim: 4
  reduce_beams: 32
  type: LoadPointsFromFile
  use_dim: 4
  with_lidarid: false
- type: LoadLaneAnnotations3D
  with_lane_3d: true
  with_lane_label_3d: true
- bot_pct_lim:
  - - 0.0
    - 0.0
  - - 0.14
    - 0.14
  - - 0.22
    - 0.22
  - - 0.22
    - 0.22
  - - 0.17
    - 0.17
  - - 0.22
    - 0.22
  - - 0.22
    - 0.22
  camera_num: 7
  final_dim:
  - 256
  - 704
  is_train: false
  rand_flip: false
  resize_lim:
  - - 0.185
    - 0.185
  - - 0.4
    - 0.4
  - - 0.4
    - 0.4
  - - 0.4
    - 0.4
  - - 0.47
    - 0.47
  - - 0.4
    - 0.4
  - - 0.4
    - 0.4
  rot_lim:
  - 0.0
  - 0.0
  type: ImageAug3D
- is_train: false
  resize_lim:
  - 1.0
  - 1.0
  rot_lim:
  - 0.0
  - 0.0
  trans_lim: 0.0
  type: GlobalRotScaleTrans
- point_cloud_range:
  - -81.6
  - -48
  - -1
  - 97.6
  - 48
  - 3.0
  type: PointsRangeFilter
- mean:
  - 0.485
  - 0.456
  - 0.406
  std:
  - 0.229
  - 0.224
  - 0.225
  type: ImageNormalize
- enable_visualization: false
  grid_conf:
    xbound:
    - -81.6
    - 97.6
    - 0.4
    ybound:
    - -48.0
    - 48.0
    - 0.4
  lane_classes:
  - background
  - white-solid
  - white-dashed
  - white-double-solid
  - white-solid-dashed
  - white-dashed-solid
  - white-double-dashed
  - yellow-solid
  - yellow-dashed
  - yellow-double-solid
  - yellow-solid-dashed
  - left-yellow-right-white-double-solid
  - road-edge-dashed
  point_cloud_range:
  - -81.6
  - -48
  - -1
  - 97.6
  - 48
  - 3.0
  target_config:
    cls_radius: 1
    gaussian_sigma: 1.0
    generate_instance_ids: true
    heatmap_radius: 2
    max_lanes: 40
    num_points: 120
    reg_radius: 0
    vis_threshold: 0.5
  type: GenerateBEVLaneHeatmapTargets
  visualization_output_dir: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/lane_visualization
- classes:
  - background
  - white-solid
  - white-dashed
  - white-double-solid
  - white-solid-dashed
  - white-dashed-solid
  - white-double-dashed
  - yellow-solid
  - yellow-dashed
  - yellow-double-solid
  - yellow-solid-dashed
  - left-yellow-right-white-double-solid
  - road-edge-dashed
  type: DefaultFormatBundle3D
- keys:
  - img
  - points
  - gt_lanes_3d
  - gt_lane_labels
  meta_keys:
  - camera_intrinsics
  - camera2ego
  - lidar2ego
  - lidar2camera
  - camera2lidar
  - lidar2image
  - img_aug_matrix
  - lidar_aug_matrix
  type: Collect3D
train_pipeline:
- to_float32: true
  type: LoadMultiViewImageFromFiles
- coord_type: LIDAR
  load_dim: 4
  reduce_beams: 32
  type: LoadPointsFromFile
  use_dim: 4
  with_lidarid: false
- type: LoadLaneAnnotations3D
  with_lane_3d: true
  with_lane_label_3d: true
- bot_pct_lim:
  - - 0.0
    - 0.0
  - - 0.12
    - 0.15
  - - 0.2
    - 0.25
  - - 0.2
    - 0.25
  - - 0.15
    - 0.2
  - - 0.2
    - 0.25
  - - 0.2
    - 0.25
  camera_num: 7
  final_dim:
  - 256
  - 704
  is_train: true
  rand_flip: false
  resize_lim:
  - - 0.184
    - 0.187
  - - 0.38
    - 0.42
  - - 0.38
    - 0.42
  - - 0.38
    - 0.42
  - - 0.38
    - 0.55
  - - 0.38
    - 0.42
  - - 0.38
    - 0.42
  rot_lim:
  - 0.0
  - 0.0
  type: ImageAug3D
- is_train: true
  resize_lim:
  - 1.0
  - 1.0
  rot_lim:
  - 0.0
  - 0.0
  trans_lim: 0.0
  type: GlobalRotScaleTrans
- point_cloud_range:
  - -81.6
  - -48
  - -1
  - 97.6
  - 48
  - 3.0
  type: PointsRangeFilter
- mean:
  - 0.485
  - 0.456
  - 0.406
  std:
  - 0.229
  - 0.224
  - 0.225
  type: ImageNormalize
- enable_visualization: false
  grid_conf:
    xbound:
    - -81.6
    - 97.6
    - 0.4
    ybound:
    - -48.0
    - 48.0
    - 0.4
  lane_classes:
  - background
  - white-solid
  - white-dashed
  - white-double-solid
  - white-solid-dashed
  - white-dashed-solid
  - white-double-dashed
  - yellow-solid
  - yellow-dashed
  - yellow-double-solid
  - yellow-solid-dashed
  - left-yellow-right-white-double-solid
  - road-edge-dashed
  point_cloud_range:
  - -81.6
  - -48
  - -1
  - 97.6
  - 48
  - 3.0
  target_config:
    cls_radius: 1
    gaussian_sigma: 1.0
    generate_instance_ids: true
    heatmap_radius: 2
    max_lanes: 40
    num_points: 120
    reg_radius: 0
    vis_threshold: 0.5
  type: GenerateBEVLaneHeatmapTargets
  visualization_output_dir: /pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/lane_visualization
- classes:
  - background
  - white-solid
  - white-dashed
  - white-double-solid
  - white-solid-dashed
  - white-dashed-solid
  - white-double-dashed
  - yellow-solid
  - yellow-dashed
  - yellow-double-solid
  - yellow-solid-dashed
  - left-yellow-right-white-double-solid
  - road-edge-dashed
  type: DefaultFormatBundle3D
- keys:
  - img
  - points
  - gt_lanes_3d
  - gt_lane_labels
  - lane_targets
  meta_keys:
  - camera_intrinsics
  - camera2ego
  - lidar2ego
  - lidar2camera
  - camera2lidar
  - lidar2image
  - img_aug_matrix
  - lidar_aug_matrix
  type: Collect3D
use_dim: 4
voxel_size:
- 0.1
- 0.1
- 0.2
