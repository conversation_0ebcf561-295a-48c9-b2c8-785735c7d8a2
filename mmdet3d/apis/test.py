import mmcv
import torch
import numpy as np
from tools.visualizer_tool.visualize_3d_lane_results import visualize_lane_detection_results

def single_gpu_test(model, data_loader):
    model.eval()
    results = []
    dataset = data_loader.dataset
    prog_bar = mmcv.ProgressBar(len(dataset))
    for data in data_loader:
        with torch.no_grad():
            result = model(return_loss=False, rescale=True, **data)

        # logits2label, grid2point
        if "seg_logits_bev" in result[0]:
            # pred_labels_bev = torch.argmax(result[0]["seg_logits_bev"], dim=0)
            pred_labels_bev = result[0]["seg_logits_bev"]
            result[0]["pred_labels_bev"] = pred_labels_bev
            # # output visabel seg result
            lidar_path = data["metas"].data[0][0]["lidar_path"]
            cloud_name = lidar_path.split('/')[-1][:-4]
            points = data["points"].data[0][0].detach().numpy()
            points_grid_ind = data["points_grid_ind"].data[0][0].detach().numpy()
            points_labels = pred_labels_bev[points_grid_ind[:, 0], points_grid_ind[:, 1]].detach().numpy()
            points_labeled = np.hstack((points, points_labels.reshape(-1,1)))
            # np.savetxt("/rbs/szh/test_bev/{}.txt".format(cloud_name), points_labeled, fmt='%.3f')
            result[0]["pred_points_labels"] = points_labels
            result[0]["gt_points_labels"] = data["points_labels"]
        if "pred_lanes" in result[0]:
            lidar_path = data["metas"].data[0][0]["lidar_path"]
            cloud_name = lidar_path.split('/')[-1][:-4]  # todo: fix 100ms
            visualize_lane_detection_results(
                    result[0],
                    img=data["img"].data[0].detach().numpy(),
                    camera_intrinsics=data["camera_intrinsics"].data[0].detach().numpy(),
                    lidar2camera=data["lidar2camera"].data[0].detach().numpy(),
                    point_cloud_range=dataset.point_cloud_range,
                    output_dir="output/lane_vis",
                    prefix=cloud_name,
                    lane_classes=dataset.LANE_CLASSES,
                    vis_modes=('bev'),
                    gt_lanes_3d=data["gt_lanes_3d"].data[0][0],
                    gt_lane_labels=data["gt_lane_labels"].data[0][0].detach().numpy()
                )


        results.extend(result)

        batch_size = len(result)
        for _ in range(batch_size):
            prog_bar.update()
    return results
