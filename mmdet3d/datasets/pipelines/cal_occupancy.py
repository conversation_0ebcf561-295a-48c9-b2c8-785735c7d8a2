def get_grid_indices_for_line(x_min, x_max, y_min, y_max, dx, dy, x1, y1, x2, y2):
    # 计算网格数量
    nx = int(round((x_max - x_min) / dx))
    ny = int(round((y_max - y_min) / dy))
    
    # 实际格网边界（考虑浮点精度）
    actual_x_max = x_min + nx * dx
    actual_y_max = y_min + ny * dy
    
    # 裁剪线段至格网边界内
    seg = clip_line(x1, y1, x2, y2, x_min, actual_x_max, y_min, actual_y_max)
    if seg is None:
        return set()
    x0, y0, x_end, y_end = seg
    
    # 获取起点和终点网格索引
    i0, j0 = get_cell_index(x0, y0, x_min, y_min, dx, dy, nx, ny)
    i_end, j_end = get_cell_index(x_end, y_end, x_min, y_min, dx, dy, nx, ny)
    cells = set()
    cells.add((i0, j0))
    
    # 若起点和终点在同一网格，直接返回
    if i0 == i_end and j0 == j_end:
        return cells
    
    # 计算方向向量
    dir_x = x_end - x0
    dir_y = y_end - y0
    if abs(dir_x) < 1e-10 and abs(dir_y) < 1e-10:
        return cells
    
    # 初始化步进方向和步长参数
    step_x = 1 if dir_x > 0 else -1
    step_y = 1 if dir_y > 0 else -1
    
    # 计算初始t_max_x和t_max_y
    if abs(dir_x) > 1e-10:
        if step_x > 0:
            t_max_x = (x_min + (i0 + 1) * dx - x0) / dir_x
        else:
            t_max_x = (x_min + i0 * dx - x0) / dir_x
        t_delta_x = dx / abs(dir_x)
    else:
        t_max_x = float('inf')
        t_delta_x = float('inf')
    
    if abs(dir_y) > 1e-10:
        if step_y > 0:
            t_max_y = (y_min + (j0 + 1) * dy - y0) / dir_y
        else:
            t_max_y = (y_min + j0 * dy - y0) / dir_y
        t_delta_y = dy / abs(dir_y)
    else:
        t_max_y = float('inf')
        t_delta_y = float('inf')
    
    # 初始化当前网格索引
    i, j = i0, j0
    
    # 网格遍历循环
    while True:
        if t_max_x < t_max_y:
            if t_max_x > 1:
                break
            i += step_x
            if i < 0 or i >= nx or j < 0 or j >= ny:
                break
            cells.add((i, j))
            t_max_x += t_delta_x
        elif t_max_x > t_max_y:
            if t_max_y > 1:
                break
            j += step_y
            if i < 0 or i >= nx or j < 0 or j >= ny:
                break
            cells.add((i, j))
            t_max_y += t_delta_y
        else:  # t_max_x == t_max_y
            if t_max_x > 1:
                break
            i += step_x
            j += step_y
            if i < 0 or i >= nx or j < 0 or j >= ny:
                break
            cells.add((i, j))
            t_max_x += t_delta_x
            t_max_y += t_delta_y
    
    return cells

def clip_line(x1, y1, x2, y2, x_min, x_max, y_min, y_max):
    # 使用Liang-Barsky算法裁剪线段
    dx = x2 - x1
    dy = y2 - y1
    p = [-dx, dx, -dy, dy]
    q = [x1 - x_min, x_max - x1, y1 - y_min, y_max - y1]
    t0, t1 = 0.0, 1.0

    for i in range(4):
        if abs(p[i]) < 1e-10:  # 平行于边界
            if q[i] < 0:
                return None
        else:
            r = q[i] / p[i]
            if p[i] < 0:
                if r > t1:
                    return None
                if r > t0:
                    t0 = r
            else:
                if r < t0:
                    return None
                if r < t1:
                    t1 = r

    if t0 < t1:
        new_x1 = x1 + t0 * dx
        new_y1 = y1 + t0 * dy
        new_x2 = x1 + t1 * dx
        new_y2 = y1 + t1 * dy
        return (new_x1, new_y1, new_x2, new_y2)
    return None

def get_cell_index(x, y, x_min, y_min, dx, dy, nx, ny):
    # 计算网格索引，处理边界情况
    i = min(nx - 1, max(0, int((x - x_min) / dx)))
    j = min(ny - 1, max(0, int((y - y_min) / dy)))
    return i, j


if __name__ == '__main__':
    x_min, x_max = 0.0, 51.2
    y_min, y_max = -9.6, 9.6
    dx, dy = 0.8, 0.8
    x1, y1 = 5.0, 3.54
    x2, y2 = 10.0, 3.67
    occupied_grid = get_grid_indices_for_line(x_min, x_max, y_min, y_max, dx, dy, x1, y1, x2, y2)
    print(occupied_grid)  