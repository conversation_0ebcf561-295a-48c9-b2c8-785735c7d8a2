import numpy as np
import torch
from mmcv.parallel import DataContainer as DC

from mmdet.datasets.builder import PIPELINES
from .lane_visualization import BEVTargetVisualizer


@PIPELINES.register_module()
class GenerateBEVLaneHeatmapTargets:
    """Generate BEV lane targets for heatmap-based lane head.
    
    This transform generates the targets for training the BEVLaneHeatmapHead,
    including:
    - Heatmap of lane points
    - Y-offset for precise localization
    - Z-height values
    - Lane classification
    - Valid mask
    - Instance IDs for instance-level grouping
    """
    
    def __init__(self,
                 point_cloud_range,
                 voxel_size,
                 grid_size=None,
                 lane_classes=None,
                 target_config=None,
                 enable_visualization=False,
                 visualization_output_dir=None):
        """Initialize GenerateBEVLaneHeatmapTargets.
        
        Args:
            point_cloud_range (list): [x_min, y_min, z_min, x_max, y_max, z_max]
            voxel_size (list): [x_res, y_res, z_res]
            grid_size (list, optional): BEV grid size [x_size, y_size]
            lane_classes (list, optional): List of lane class names.
            target_config (dict, optional): Additional config for target generation.
            enable_visualization (bool, optional): Whether to enable BEV visualization.
            visualization_output_dir (str, optional): Output directory for visualizations.
        """
        self.point_cloud_range = point_cloud_range
        self.voxel_size = voxel_size
        
        # Extract BEV grid parameters
        self.x_min, self.y_min, self.z_min = point_cloud_range[:3]
        self.x_max, self.y_max, self.z_max = point_cloud_range[3:]
        self.x_res, self.y_res, self.z_res = voxel_size
        
        # Calculate grid size if not provided
        if grid_size is None:
            self.grid_size = [
                int((self.x_max - self.x_min) / self.x_res),
                int((self.y_max - self.y_min) / self.y_res)
            ]
        else:
            self.grid_size = grid_size
            
        self.nx, self.ny = self.grid_size
        
        # Lane classes
        self.lane_classes = lane_classes
        self.num_classes = len(lane_classes) if lane_classes is not None else 1
        
        # Default target config
        self.target_config = {
            'gaussian_sigma': 1.0,
            'heatmap_radius': 2,
            'cls_radius': 1,  # Smaller radius for classification
            'reg_radius': 0,  # Center point only for regression
            'max_lanes': 20,
            'num_points': 100,
            'generate_instance_ids': False,
        }
        
        # Update with user config if provided
        if target_config is not None:
            self.target_config.update(target_config)

        # Generate x positions for interpolation along forward direction
        self.x_positions = np.linspace(self.x_min, self.x_max, self.target_config['num_points'])
        
        # Initialize visualizer if enabled
        self.visualizer = None
        if enable_visualization and visualization_output_dir:
            try:
                grid_conf = {
                    'point_cloud_range': point_cloud_range,
                    'grid_size': self.grid_size,
                    'bev_resolution': self.x_res
                }
                self.visualizer = BEVTargetVisualizer(visualization_output_dir, grid_conf)
                print(f"[GenerateBEVLaneHeatmapTargets] BEV visualization enabled, output to: {visualization_output_dir}")
            except Exception as e:
                print(f"[GenerateBEVLaneHeatmapTargets] Failed to initialize visualizer: {e}")
                self.visualizer = None
    
    def _get_gaussian_kernel(self, center, sigma=1):
        """Generate a 2D Gaussian kernel.

        Args:
            center (tuple): (x, y) center of the kernel.
            sigma (float): Standard deviation of the Gaussian.

        Returns:
            np.ndarray: 2D Gaussian kernel with shape matching heatmap indexing [x, y].
        """
        x = np.arange(0, self.grid_size[0], dtype=np.float32)
        y = np.arange(0, self.grid_size[1], dtype=np.float32)

        x_grid, y_grid = np.meshgrid(x, y, indexing='xy')
        g = np.exp(-((x_grid - center[0])**2 + (y_grid - center[1])**2) / (2 * sigma**2))
        g = g.T

        return g
    
    def _get_lane_points(self, lane_3d):
        """Extract lane points from 3D lane data.
        
        Args:
            lane_3d (dict): 3D lane data.
            
        Returns:
            tuple: Points (x, y, z) and visibility.
        """
        # Extract 3D lane points and visibility
        if isinstance(lane_3d, dict) and 'points_3d' in lane_3d:
            points_3d = lane_3d['points_3d']
        elif isinstance(lane_3d, torch.Tensor):
            points_3d = lane_3d.cpu().numpy()
        else:
            points_3d = np.array(lane_3d)
        
        # Extract x, y, z coordinates
        if isinstance(points_3d, torch.Tensor):
            lane_x = points_3d[:, 0].cpu().numpy()
            lane_y = points_3d[:, 1].cpu().numpy()
            lane_z = points_3d[:, 2].cpu().numpy()
        else:
            lane_x = points_3d[:, 0]
            lane_y = points_3d[:, 1]
            lane_z = points_3d[:, 2]
        
        # Extract visibility information if available
        if isinstance(lane_3d, dict) and 'visibility' in lane_3d:
            vis = lane_3d['visibility']
            if isinstance(vis, torch.Tensor):
                vis = vis.cpu().numpy()
        else:
            vis = np.ones_like(lane_x)
        
        return lane_x, lane_y, lane_z, vis
    
    def _interpolate_lane_robust(self, lane_x, lane_y, lane_z, vis, x_positions):
        """Robust lane interpolation with improved handling of edge cases.

        Args:
            lane_x (np.ndarray): Lane x-coordinates (forward direction).
            lane_y (np.ndarray): Lane y-coordinates (lateral direction).
            lane_z (np.ndarray): Lane z-coordinates (height).
            vis (np.ndarray): Lane visibility.
            x_positions (np.ndarray): X positions for interpolation.

        Returns:
            tuple: Interpolated y, z coordinates and visibility.
        """
        if len(lane_x) < 2:
            return None, None, None

        # Sort points by x-coordinate
        sort_idx = np.argsort(lane_x)
        lane_x = lane_x[sort_idx]
        lane_y = lane_y[sort_idx]
        lane_z = lane_z[sort_idx]
        vis = vis[sort_idx]

        # Remove duplicate x values to avoid interpolation issues
        unique_mask = np.concatenate(([True], np.diff(lane_x) > 1e-6))
        lane_x = lane_x[unique_mask]
        lane_y = lane_y[unique_mask]
        lane_z = lane_z[unique_mask]
        vis = vis[unique_mask]
        
        if len(lane_x) < 2:
            return None, None, None

        # Check for intersection with sampling range
        x_min, x_max = lane_x.min(), lane_x.max()
        valid_idx = (x_positions >= x_min) & (x_positions <= x_max)
        
        if not np.any(valid_idx):
            return None, None, None

        # Initialize output arrays
        y_full = np.full_like(x_positions, np.nan)
        z_full = np.full_like(x_positions, np.nan)
        vis_full = np.zeros_like(x_positions)

        # Interpolate only for valid x positions
        x_valid = x_positions[valid_idx]
        
        try:
            y_interp = np.interp(x_valid, lane_x, lane_y)
            z_interp = np.interp(x_valid, lane_x, lane_z)
            vis_interp = np.interp(x_valid, lane_x, vis)
            vis_interp = (vis_interp > 0.5).astype(np.float32)
            
            # Fill valid positions
            y_full[valid_idx] = y_interp
            z_full[valid_idx] = z_interp
            vis_full[valid_idx] = vis_interp
            
        except Exception:
            return None, None, None

        return y_full, z_full, vis_full
    
    def _get_line_grid_indices(self, x1, y1, x2, y2):
        """Get grid indices for a line segment using rasterization.
        
        Adapted from cal_occupancy.py for lane line rasterization.
        
        Args:
            x1, y1, x2, y2: Line segment endpoints in world coordinates.
            
        Returns:
            set: Set of (i, j) grid indices that the line passes through.
        """
        # Clip line to grid boundaries
        seg = self._clip_line(x1, y1, x2, y2)
        if seg is None:
            return set()
        x0, y0, x_end, y_end = seg
        
        # Get start and end grid indices
        i0, j0 = self._get_cell_index(x0, y0)
        i_end, j_end = self._get_cell_index(x_end, y_end)
        cells = set()
        cells.add((i0, j0))
        
        if i0 == i_end and j0 == j_end:
            return cells
        
        # Calculate direction vector
        dir_x = x_end - x0
        dir_y = y_end - y0
        if abs(dir_x) < 1e-10 and abs(dir_y) < 1e-10:
            return cells
        
        # Initialize stepping parameters
        step_x = 1 if dir_x > 0 else -1
        step_y = 1 if dir_y > 0 else -1
        
        # Calculate t_max and t_delta for DDA algorithm
        if abs(dir_x) > 1e-10:
            if step_x > 0:
                t_max_x = (self.x_min + (i0 + 1) * self.x_res - x0) / dir_x
            else:
                t_max_x = (self.x_min + i0 * self.x_res - x0) / dir_x
            t_delta_x = self.x_res / abs(dir_x)
        else:
            t_max_x = float('inf')
            t_delta_x = float('inf')
        
        if abs(dir_y) > 1e-10:
            if step_y > 0:
                t_max_y = (self.y_min + (j0 + 1) * self.y_res - y0) / dir_y
            else:
                t_max_y = (self.y_min + j0 * self.y_res - y0) / dir_y
            t_delta_y = self.y_res / abs(dir_y)
        else:
            t_max_y = float('inf')
            t_delta_y = float('inf')
        
        # Grid traversal loop
        i, j = i0, j0
        while True:
            if t_max_x < t_max_y:
                if t_max_x > 1:
                    break
                i += step_x
                if i < 0 or i >= self.nx or j < 0 or j >= self.ny:
                    break
                cells.add((i, j))
                t_max_x += t_delta_x
            elif t_max_x > t_max_y:
                if t_max_y > 1:
                    break
                j += step_y
                if i < 0 or i >= self.nx or j < 0 or j >= self.ny:
                    break
                cells.add((i, j))
                t_max_y += t_delta_y
            else:
                if t_max_x > 1:
                    break
                i += step_x
                j += step_y
                if i < 0 or i >= self.nx or j < 0 or j >= self.ny:
                    break
                cells.add((i, j))
                t_max_x += t_delta_x
                t_max_y += t_delta_y
        
        return cells
    
    def _clip_line(self, x1, y1, x2, y2):
        """Clip line segment to grid boundaries using Liang-Barsky algorithm."""
        dx = x2 - x1
        dy = y2 - y1
        p = [-dx, dx, -dy, dy]
        q = [x1 - self.x_min, self.x_max - x1, y1 - self.y_min, self.y_max - y1]
        t0, t1 = 0.0, 1.0

        for i in range(4):
            if abs(p[i]) < 1e-10:
                if q[i] < 0:
                    return None
            else:
                r = q[i] / p[i]
                if p[i] < 0:
                    if r > t1:
                        return None
                    if r > t0:
                        t0 = r
                else:
                    if r < t0:
                        return None
                    if r < t1:
                        t1 = r

        if t0 < t1:
            new_x1 = x1 + t0 * dx
            new_y1 = y1 + t0 * dy
            new_x2 = x1 + t1 * dx
            new_y2 = y1 + t1 * dy
            return (new_x1, new_y1, new_x2, new_y2)
        return None
    
    def _get_cell_index(self, x, y):
        """Get grid cell index for world coordinates."""
        i = min(self.nx - 1, max(0, int((x - self.x_min) / self.x_res)))
        j = min(self.ny - 1, max(0, int((y - self.y_min) / self.y_res)))
        return i, j
    
    def _convert_to_grid(self, x, y):
        """Convert world coordinates to grid indices.
        
        Args:
            x (np.ndarray): X-coordinates in world space.
            y (np.ndarray): Y-coordinates in world space.
            
        Returns:
            tuple: Grid indices (x_idx, y_idx).
        """
        x_idx = (x - self.x_min) / self.x_res
        y_idx = (y - self.y_min) / self.y_res
        return x_idx, y_idx
    
    def _adjust_lane_labels(self, lane_labels):
        """Adjust lane labels to properly match model expectations.

        Convert 1-indexed annotation labels (1-12) to model labels (1-12) with background=0.
        Now background=0, and lane classes are 1-12.

        Args:
            lane_labels (list): Original lane class labels (1-indexed: 1-12).

        Returns:
            list: Adjusted lane class labels (1-12, with background=0 reserved).
        """
        if not lane_labels:
            return lane_labels

        unique_labels = set(lane_labels)
        min_label = min(lane_labels) if lane_labels else 0
        max_label = max(lane_labels) if lane_labels else 0

        # Check if labels are 1-indexed
        all_greater_than_zero = all(label >= 1 for label in lane_labels)
        has_typical_lane_types = any(label in [1, 2, 7, 8] for label in unique_labels)

        if all_greater_than_zero and max_label <= 12 and has_typical_lane_types:
            # Keep original 1-indexed labels since background=0 is now reserved
            adjusted_labels = []
            for label in lane_labels:
                if label >= 1 and label <= 12:
                    adjusted_labels.append(label)  # Keep original 1-indexed labels
            return adjusted_labels
        else:
            return lane_labels

    def __call__(self, results):
        """Call function to generate BEV lane targets.
        
        Args:
            results (dict): Result dict containing lane annotations.
            
        Returns:
            dict: Updated result dict with lane targets.
        """
        # DEBUG: Print input data information
        print(f"[GenerateBEVLaneHeatmapTargets] === INPUT DEBUG ===")
        print(f"[GenerateBEVLaneHeatmapTargets] Results keys: {list(results.keys())}")
        
        # Get 3D lane annotations
        gt_lanes_3d = results.get('gt_lanes_3d', [])
        gt_lane_labels = results.get('gt_lane_labels', [])
        
        # DEBUG: Print lane data information
        print(f"[GenerateBEVLaneHeatmapTargets] Number of lanes: {len(gt_lanes_3d)}")
        print(f"[GenerateBEVLaneHeatmapTargets] Lane labels: {gt_lane_labels}")
        print(f"[GenerateBEVLaneHeatmapTargets] Grid size: {self.grid_size} (nx={self.nx}, ny={self.ny})")
        print(f"[GenerateBEVLaneHeatmapTargets] Point cloud range: {self.point_cloud_range}")
        print(f"[GenerateBEVLaneHeatmapTargets] Voxel size: {self.voxel_size}")
        print(f"[GenerateBEVLaneHeatmapTargets] Number of classes: {self.num_classes}")
        
        # Adjust lane labels to handle 1-indexed IDs if needed
        if self.lane_classes and gt_lane_labels:
            original_labels = gt_lane_labels.copy()
            gt_lane_labels = self._adjust_lane_labels(gt_lane_labels)
            # print(f"[LABEL_DEBUG] Original labels: {original_labels}")
            # print(f"[LABEL_DEBUG] Adjusted labels: {gt_lane_labels}")
            # print(f"[LABEL_DEBUG] num_classes: {self.num_classes}")
        
        # Validate lane class labels
        valid_lane_labels = []
        valid_lanes_3d = []

        for i, label in enumerate(gt_lane_labels):
            if label < 0 or label >= self.num_classes:
                continue
            valid_lane_labels.append(label)
            valid_lanes_3d.append(gt_lanes_3d[i])
        
        # Use validated lanes for processing
        gt_lanes_3d = valid_lanes_3d
        gt_lane_labels = valid_lane_labels
        
        if len(gt_lanes_3d) == 0:
            # No lane annotations, create empty targets
            print(f"[GenerateBEVLaneHeatmapTargets] No lanes found, creating empty targets")
            heatmap = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            offset = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            z_map = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            mask = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            cls_map = np.zeros((1, self.num_classes, self.nx, self.ny), dtype=np.float32)
            # Initialize background pixels (class 0) to 1.0
            cls_map[0, 0, :, :] = 1.0
            instance_ids = np.zeros((1, self.nx, self.ny), dtype=np.int32)
            print(f"[GenerateBEVLaneHeatmapTargets] Empty targets created with shapes:")
            print(f"[GenerateBEVLaneHeatmapTargets]   heatmap: {heatmap.shape}")
            print(f"[GenerateBEVLaneHeatmapTargets]   cls_map: {cls_map.shape}")
            print(f"[GenerateBEVLaneHeatmapTargets]   mask: {mask.shape}")
        else:
            # Initialize target maps
            print(f"[GenerateBEVLaneHeatmapTargets] Initializing target maps for {len(gt_lanes_3d)} lanes")
            heatmap = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            offset = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            z_map = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            mask = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            cls_map = np.zeros((1, self.num_classes, self.nx, self.ny), dtype=np.float32)
            # Initialize background pixels (class 0) to 1.0
            cls_map[0, 0, :, :] = 1.0
            instance_ids = np.zeros((1, self.nx, self.ny), dtype=np.int32)
            
            print(f"[GenerateBEVLaneHeatmapTargets] Initial target shapes:")
            print(f"[GenerateBEVLaneHeatmapTargets]   heatmap: {heatmap.shape}")
            print(f"[GenerateBEVLaneHeatmapTargets]   offset: {offset.shape}")
            print(f"[GenerateBEVLaneHeatmapTargets]   z_map: {z_map.shape}")
            print(f"[GenerateBEVLaneHeatmapTargets]   mask: {mask.shape}")
            print(f"[GenerateBEVLaneHeatmapTargets]   cls_map: {cls_map.shape}")
            print(f"[GenerateBEVLaneHeatmapTargets]   instance_ids: {instance_ids.shape}")
            
            # Initialize visualization cache if visualizer is enabled
            if self.visualizer is not None:
                self._cached_interpolated_lanes = []
            
            # Process each lane
            for lane_idx, (lane_3d, lane_label) in enumerate(zip(gt_lanes_3d, gt_lane_labels)):
                if lane_idx >= self.target_config['max_lanes']:
                    break
                    
                # Extract lane points
                lane_x, lane_y, lane_z, vis = self._get_lane_points(lane_3d)

                # OPTIMIZATION: Temporarily disable interpolation for performance testing
                # Original interpolation code:
                # y_interp, z_interp, vis_interp = self._interpolate_lane_robust(
                #     lane_x, lane_y, lane_z, vis, self.x_positions)
                
                # Use original lane points directly without interpolation
                y_interp = lane_y
                z_interp = lane_z
                vis_interp = vis
                # Update x_positions to match original lane points
                self.x_positions = lane_x

                # PERFORMANCE FIX: Cache lane data for visualization (adapted for non-interpolated data)
                if self.visualizer is not None and y_interp is not None:
                    lane_data = {
                        'x_positions': lane_x.copy(),  # Use original x positions
                        'y_interp': y_interp.copy(),
                        'z_interp': z_interp.copy(),
                        'vis_interp': vis_interp.copy()
                    }
                    self._cached_interpolated_lanes.append(lane_data)

                if y_interp is None:
                    continue

                # Convert to grid coordinates (using original lane points)
                x_grid, y_grid = self._convert_to_grid(lane_x, y_interp)
                
                # Generate targets for valid points
                valid = ~np.isnan(y_interp) & (vis_interp > 0.5)
                
                if not np.any(valid):
                    continue
                
                # Get valid grid coordinates
                x_valid = x_grid[valid]
                y_valid = y_grid[valid]
                z_valid = z_interp[valid]
                x_pos_valid = lane_x[valid]
                y_pos_valid = y_interp[valid]  # Store actual Y coordinates for offset calculation
                
                # Filter points within grid bounds
                in_bounds = (x_valid >= 0) & (x_valid < self.nx) & (y_valid >= 0) & (y_valid < self.ny)
                
                if not np.any(in_bounds):
                    continue
                
                x_valid = x_valid[in_bounds]
                y_valid = y_valid[in_bounds]
                z_valid = z_valid[in_bounds]
                x_pos_valid = x_pos_valid[in_bounds]
                y_pos_valid = y_pos_valid[in_bounds]  # Also filter Y coordinates
                
                # Convert to integer indices
                x_idx = np.round(x_valid).astype(int)
                y_idx = np.round(y_valid).astype(int)
                
                # Ensure indices are within bounds
                x_idx = np.clip(x_idx, 0, self.nx - 1)
                y_idx = np.clip(y_idx, 0, self.ny - 1)
                
                # Differentiated radius processing strategy
                for i in range(len(x_idx)):
                    xi, yi = x_idx[i], y_idx[i]
                    
                    # Get radius configurations
                    sigma = self.target_config['gaussian_sigma']
                    heatmap_radius = self.target_config['heatmap_radius']
                    cls_radius = self.target_config['cls_radius']
                    reg_radius = self.target_config['reg_radius']
                    
                    # 1. Heatmap: Keep Gaussian kernel processing
                    h_x_start = max(0, xi - heatmap_radius)
                    h_x_end = min(self.nx, xi + heatmap_radius + 1)
                    h_y_start = max(0, yi - heatmap_radius)
                    h_y_end = min(self.ny, yi + heatmap_radius + 1)
                    
                    for kx in range(h_x_start, h_x_end):
                        for ky in range(h_y_start, h_y_end):
                            dist_sq = (kx - xi)**2 + (ky - yi)**2
                            if dist_sq <= heatmap_radius**2:
                                gaussian_val = np.exp(-dist_sq / (2 * sigma**2))
                                heatmap[0, 0, kx, ky] = max(heatmap[0, 0, kx, ky], gaussian_val)
                    
                    # 2. Classification: Use smaller radius or center point only
                    if cls_radius == 0:
                        # Center point only
                        cls_map[0, 0, xi, yi] = 0.0  # Clear background
                        cls_map[0, lane_label, xi, yi] = 1.0  # Set lane class
                        mask[0, 0, xi, yi] = 1.0
                    else:
                        # Smaller radius
                        c_x_start = max(0, xi - cls_radius)
                        c_x_end = min(self.nx, xi + cls_radius + 1)
                        c_y_start = max(0, yi - cls_radius)
                        c_y_end = min(self.ny, yi + cls_radius + 1)
                        
                        for kx in range(c_x_start, c_x_end):
                            for ky in range(c_y_start, c_y_end):
                                dist_sq = (kx - xi)**2 + (ky - yi)**2
                                if dist_sq <= cls_radius**2:
                                    cls_map[0, 0, kx, ky] = 0.0  # Clear background
                                    cls_map[0, lane_label, kx, ky] = 1.0  # Set lane class
                                    mask[0, 0, kx, ky] = 1.0
                    
                    # 3. Regression: Set only at center point or small radius
                    # CRITICAL FIX: Calculate Y-direction offset instead of X-direction
                    # According to BEV-LaneDet methodology, offset should represent lateral deviation
                    if reg_radius == 0:
                        # Center point only
                        # Calculate Y-offset: actual Y position - grid center Y position
                        y_center_world = self.y_min + yi * self.y_res
                        y_actual_world = y_interp[np.where(valid)[0][in_bounds][i]]  # Get actual Y coordinate
                        offset[0, 0, xi, yi] = y_actual_world - y_center_world
                        z_map[0, 0, xi, yi] = z_valid[i]
                    else:
                        # Small radius for regression
                        r_x_start = max(0, xi - reg_radius)
                        r_x_end = min(self.nx, xi + reg_radius + 1)
                        r_y_start = max(0, yi - reg_radius)
                        r_y_end = min(self.ny, yi + reg_radius + 1)
                        
                        for kx in range(r_x_start, r_x_end):
                            for ky in range(r_y_start, r_y_end):
                                dist_sq = (kx - xi)**2 + (ky - yi)**2
                                if dist_sq <= reg_radius**2:
                                    # Calculate Y-offset for each grid cell
                                    ky_center_world = self.y_min + ky * self.y_res
                                    y_actual_world = y_pos_valid[i]
                                    offset[0, 0, kx, ky] = y_actual_world - ky_center_world
                                    z_map[0, 0, kx, ky] = z_valid[i]
                    
                    # Instance IDs (follow classification pattern)
                    if self.target_config['generate_instance_ids']:
                        if cls_radius == 0:
                            instance_ids[0, xi, yi] = lane_idx + 1
                        else:
                            c_x_start = max(0, xi - cls_radius)
                            c_x_end = min(self.nx, xi + cls_radius + 1)
                            c_y_start = max(0, yi - cls_radius)
                            c_y_end = min(self.ny, yi + cls_radius + 1)
                            
                            for kx in range(c_x_start, c_x_end):
                                for ky in range(c_y_start, c_y_end):
                                    dist_sq = (kx - xi)**2 + (ky - yi)**2
                                    if dist_sq <= cls_radius**2:
                                        instance_ids[0, kx, ky] = lane_idx + 1
        
        # DEBUG: 检查cls_map的统计信息 (保留关键统计信息)
        # print(f"[CLS_DEBUG] cls_map shape: {cls_map.shape}, activated pixels: {np.sum(cls_map)}")
        # print(f"[CLS_DEBUG] === CLS_MAP STATISTICS ===")
        # print(f"[CLS_DEBUG] Any non-zero elements in cls_map: {np.any(cls_map > 0)}")
        # print(f"[CLS_DEBUG] Total number of activated pixels in cls_map: {np.sum(cls_map)}")
        
        # 统计每个类别的激活像素数
        # for class_idx in range(cls_map.shape[1]):
        #     class_pixels = np.sum(cls_map[0, class_idx, :, :])
        #     if class_pixels > 0:
        #         print(f"[CLS_DEBUG] Class {class_idx}: {class_pixels} activated pixels")
        
        # print(f"[CLS_DEBUG] cls_map dtype: {cls_map.dtype}")
        # print(f"[CLS_DEBUG] cls_map min/max: [{np.min(cls_map):.3f}, {np.max(cls_map):.3f}]")
        # print(f"[CLS_DEBUG] =========================")
        
        # Generate BEV visualization if enabled
        if self.visualizer is not None and len(gt_lanes_3d) > 0:
            try:
                # Prepare maps for visualization
                maps_for_viz = {
                    'gt_heatmap': heatmap,
                    'gt_mask': mask,
                    'gt_cls': cls_map
                }
                
                # CRITICAL FIX: Robust frame ID extraction
                frame_id = results.get('sample_idx', results.get('frame_id', 'unknown_frame'))
                if hasattr(frame_id, '__iter__') and not isinstance(frame_id, str):
                    try:
                        frame_id = str(frame_id[0]) if len(frame_id) > 0 else 'unknown_frame'
                    except (IndexError, TypeError):
                        frame_id = 'unknown_frame'
                else:
                    frame_id = str(frame_id) if frame_id is not None else 'unknown_frame'
                
                # CRITICAL FIX: Use pre-computed interpolations instead of re-computing
                # The interpolated_lanes_for_viz should be collected during the main loop
                # For now, we'll use the cached data if available
                if hasattr(self, '_cached_interpolated_lanes') and self._cached_interpolated_lanes:
                    interpolated_lanes_for_viz = self._cached_interpolated_lanes
                else:
                    # Fallback: quick collection without full re-computation
                    interpolated_lanes_for_viz = []
                    # print("[WARNING] Using fallback visualization data collection - consider optimizing")
                
                # Generate visualization
                self.visualizer.draw_bev_targets(
                    maps=maps_for_viz,
                    original_lanes=gt_lanes_3d,
                    interpolated_lanes=interpolated_lanes_for_viz,
                    frame_id=frame_id
                )
                
            except Exception as e:
                # print(f"[GenerateBEVLaneHeatmapTargets] Visualization error for frame {frame_id}: {e}")
                # import traceback
                # traceback.print_exc()
                pass
        
        # 直接返回 numpy 数组，让 DefaultFormatBundle3D 统一处理 DataContainer 包装
        print(f"[GenerateBEVLaneHeatmapTargets] Final target shapes:")
        print(f"[GenerateBEVLaneHeatmapTargets]   heatmap: {heatmap.shape}")
        print(f"[GenerateBEVLaneHeatmapTargets]   offset: {offset.shape}")
        print(f"[GenerateBEVLaneHeatmapTargets]   z_map: {z_map.shape}")
        print(f"[GenerateBEVLaneHeatmapTargets]   mask: {mask.shape}")
        print(f"[GenerateBEVLaneHeatmapTargets]   cls_map: {cls_map.shape}")
        
        results['lane_targets'] = {
            'gt_heatmap': heatmap,
            'gt_offset': offset,
            'gt_z': z_map,
            'gt_mask': mask,
            'gt_cls': cls_map,
        }

        if self.target_config['generate_instance_ids']:
            results['lane_targets']['gt_instance_ids'] = instance_ids
            print(f"[GenerateBEVLaneHeatmapTargets]   instance_ids: {instance_ids.shape}")
        
        print(f"[GenerateBEVLaneHeatmapTargets] === OUTPUT DEBUG COMPLETE ===")
        return results
