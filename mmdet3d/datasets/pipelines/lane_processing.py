import numpy as np
import os
import matplotlib.pyplot as plt
from mmdet.datasets.builder import PIPELINES


@PIPELINES.register_module()
class GenerateBEVLaneHeatmapTargets:
    """Generate BEV lane heatmap targets for training.
    
    This class generates training targets for the BEVLaneHeatmapHead, including:
    - Heatmap targets with Gaussian kernels
    - Offset regression targets
    - Z-height regression targets  
    - Classification targets
    - Instance ID targets for embedding learning
    
    The target generation uses the same grid resolution as the head to ensure
    perfect alignment between supervision and prediction.
    """

    def __init__(self,
                 point_cloud_range,
                 grid_conf=None,  # NEW: Use head's grid configuration
                 voxel_size=None,  # DEPRECATED: Only for backward compatibility
                 lane_classes=None,
                 target_config=None,
                 enable_visualization=False,
                 visualization_output_dir=None):
        """Initialize the target generator.
        
        Args:
            point_cloud_range (list): [x_min, y_min, z_min, x_max, y_max, z_max]
            grid_conf (dict): Grid configuration from BEVLaneHeatmapHead
                Should contain 'xbound' and 'ybound' with [min, max, resolution]
            voxel_size (list): DEPRECATED - kept for backward compatibility
            lane_classes (list): List of lane class names
            target_config (dict): Configuration for target generation
            enable_visualization (bool): Whether to enable debug visualization
            visualization_output_dir (str): Directory for saving visualizations
        """
        self.point_cloud_range = point_cloud_range
        self.lane_classes = lane_classes or []
        self.num_classes = len(self.lane_classes)
        self.enable_visualization = enable_visualization
        self.visualization_output_dir = visualization_output_dir
        
        # P0 FIX: Use grid_conf from head if available, fallback to voxel_size with proper BEV resolution
        if grid_conf is not None:
            self.x_min = grid_conf['xbound'][0]
            self.x_max = grid_conf['xbound'][1] 
            self.x_res = grid_conf['xbound'][2]
            self.y_min = grid_conf['ybound'][0]
            self.y_max = grid_conf['ybound'][1]
            self.y_res = grid_conf['ybound'][2]
            
            # Validation: Ensure consistent resolution
            if abs(self.x_res - self.y_res) > 1e-6:
                raise ValueError(f"Inconsistent grid resolution: x_res={self.x_res}, y_res={self.y_res}")
        else:
            # P0 CRITICAL FIX: Infer BEV head resolution from point cloud range
            # The BEV head uses 0.4m resolution based on the config analysis
            if voxel_size is None:
                raise ValueError("Either grid_conf or voxel_size must be provided")
            
            self.x_min, self.y_min = point_cloud_range[0], point_cloud_range[1]
            self.x_max, self.y_max = point_cloud_range[3], point_cloud_range[4]
            
            # CRITICAL RESOLUTION ALIGNMENT: Use 0.4m resolution to match BEV head
            # This fixes the 4x scale mismatch between target generation and head prediction
            self.x_res = self.y_res = 0.4  # Fixed BEV resolution from head config
            
            # Warning for fallback usage is logged only in debug mode
            import logging
            logger = logging.getLogger(__name__)
            logger.debug(f"Using fallback BEV resolution: {self.x_res}m. "
                         f"Consider passing grid_conf explicitly for better maintainability.")
        
        # === P0 CRITICAL FIX: 检测并同步下采样因子 ===
        # 基于BEVFusion实际输出检测到的2倍下采样
        self.downsample_factor = 2
        
        # 使用调整后的分辨率
        self.adjusted_x_res = self.x_res * self.downsample_factor
        self.adjusted_y_res = self.y_res * self.downsample_factor
        
        # Calculate grid dimensions using adjusted resolution
        self.grid_size = [
            int(round((self.x_max - self.x_min) / self.adjusted_x_res)),
            int(round((self.y_max - self.y_min) / self.adjusted_y_res))
        ]
        
        # Parse target configuration
        if target_config is None:
            target_config = {}
        
        self.gaussian_sigma = target_config.get('gaussian_sigma', 1.0)
        self.heatmap_radius = target_config.get('heatmap_radius', 2)
        self.cls_radius = target_config.get('cls_radius', 1) 
        self.reg_radius = target_config.get('reg_radius', 0)
        self.max_lanes = target_config.get('max_lanes', 40)
        self.num_points = target_config.get('num_points', 120)
        self.generate_instance_ids = target_config.get('generate_instance_ids', True)
        self.vis_threshold = target_config.get('vis_threshold', 0.5)
        
        # P0 FIX: Pre-compute fixed x_positions and never modify them (batch-safe)
        self.x_positions = np.linspace(
            self.x_min, self.x_max, self.num_points, dtype=np.float32
        )
        
        # Pre-compute Gaussian kernel for heatmap generation
        self.gaussian_kernel = self._get_gaussian_kernel()

    def _get_gaussian_kernel(self):
        """Pre-compute Gaussian kernel for heatmap generation."""
        kernel_size = 2 * self.heatmap_radius + 1
        kernel = np.zeros((kernel_size, kernel_size), dtype=np.float32)
        center = self.heatmap_radius
        
        for i in range(kernel_size):
            for j in range(kernel_size):
                distance = np.sqrt((i - center) ** 2 + (j - center) ** 2)
                kernel[i, j] = np.exp(-(distance ** 2) / (2 * self.gaussian_sigma ** 2))
        
        return kernel

    def _get_lane_points(self, lane_3d):
        """Extract lane points from 3D lane annotation.
        
        Args:
            lane_3d: Lane annotation - can be numpy array or dict with 'points' and 'visibility' fields
            
        Returns:
            tuple: (lane_x, lane_y, lane_z, visibility) arrays
        """
        # 处理不同的输入格式
        if isinstance(lane_3d, dict):
            # 字典格式：包含'points'和'visibility'字段
            points = np.array(lane_3d['points'], dtype=np.float32)
            visibility = np.array(lane_3d.get('visibility', [1.0] * len(points)), dtype=np.float32)
        elif isinstance(lane_3d, (np.ndarray, list)):
            # 数组格式：直接是点坐标数组
            points = np.array(lane_3d, dtype=np.float32)
            visibility = np.ones(len(points), dtype=np.float32)  # 默认所有点都可见
        else:
            raise ValueError(f"Unsupported lane_3d format: {type(lane_3d)}")
        
        if len(points) == 0:
            return np.array([]), np.array([]), np.array([]), np.array([])
        
        lane_x = points[:, 0]
        lane_y = points[:, 1] 
        lane_z = points[:, 2] if points.shape[1] > 2 else np.zeros_like(lane_x)
        
        return lane_x, lane_y, lane_z, visibility

    def _interpolate_lane_robust(self, lane_x, lane_y, lane_z, visibility):
        """P0 FIX: Robust lane interpolation with proper bounds checking.
        
        This method interpolates lane points at fixed x-positions to provide
        dense and uniform supervision signals for training.
        
        Args:
            lane_x, lane_y, lane_z, visibility: Original lane data arrays
            
        Returns:
            tuple: Interpolated (y, z, vis) at fixed x_positions
        """
        if len(lane_x) < 2:
            # Not enough points for interpolation
            return np.full_like(self.x_positions, np.nan), \
                   np.full_like(self.x_positions, np.nan), \
                   np.zeros_like(self.x_positions)
        
        # P0 FIX: Clip x_positions to lane bounds to prevent extrapolation
        x_min_lane, x_max_lane = np.min(lane_x), np.max(lane_x)
        valid_x_mask = (self.x_positions >= x_min_lane) & (self.x_positions <= x_max_lane)
        
        # Initialize output arrays
        y_interp = np.full_like(self.x_positions, np.nan)
        z_interp = np.full_like(self.x_positions, np.nan) 
        vis_interp = np.zeros_like(self.x_positions)
        
        if np.any(valid_x_mask):
            valid_x_positions = self.x_positions[valid_x_mask]
            
            # Sort by x for interpolation
            sort_indices = np.argsort(lane_x)
            sorted_x = lane_x[sort_indices]
            sorted_y = lane_y[sort_indices]
            sorted_z = lane_z[sort_indices]
            sorted_vis = visibility[sort_indices]
            
            # Interpolate y, z, and visibility
            y_interp[valid_x_mask] = np.interp(valid_x_positions, sorted_x, sorted_y)
            z_interp[valid_x_mask] = np.interp(valid_x_positions, sorted_x, sorted_z)
            vis_interp[valid_x_mask] = np.interp(valid_x_positions, sorted_x, sorted_vis)
        
        return y_interp, z_interp, vis_interp

    def _initialize_target_maps(self, batch_size=1):
        """P1 REFACTOR: Initialize all target maps.
        
        Returns:
            dict: Dictionary containing all initialized target tensors
        """
        H, W = self.grid_size
        
        targets = {
            'heatmap': np.zeros((batch_size, 1, H, W), dtype=np.float32),
            'offset': np.zeros((batch_size, 1, H, W), dtype=np.float32),  # ✅ 单通道：y方向
            'z_map': np.zeros((batch_size, 1, H, W), dtype=np.float32),
            'mask': np.zeros((batch_size, 1, H, W), dtype=np.float32),
            'cls_map': np.zeros((batch_size, self.num_classes, H, W), dtype=np.int64),
        }
        
        if self.generate_instance_ids:
            targets['instance_ids'] = np.zeros((batch_size, 1, H, W), dtype=np.int64)
            
        return targets

    def _process_single_lane(self, lane_3d, lane_label, lane_instance_id, targets, batch_idx=0):
        """P1 REFACTOR: Process a single lane and update target maps.
        
        Args:
            lane_3d: 3D lane annotation
            lane_label: Lane class label
            lane_instance_id: Unique instance ID for this lane
            targets: Target maps dictionary to update
            batch_idx: Batch index
        """
        # Extract and interpolate lane points
        lane_x, lane_y, lane_z, visibility = self._get_lane_points(lane_3d)
        
        if len(lane_x) == 0:
            return
        
        # P0 FIX: Use robust interpolation with fixed x_positions (batch-safe)
        y_interp, z_interp, vis_interp = self._interpolate_lane_robust(
            lane_x, lane_y, lane_z, visibility
        )
        
        # Filter points by visibility threshold and valid interpolation
        valid_mask = (vis_interp >= self.vis_threshold) & ~np.isnan(y_interp)
        
        if not np.any(valid_mask):
            return
        
        valid_x = self.x_positions[valid_mask]
        valid_y = y_interp[valid_mask]
        valid_z = z_interp[valid_mask]
        
        # Convert to grid coordinates using adjusted resolution
        grid_x = ((valid_x - self.x_min) / self.adjusted_x_res).astype(np.int32)
        grid_y = ((valid_y - self.y_min) / self.adjusted_y_res).astype(np.int32)
        
        # Filter points within grid bounds
        H, W = self.grid_size
        in_bounds = (grid_x >= 0) & (grid_x < H) & (grid_y >= 0) & (grid_y < W)
        
        # 边界检查警告信息
        out_of_bounds_count = np.sum(~in_bounds)
        if out_of_bounds_count > 0:
            print(f"[LANE_PROCESSING_WARN] Lane {lane_instance_id}: {out_of_bounds_count} points out of grid bounds")
        
        if not np.any(in_bounds):
            print(f"[LANE_PROCESSING_WARN] Lane {lane_instance_id}: All points are out of grid bounds")
            return
        
        grid_x = grid_x[in_bounds]
        grid_y = grid_y[in_bounds]
        valid_x = valid_x[in_bounds]
        valid_y = valid_y[in_bounds] 
        valid_z = valid_z[in_bounds]
        
        # Generate targets for each valid point
        for i, (gx, gy, real_x, real_y, real_z) in enumerate(
            zip(grid_x, grid_y, valid_x, valid_y, valid_z)
        ):
            self._add_heatmap_target(targets, batch_idx, gx, gy, self.heatmap_radius)
            self._add_classification_target(targets, batch_idx, gx, gy, lane_label, self.cls_radius)
            self._add_regression_target(targets, batch_idx, gx, gy, real_x, real_y, real_z, self.reg_radius)
            
            if self.generate_instance_ids:
                self._add_instance_target(targets, batch_idx, gx, gy, lane_instance_id, self.reg_radius)

    def _add_heatmap_target(self, targets, batch_idx, center_x, center_y, radius):
        """Add Gaussian heatmap target around the specified center."""
        H, W = self.grid_size
        
        # Apply Gaussian kernel
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                x, y = center_x + dx, center_y + dy
                if 0 <= x < H and 0 <= y < W:
                    kernel_x, kernel_y = dx + radius, dy + radius
                    if kernel_x < self.gaussian_kernel.shape[0] and kernel_y < self.gaussian_kernel.shape[1]:
                        targets['heatmap'][batch_idx, 0, x, y] = max(
                            targets['heatmap'][batch_idx, 0, x, y],
                            self.gaussian_kernel[kernel_x, kernel_y]
                        )

    def _add_classification_target(self, targets, batch_idx, center_x, center_y, lane_label, radius):
        """Add classification target in the specified radius."""
        H, W = self.grid_size
        
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                x, y = center_x + dx, center_y + dy
                if 0 <= x < H and 0 <= y < W:
                    # Set the corresponding class channel to 1
                    targets['cls_map'][batch_idx, lane_label, x, y] = 1

    def _add_regression_target(self, targets, batch_idx, center_x, center_y, real_x, real_y, real_z, radius):
        """Add regression targets (offset and z) at the exact center point."""
        H, W = self.grid_size
        
        # Only add regression target at the exact center (radius=0 typically)
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                x, y = center_x + dx, center_y + dy
                if 0 <= x < H and 0 <= y < W:
                    # Calculate offset from grid center to real position using adjusted resolution
                    grid_center_x = self.x_min + (x + 0.5) * self.adjusted_x_res
                    grid_center_y = self.y_min + (y + 0.5) * self.adjusted_y_res
                    
                    # ✅ 仅计算y方向偏移（横向偏移）
                    offset_y = (real_y - grid_center_y) / self.adjusted_y_res
                    
                    # Clip to ensure within valid range
                    offset_y = np.clip(offset_y, -0.5, 0.5)
                    
                    targets['offset'][batch_idx, 0, x, y] = offset_y
                    targets['z_map'][batch_idx, 0, x, y] = real_z
                    targets['mask'][batch_idx, 0, x, y] = 1.0

    def _add_instance_target(self, targets, batch_idx, center_x, center_y, instance_id, radius):
        """Add instance ID target for embedding learning."""
        H, W = self.grid_size
        
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                x, y = center_x + dx, center_y + dy
                if 0 <= x < H and 0 <= y < W:
                    targets['instance_ids'][batch_idx, 0, x, y] = instance_id

    def __call__(self, results):
        """Main processing function to generate lane targets.
        
        Args:
            results: Data dictionary containing lane annotations
            
        Returns:
            dict: Updated results with lane_targets
        """
        # Extract lane annotations
        gt_lanes_3d = results.get('gt_lanes_3d', [])
        gt_lane_labels = results.get('gt_lane_labels', [])
        
        if len(gt_lanes_3d) == 0:
            # No lanes in this sample
            targets = self._initialize_target_maps()
            results['lane_targets'] = {
                'gt_heatmap': targets['heatmap'],
                'gt_offset': targets['offset'],
                'gt_z': targets['z_map'],  # CRITICAL FIX: Use 'gt_z' instead of 'gt_height'
                'gt_mask': targets['mask'],
                'gt_cls': targets['cls_map'],
            }
            if self.generate_instance_ids and 'instance_ids' in targets:
                results['lane_targets']['gt_instance_ids'] = targets['instance_ids']
            return results
        
        # Initialize target maps
        targets = self._initialize_target_maps()
        
        # Process each lane (batch-safe processing)
        for lane_idx, (lane_3d, lane_label) in enumerate(zip(gt_lanes_3d, gt_lane_labels)):
            lane_instance_id = lane_idx + 1  # Instance IDs start from 1 (0 = background)
            self._process_single_lane(lane_3d, lane_label, lane_instance_id, targets)
        
        # 简化的目标张量统计信息
        total_lanes = len(gt_lanes_3d)
        heatmap_points = np.count_nonzero(targets['heatmap'])
        mask_points = np.count_nonzero(targets['mask'])
        print(f"[LANE_PROCESSING] Processed {total_lanes} lanes -> heatmap_points: {heatmap_points}, mask_points: {mask_points}")
        
        # CRITICAL FIX: Use consistent field names that match bev_lane_heatmap_head.py expectations
        results['lane_targets'] = {
            'gt_heatmap': targets['heatmap'],
            'gt_offset': targets['offset'],
            'gt_z': targets['z_map'],  # CRITICAL FIX: Use 'gt_z' instead of 'gt_height' to match head expectations
            'gt_mask': targets['mask'],
            'gt_cls': targets['cls_map'],
        }
        
        if self.generate_instance_ids and 'instance_ids' in targets:
            results['lane_targets']['gt_instance_ids'] = targets['instance_ids']
        
        # 验证关键目标张量
        for key in ['gt_heatmap', 'gt_mask']:
            if np.count_nonzero(results['lane_targets'][key]) == 0:
                print(f"[LANE_PROCESSING_WARN] {key} is all zeros - this may indicate a problem!")
        
        # Optional visualization
        if self.enable_visualization and self.visualization_output_dir:
            self._visualize_targets(targets, results)
        
        return results

    def _visualize_targets(self, targets, results):
        """Generate debug visualization of the targets."""
        if not os.path.exists(self.visualization_output_dir):
            os.makedirs(self.visualization_output_dir)
        
        # Create visualization
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Heatmap
        axes[0, 0].imshow(targets['heatmap'][0, 0], cmap='hot', origin='lower')
        axes[0, 0].set_title('Heatmap')
        
        # Classification map
        axes[0, 1].imshow(targets['cls_map'][0, 0], cmap='tab10', origin='lower')
        axes[0, 1].set_title('Classification Map')
        
        # Regression mask
        axes[1, 0].imshow(targets['mask'][0, 0], cmap='gray', origin='lower')
        axes[1, 0].set_title('Regression Mask')
        
        # Instance IDs (if available)
        if 'instance_ids' in targets:
            axes[1, 1].imshow(targets['instance_ids'][0, 0], cmap='tab20', origin='lower')
            axes[1, 1].set_title('Instance IDs')
        else:
            axes[1, 1].axis('off')
        
        # Save visualization
        sample_token = results.get('sample_idx', 'unknown')
        save_path = os.path.join(self.visualization_output_dir, f'targets_{sample_token}.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()

    def __repr__(self):
        """String representation of the class."""
        return (f'{self.__class__.__name__}('
                f'grid_size={self.grid_size}, '
                f'num_points={self.num_points}, '
                f'vis_threshold={self.vis_threshold}, '
                f'x_res={self.x_res}m, y_res={self.y_res}m, '
                f'BEV_range=[{self.x_min:.1f}, {self.y_min:.1f}] to [{self.x_max:.1f}, {self.y_max:.1f}])')
