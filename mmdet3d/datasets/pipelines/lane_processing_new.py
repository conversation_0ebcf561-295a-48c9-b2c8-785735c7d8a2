import numpy as np
import os
import matplotlib.pyplot as plt
from mmdet.datasets.builder import PIPELINES


@PIPELINES.register_module()
class GenerateBEVLaneHeatmapTargets:
    """Generate BEV lane targets for heatmap-based lane head."""

    def __init__(self,
                 point_cloud_range,
                 grid_conf=None,
                 voxel_size=None,
                 lane_classes=None,
                 target_config=None,
                 enable_visualization=False,
                 visualization_output_dir=None):
        self.point_cloud_range = point_cloud_range
        self.lane_classes = lane_classes or []
        self.num_classes = len(self.lane_classes) if lane_classes is not None else 1
        self.enable_visualization = enable_visualization
        self.visualization_output_dir = visualization_output_dir

        # 解析BEV网格参数
        if grid_conf is not None:
            self.x_min = grid_conf['xbound'][0]
            self.x_max = grid_conf['xbound'][1]
            self.x_res = grid_conf['xbound'][2]
            self.y_min = grid_conf['ybound'][0]
            self.y_max = grid_conf['ybound'][1]
            self.y_res = grid_conf['ybound'][2]
        else:
            self.x_min, self.y_min = point_cloud_range[0], point_cloud_range[1]
            self.x_max, self.y_max = point_cloud_range[3], point_cloud_range[4]
            self.x_res = self.y_res = 0.4  # 默认BEV分辨率

        # 下采样因子（适配实际BEV特征）
        self.downsample_factor = 2
        self.adjusted_x_res = self.x_res * self.downsample_factor
        self.adjusted_y_res = self.y_res * self.downsample_factor

        # 网格尺寸（使用round避免浮点误差）
        self.grid_size = [
            int(round((self.x_max - self.x_min) / self.adjusted_x_res)),
            int(round((self.y_max - self.y_min) / self.adjusted_y_res))
        ]
        self.nx, self.ny = self.grid_size

        # 目标配置
        self.target_config = {
            'gaussian_sigma': 1.0,
            'heatmap_radius': 2,
            'cls_radius': 1,
            'reg_radius': 0,
            'max_lanes': 40,
            'num_points': 120,
            'generate_instance_ids': True,
            'vis_threshold': 0.5,
        }
        if target_config is not None:
            self.target_config.update(target_config)

        self.x_positions = np.linspace(self.x_min, self.x_max, self.target_config['num_points'], dtype=np.float32)
        self.gaussian_kernel = self._get_gaussian_kernel()

    def _get_gaussian_kernel(self):
        kernel_size = 2 * self.target_config['heatmap_radius'] + 1
        kernel = np.zeros((kernel_size, kernel_size), dtype=np.float32)
        center = self.target_config['heatmap_radius']
        for i in range(kernel_size):
            for j in range(kernel_size):
                dist = np.sqrt((i - center) ** 2 + (j - center) ** 2)
                kernel[i, j] = np.exp(-(dist ** 2) / (2 * self.target_config['gaussian_sigma'] ** 2))
        return kernel

    def _get_lane_points(self, lane_3d):
        if isinstance(lane_3d, dict) and 'points' in lane_3d:
            points = np.array(lane_3d['points'], dtype=np.float32)
            vis = np.array(lane_3d.get('visibility', [1.0] * len(points)), dtype=np.float32)
        else:
            points = np.array(lane_3d, dtype=np.float32)
            vis = np.ones(len(points), dtype=np.float32)
        return points[:, 0], points[:, 1], points[:, 2] if points.shape[1] > 2 else np.zeros_like(points[:, 0]), vis

    def _interpolate_lane_robust(self, lane_x, lane_y, lane_z, visibility):
        if len(lane_x) < 2:
            return None, None, None
        sort_idx = np.argsort(lane_x)
        lane_x, lane_y, lane_z, visibility = lane_x[sort_idx], lane_y[sort_idx], lane_z[sort_idx], visibility[sort_idx]

        x_min, x_max = lane_x.min(), lane_x.max()
        valid_mask = (self.x_positions >= x_min) & (self.x_positions <= x_max)
        if not np.any(valid_mask):
            return None, None, None

        y_interp = np.full_like(self.x_positions, np.nan)
        z_interp = np.full_like(self.x_positions, np.nan)
        vis_interp = np.zeros_like(self.x_positions)

        valid_x = self.x_positions[valid_mask]
        y_interp[valid_mask] = np.interp(valid_x, lane_x, lane_y)
        z_interp[valid_mask] = np.interp(valid_x, lane_x, lane_z)
        vis_interp[valid_mask] = np.interp(valid_x, lane_x, visibility)
        return y_interp, z_interp, vis_interp

    def _initialize_target_maps(self, batch_size=1):
        return {
            'heatmap': np.zeros((batch_size, 1, self.nx, self.ny), dtype=np.float32),
            'offset': np.zeros((batch_size, 1, self.nx, self.ny), dtype=np.float32),  # ✅ 单通道：y方向偏移
            'z_map': np.zeros((batch_size, 1, self.nx, self.ny), dtype=np.float32),
            'mask': np.zeros((batch_size, 1, self.nx, self.ny), dtype=np.float32),
            'cls_map': np.zeros((batch_size, self.num_classes, self.nx, self.ny), dtype=np.float32),
            'instance_ids': np.zeros((batch_size, 1, self.nx, self.ny), dtype=np.int64) if self.target_config['generate_instance_ids'] else None
        }

    def _add_regression_target(self, targets, batch_idx, center_x, center_y, real_x, real_y, real_z, radius):
        H, W = self.grid_size
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                x, y = center_x + dx, center_y + dy
                if 0 <= x < H and 0 < y < W:
                    # ✅ 仅计算y方向偏移（横向偏移）
                    grid_center_y = self.y_min + (y + 0.5) * self.adjusted_y_res
                    offset_y = (real_y - grid_center_y) / self.adjusted_y_res
                    targets['offset'][batch_idx, 0, x, y] = np.clip(offset_y, -0.5, 0.5)
                    targets['z_map'][batch_idx, 0, x, y] = real_z
                    targets['mask'][batch_idx, 0, x, y] = 1.0

    def _add_heatmap_target(self, targets, batch_idx, center_x, center_y, radius):
        H, W = self.grid_size
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                x, y = center_x + dx, center_y + dy
                if 0 <= x < H and 0 <= y < W:
                    dist = np.sqrt(dx ** 2 + dy ** 2)
                    val = np.exp(-(dist ** 2) / (2 * self.target_config['gaussian_sigma'] ** 2))
                    targets['heatmap'][batch_idx, 0, x, y] = max(targets['heatmap'][batch_idx, 0, x, y], val)

    def _add_classification_target(self, targets, batch_idx, center_x, center_y, lane_label, radius):
        H, W = self.grid_size
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                x, y = center_x + dx, center_y + dy
                if 0 <= x < H and 0 <= y < W:
                    targets['cls_map'][batch_idx, 0, x, y] = 0.0  # 清除背景
                    targets['cls_map'][batch_idx, lane_label, x, y] = 1.0

    def __call__(self, results):
        gt_lanes_3d = results.get('gt_lanes_3d', [])
        gt_lane_labels = results.get('gt_lane_labels', [])

        if len(gt_lanes_3d) == 0:
            targets = self._initialize_target_maps()
            results['lane_targets'] = {
                'gt_heatmap': targets['heatmap'],
                'gt_offset': targets['offset'],
                'gt_z': targets['z_map'],
                'gt_mask': targets['mask'],
                'gt_cls': targets['cls_map'],
            }
            if targets['instance_ids'] is not None:
                results['lane_targets']['gt_instance_ids'] = targets['instance_ids']
            return results

        targets = self._initialize_target_maps()
        for lane_idx, (lane_3d, lane_label) in enumerate(zip(gt_lanes_3d, gt_lane_labels)):
            if lane_idx >= self.target_config['max_lanes']:
                break

            lane_x, lane_y, lane_z, vis = self._get_lane_points(lane_3d)
            y_interp, z_interp, vis_interp = self._interpolate_lane_robust(lane_x, lane_y, lane_z, vis)

            if y_interp is None:
                continue

            valid_mask = ~np.isnan(y_interp) & (vis_interp > self.vis_threshold)
            if not np.any(valid_mask):
                continue

            valid_x = self.x_positions[valid_mask]
            valid_y = y_interp[valid_mask]
            valid_z = z_interp[valid_mask]

            grid_x = ((valid_x - self.x_min) / self.adjusted_x_res).astype(np.int32)
            grid_y = ((valid_y - self.y_min) / self.adjusted_y_res).astype(np.int32)

            in_bounds = (grid_x >= 0) & (grid_x < self.nx) & (grid_y >= 0) & (grid_y < self.ny)
            if not np.any(in_bounds):
                continue

            grid_x = grid_x[in_bounds]
            grid_y = grid_y[in_bounds]
            valid_x = valid_x[in_bounds]
            valid_y = valid_y[in_bounds]
            valid_z = valid_z[in_bounds]

            for xi, yi, rx, ry, rz in zip(grid_x, grid_y, valid_x, valid_y, valid_z):
                self._add_heatmap_target(targets, 0, xi, yi, self.target_config['heatmap_radius'])
                self._add_classification_target(targets, 0, xi, yi, lane_label, self.target_config['cls_radius'])
                self._add_regression_target(targets, 0, xi, yi, rx, ry, rz, self.target_config['reg_radius'])
                if targets['instance_ids'] is not None:
                    targets['instance_ids'][0, 0, xi, yi] = lane_idx + 1

        results['lane_targets'] = {
            'gt_heatmap': targets['heatmap'],
            'gt_offset': targets['offset'],
            'gt_z': targets['z_map'],
            'gt_mask': targets['mask'],
            'gt_cls': targets['cls_map'],
        }
        if targets['instance_ids'] is not None:
            results['lane_targets']['gt_instance_ids'] = targets['instance_ids']
        return results