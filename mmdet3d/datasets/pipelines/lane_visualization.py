import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import Normalize
import matplotlib.gridspec as gridspec
from typing import Dict, List, Tuple, Optional, Union


class BEVTargetVisualizer:
    """Bird's-Eye-View target visualizer for lane detection.
    
    This class generates detailed BEV visualizations of ground truth targets
    produced by GenerateBEVLaneHeatmapTargets, showing BEV grids, generated
    lane data, and raw ground truth for comparison.
    """
    
    def __init__(self, output_dir: str, grid_conf: Dict):
        """Initialize BEVTargetVisualizer with Matplotlib configuration.
        
        Args:
            output_dir (str): Directory path where visualization images will be saved.
            grid_conf (dict): BEV grid configuration containing point_cloud_range,
                             grid_size, and bev_resolution.
        """
        self.output_dir = output_dir
        self.grid_conf = grid_conf
        
        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Parse grid configuration
        point_cloud_range = grid_conf.get('point_cloud_range', [0.0, -12.0, -1.0, 60.0, 12.0, 3.0])
        self.x_min, self.y_min, self.z_min = point_cloud_range[:3]
        self.x_max, self.y_max, self.z_max = point_cloud_range[3:]
        
        # Grid parameters
        if 'grid_size' in grid_conf:
            self.nx, self.ny = grid_conf['grid_size']
        else:
            # Calculate from bev_resolution if grid_size not provided
            bev_resolution = grid_conf.get('bev_resolution', 0.5)
            self.nx = int((self.x_max - self.x_min) / bev_resolution)
            self.ny = int((self.y_max - self.y_min) / bev_resolution)
        
        # Resolution
        self.x_res = (self.x_max - self.x_min) / self.nx
        self.y_res = (self.y_max - self.y_min) / self.ny
        
        # Matplotlib style configuration
        self.style = {
            'figure_size': (12, 10),           # Publication quality size
            'dpi': 150,                        # High resolution
            'background_color': 'white',       # Clean white background
            'grid_color': '#AAAAAA',          # Darker gray grid for better visibility
            'major_grid_color': '#000000',     # Black for major grid lines
            'heatmap_color': 'blue',           # Blue for heatmap regions
            'heatmap_alpha': 0.3,              # Semi-transparent overlay
            'interpolated_color': '#00AA00',   # Green for interpolated lanes
            'interpolated_linewidth': 2.5,     # Medium line width
            'original_gt_color': '#FF0000',    # Red for original GT
            'original_gt_size': 25,            # Point size for scatter plot
            'text_color': 'black',             # Black text for readability
            'font_size': 12,                   # Standard font size
            'title_font_size': 14,             # Larger title font
            'axis_label_size': 12,             # Axis label font size
        }
        
        # Set matplotlib global parameters for publication quality
        plt.rcParams.update({
            'font.size': self.style['font_size'],
            'axes.labelsize': self.style['axis_label_size'],
            'axes.titlesize': self.style['title_font_size'],
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'legend.fontsize': 10,
            'figure.titlesize': self.style['title_font_size']
        })
        
        print(f"[BEVTargetVisualizer] Initialized with output_dir: {self.output_dir}")
        print(f"[BEVTargetVisualizer] Grid: {self.nx}x{self.ny}, Range: [{self.x_min:.1f}, {self.y_min:.1f}] to [{self.x_max:.1f}, {self.y_max:.1f}]")
        print(f"[BEVTargetVisualizer] Resolution: {self.x_res:.2f}m x {self.y_res:.2f}m")
        print(f"[BEVTargetVisualizer] Using Matplotlib-based publication-quality rendering")    
    def _prepare_heatmap_data(self, heatmap: np.ndarray, mask: np.ndarray) -> Optional[np.ndarray]:
        """Prepare heatmap/mask data for discrete cell-based visualization.
        
        Args:
            heatmap: Heatmap array from BEV targets
            mask: Mask array from BEV targets
            
        Returns:
            2D binary array indicating occupied cells, or None if no valid data
        """
        # Use mask if available, otherwise use heatmap with threshold
        if mask is not None and mask.size > 0:
            # Extract 2D mask
            if mask.ndim == 4:  # Shape: (1, 1, nx, ny)
                mask_2d = mask[0, 0]
            elif mask.ndim == 3:  # Shape: (1, nx, ny)
                mask_2d = mask[0]
            else:  # Shape: (nx, ny)
                mask_2d = mask
            
            occupancy_map = (mask_2d > 0.5).astype(np.float32)
        else:
            # Use heatmap with threshold
            if heatmap is not None and heatmap.size > 0:
                if heatmap.ndim == 4:  # Shape: (1, 1, nx, ny)
                    heatmap_2d = heatmap[0, 0]
                elif heatmap.ndim == 3:  # Shape: (1, nx, ny)
                    heatmap_2d = heatmap[0]
                else:  # Shape: (nx, ny)
                    heatmap_2d = heatmap
                
                occupancy_map = (heatmap_2d > 0.1).astype(np.float32)
            else:
                return None
        
        # Return binary occupancy map (don't convert zeros to NaN for discrete visualization)
        return occupancy_map
    
    def draw_bev_targets(self, maps: Dict, original_lanes: List, 
                        interpolated_lanes: List[Dict], frame_id: str) -> str:
        """Generate and save publication-quality BEV target visualization using Matplotlib.
        
        Args:
            maps: Dictionary containing BEV target maps (gt_heatmap, gt_mask, gt_cls)
            original_lanes: List of original ground truth lane data
            interpolated_lanes: List of interpolated lane data dictionaries
            frame_id: Frame identifier for naming the output file
            
        Returns:
            str: Path to the saved visualization image
        """
        try:
            # Create figure and axis
            fig, ax = plt.subplots(1, 1, figsize=self.style['figure_size'], 
                                 dpi=self.style['dpi'], 
                                 facecolor=self.style['background_color'])
            
            # Step 1: Set up BEV coordinate system and grid
            ax.set_xlim(self.y_min, self.y_max)  # Lateral range on X-axis
            ax.set_ylim(self.x_min, self.x_max)  # Forward range on Y-axis
            ax.set_aspect('equal', adjustable='box')
            
            # Configure grid with enhanced visibility
            ax.set_axisbelow(True)  # Ensure grid is behind data
            
            # Add major grid lines with black color for better visibility
            major_ticks_x = np.arange(np.ceil(self.y_min/10)*10, self.y_max, 10)
            major_ticks_y = np.arange(np.ceil(self.x_min/10)*10, self.x_max, 10)
            ax.set_xticks(major_ticks_x)
            ax.set_yticks(major_ticks_y)
            ax.grid(True, which='major', alpha=0.8, color=self.style['major_grid_color'], linewidth=1.0)
            
            # Add minor grid lines with darker gray for better visibility
            minor_ticks_x = np.arange(np.ceil(self.y_min/5)*5, self.y_max, 5)
            minor_ticks_y = np.arange(np.ceil(self.x_min/5)*5, self.x_max, 5)
            ax.set_xticks(minor_ticks_x, minor=True)
            ax.set_yticks(minor_ticks_y, minor=True)
            ax.grid(True, which='minor', alpha=0.5, color=self.style['grid_color'], linewidth=0.5)
            
            # Step 2: Draw discrete cell-based heatmap/mask overlay
            heatmap = maps.get('gt_heatmap')
            mask = maps.get('gt_mask')
            occupancy_data = self._prepare_heatmap_data(heatmap, mask)
            
            # Track if we actually drew any occupied cells for legend purposes
            cells_drawn = False
            
            if occupancy_data is not None:
                # Find all occupied cells (value = 1)
                occupied_indices = np.argwhere(occupancy_data > 0.5)
                
                if len(occupied_indices) > 0:
                    cells_drawn = True
                    print(f"[BEVTargetVisualizer] Drawing {len(occupied_indices)} occupied cells for frame {frame_id}")
                    
                    # Draw a rectangle for each occupied cell
                    for ix, iy in occupied_indices:
                        # Calculate world coordinates for the bottom-left corner of the cell
                        # ix corresponds to forward (x) grid index, iy corresponds to lateral (y) grid index
                        corner_x = self.x_min + ix * self.x_res  # Forward position
                        corner_y = self.y_min + iy * self.y_res  # Lateral position
                        
                        # Create rectangle patch for this cell
                        # Note: matplotlib Rectangle takes (x, y) as (lateral, forward) for our coordinate system
                        rect = patches.Rectangle(
                            (corner_y, corner_x),          # (lateral, forward) bottom-left corner
                            self.y_res,                    # width (lateral direction)
                            self.x_res,                    # height (forward direction)
                            linewidth=0,                   # No border for clean appearance
                            facecolor=self.style['heatmap_color'],
                            alpha=self.style['heatmap_alpha'],
                            zorder=1                       # Behind other elements
                        )
                        ax.add_patch(rect)
                else:
                    print(f"[BEVTargetVisualizer] No occupied cells found for frame {frame_id}")
            
            # Step 3: Draw interpolated lane points
            interpolated_handles = []
            if interpolated_lanes:
                for lane_idx, lane_data in enumerate(interpolated_lanes):
                    # Validate lane_data structure
                    if not isinstance(lane_data, dict):
                        continue
                    
                    required_keys = ['x_positions', 'y_interp', 'z_interp', 'vis_interp']
                    if not all(key in lane_data for key in required_keys):
                        continue
                    
                    x_positions = lane_data.get('x_positions', [])
                    y_interp = lane_data.get('y_interp', [])
                    vis_interp = lane_data.get('vis_interp', [])
                    
                    if len(x_positions) == 0 or y_interp is None:
                        continue
                    
                    # Filter out NaN values and invisible points
                    valid_mask = ~np.isnan(y_interp) & (np.array(vis_interp) > 0.5)
                    
                    if not np.any(valid_mask):
                        continue
                    
                    x_valid = np.array(x_positions)[valid_mask]
                    y_valid = np.array(y_interp)[valid_mask]
                    
                    # Plot interpolated lane as line
                    if len(x_valid) > 1:
                        line, = ax.plot(y_valid, x_valid, 
                                      color=self.style['interpolated_color'],
                                      linewidth=self.style['interpolated_linewidth'],
                                      alpha=0.8, label='Interpolated Lanes' if lane_idx == 0 else "")
                        if lane_idx == 0:
                            interpolated_handles.append(line)
            
            # Step 4: Draw original ground truth points
            gt_handles = []
            if original_lanes:
                all_gt_x = []
                all_gt_y = []
                
                for lane_idx, lane_3d in enumerate(original_lanes):
                    # Handle different lane data formats
                    if isinstance(lane_3d, dict) and 'points_3d' in lane_3d:
                        points_3d = lane_3d['points_3d']
                    else:
                        points_3d = lane_3d
                    
                    # Convert to numpy array if needed
                    if not isinstance(points_3d, np.ndarray):
                        points_3d = np.array(points_3d)
                    
                    if points_3d.size == 0 or points_3d.shape[0] == 0:
                        continue
                    
                    # Extract x, y coordinates
                    lane_x = points_3d[:, 0]
                    lane_y = points_3d[:, 1]
                    
                    all_gt_x.extend(lane_x)
                    all_gt_y.extend(lane_y)
                
                # Plot all GT points at once for better performance
                if all_gt_x and all_gt_y:
                    scatter = ax.scatter(all_gt_y, all_gt_x, 
                                       c=self.style['original_gt_color'],
                                       s=self.style['original_gt_size'],
                                       alpha=0.8, edgecolors='white', linewidths=0.5,
                                       label='Original Ground Truth', zorder=5)
                    gt_handles.append(scatter)
            
            # Step 5: Configure axes and labels
            ax.set_xlabel('Lateral Distance (m)', fontsize=self.style['axis_label_size'], 
                         color=self.style['text_color'])
            ax.set_ylabel('Forward Distance (m)', fontsize=self.style['axis_label_size'],
                         color=self.style['text_color'])
            
            # Set title
            title = f"BEV Lane Target Visualization - Frame: {frame_id}"
            ax.set_title(title, fontsize=self.style['title_font_size'], 
                        color=self.style['text_color'], pad=20)
            
            # Add legend
            handles = []
            labels = []
            
            if cells_drawn:
                # Create proxy artist for discrete heatmap cells
                heatmap_patch = patches.Rectangle((0, 0), 1, 1, 
                                                facecolor=self.style['heatmap_color'],
                                                alpha=self.style['heatmap_alpha'],
                                                linewidth=0)
                handles.append(heatmap_patch)
                labels.append('Heatmap/Mask Regions')
            
            if interpolated_handles:
                handles.extend(interpolated_handles)
                labels.append('Interpolated Lanes')
            
            if gt_handles:
                handles.extend(gt_handles)
                labels.append('Original Ground Truth')
            
            if handles:
                ax.legend(handles, labels, loc='upper right', frameon=True,
                         fancybox=True, shadow=True, ncol=1, fontsize=10)
            
            # Add coordinate system information as text
            info_text = f"Grid: {self.nx}×{self.ny}, Resolution: {self.x_res:.2f}×{self.y_res:.2f}m"
            ax.text(0.02, 0.02, info_text, transform=ax.transAxes, fontsize=9,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                   verticalalignment='bottom')
            
            # Improve layout
            plt.tight_layout()
            
            # Save the figure
            output_path = os.path.join(self.output_dir, f"{frame_id}.png")
            plt.savefig(output_path, dpi=self.style['dpi'], bbox_inches='tight',
                       facecolor=self.style['background_color'], edgecolor='none')
            plt.close(fig)  # Close figure to free memory
            
            print(f"[BEVTargetVisualizer] Saved publication-quality visualization: {output_path}")
            return output_path
                
        except Exception as e:
            print(f"[BEVTargetVisualizer] Error generating visualization for frame {frame_id}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def draw_simple_bev_targets(self, maps: Dict, original_lanes: List, 
                               x_positions: np.ndarray, y_interp: np.ndarray, 
                               z_interp: np.ndarray, vis_interp: np.ndarray,
                               frame_id: str) -> str:
        """Simplified version for direct integration with GenerateBEVLaneHeatmapTargets.
        
        This method provides a simpler interface that matches the data available
        directly in the GenerateBEVLaneHeatmapTargets.__call__ method.
        
        Args:
            maps: Dictionary containing BEV target maps
            original_lanes: List of original ground truth lane data
            x_positions: X positions used for interpolation
            y_interp: Interpolated Y coordinates
            z_interp: Interpolated Z coordinates  
            vis_interp: Interpolated visibility
            frame_id: Frame identifier
            
        Returns:
            str: Path to the saved visualization image
        """        # Convert simple interpolation data to the expected format
        interpolated_lanes = []
        if y_interp is not None and x_positions is not None:
            lane_data = {
                'x_positions': x_positions,
                'y_interp': y_interp,
                'z_interp': z_interp if z_interp is not None else np.zeros_like(y_interp),
                'vis_interp': vis_interp if vis_interp is not None else np.ones_like(y_interp)
            }
            interpolated_lanes.append(lane_data)
        
        return self.draw_bev_targets(maps, original_lanes, interpolated_lanes, frame_id)