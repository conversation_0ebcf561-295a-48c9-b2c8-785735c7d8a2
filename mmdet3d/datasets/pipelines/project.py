"""
Projection-related operations for 3D lane detection pipeline.
All operations involving geometric projections between different coordinate systems.
"""

import numpy as np
import os.path as osp
import mmcv
from mmdet.datasets.builder import PIPELINES

@PIPELINES.register_module()
class ProjectLidarToImage:
    """Project LiDAR points onto image planes for depth supervision.
    
    Args:
        lidar_depth_mask_threshold (float): Depth threshold above which points are considered valid.
        depth_threshold_min (float): Minimum valid depth in meters.
        depth_threshold_max (float): Maximum valid depth in meters.
    """
    def __init__(self, lidar_depth_mask_threshold=2.0, depth_threshold_min=1.0, depth_threshold_max=80.0):
        self.threshold = lidar_depth_mask_threshold
        self.depth_min = depth_threshold_min
        self.depth_max = depth_threshold_max
        
    def __call__(self, results):
        """Call function to project LiDAR points to each camera image.
        
        Args:
            results (dict): Dict containing point cloud, images and transformation matrices.
        
        Returns:
            dict: Updated dict with projected points and depths.
        """
        points = results['points'].tensor.numpy()  # Get point cloud data
        
        # Get LiDAR to image transformation matrices
        if 'lidar2img' in results:
            lidar2img_list = results['lidar2img']
        else:
            # Calculate transformation if not directly available
            lidar2img_list = []
            for cam_idx in range(len(results['img'])):
                lidar2camera = results['lidar2camera'][cam_idx]  # LiDAR to camera transform
                camera_intrinsic = results['camera_intrinsics'][cam_idx]  # Camera intrinsics
                
                # Build projection matrix P = K * [R|t]
                projection = np.eye(4)
                projection[:3, :3] = camera_intrinsic
                lidar2img = projection @ lidar2camera
                lidar2img_list.append(lidar2img)
        
        # Project LiDAR points to each camera
        projected_points_list = []
        projected_depths_list = []
        
        for cam_idx, lidar2img in enumerate(lidar2img_list):
            # Get camera image dimensions
            if isinstance(results['img'][cam_idx], np.ndarray):
                img_shape = results['img'][cam_idx].shape[:2]  # (H, W)
            else:  # PIL.Image
                img_shape = (results['img'][cam_idx].height, results['img'][cam_idx].width)
            
            # Project points
            pts_2d, depths = self._project_points(points, lidar2img)
            
            # Get valid point mask
            valid_mask = self._get_valid_mask(pts_2d, depths, img_shape)
            
            # Store valid projected points and depths
            projected_points_list.append(pts_2d[valid_mask])
            projected_depths_list.append(depths[valid_mask])
            
        results['lidar_projected_points'] = projected_points_list
        results['lidar_projected_depths'] = projected_depths_list
        
        return results
        
    def _project_points(self, points, lidar2img):
        """Project 3D points to image plane.
        
        Args:
            points (np.ndarray): [N, 3+] shaped point cloud data.
            lidar2img (np.ndarray): [4, 4] shaped LiDAR to image transformation matrix.
            
        Returns:
            tuple:
                np.ndarray: [N, 2] shaped 2D projected coordinates.
                np.ndarray: [N] shaped point cloud depths.
        """
        # Convert to homogeneous coordinates
        num_points = points.shape[0]
        points_4d = np.concatenate([points[:, :3], np.ones((num_points, 1))], axis=1)
        
        # Apply transformation matrix
        projected = points_4d @ lidar2img.T
        
        # Normalize homogeneous coordinates
        projected_2d = projected[:, :2] / np.maximum(projected[:, 2:3], 1e-10)
        
        # Extract depth information
        depths = projected[:, 2]  # Depth in camera coordinate system
        
        return projected_2d, depths
        
    def _get_valid_mask(self, pts_2d, depths, img_shape):
        """Check if points are inside image boundaries and have valid depths.
        
        Args:
            pts_2d (np.ndarray): [N, 2] shaped 2D projected coordinates.
            depths (np.ndarray): [N] shaped point cloud depths.
            img_shape (tuple): Image dimensions (H, W).
            
        Returns:
            np.ndarray: [N] shaped boolean mask of valid points.
        """
        # Check if points are inside image boundaries
        h, w = img_shape
        valid_x = np.logical_and(pts_2d[:, 0] >= 0, pts_2d[:, 0] < w)
        valid_y = np.logical_and(pts_2d[:, 1] >= 0, pts_2d[:, 1] < h)
        
        # Check if depths are in valid range
        valid_depth = np.logical_and(depths > self.depth_min, depths < self.depth_max)
        
        # Require depth greater than threshold to ensure points aren't too close
        mask = np.logical_and.reduce([valid_x, valid_y, valid_depth, depths > self.threshold])
        
        return mask


@PIPELINES.register_module()
class GenerateLidarDepthSupervisionTarget:
    """Generate LiDAR depth supervision target by projecting points to camera images.
    
    Args:
        camera_names (list[str]): Names of cameras to project points to.
        point_cloud_range (list[float], optional): Point cloud range to filter.
        depth_threshold_min (float, optional): Minimum valid depth in meters.
        depth_threshold_max (float, optional): Maximum valid depth in meters.
    """
    
    def __init__(self,
                 camera_names=['120_front'],
                 point_cloud_range=None,
                 depth_threshold_min=1.0,
                 depth_threshold_max=80.0):
        self.camera_names = camera_names
        self.point_cloud_range = point_cloud_range
        self.depth_min = depth_threshold_min
        self.depth_max = depth_threshold_max
    
    def _project_points_to_image(self, points, lidar2img, img_shape):
        """Project 3D points to 2D image plane with depth.
        
        Args:
            points (np.ndarray): 3D points in shape (N, 3+).
            lidar2img (np.ndarray): Transformation matrix in shape (4, 4).
            img_shape (tuple): Image shape in (H, W).
            
        Returns:
            tuple: (uv_coords, depths, valid_mask).
        """
        # Extract 3D coordinates
        num_points = points.shape[0]
        pts_3d = points[:, :3]
        
        # Homogeneous coordinates
        pts_4d = np.concatenate([pts_3d, np.ones((num_points, 1))], axis=1)
        
        # Project to image plane
        pts_2d = pts_4d @ lidar2img.T
        
        # Normalize homogeneous coordinates
        pts_2d_depth = pts_2d[:, 2:3].copy()
        pts_2d[:, 0:2] = pts_2d[:, 0:2] / np.maximum(pts_2d_depth, 1e-8)
        
        uv_coords = pts_2d[:, 0:2]
        depths = pts_2d_depth.squeeze(-1)
        
        # Filter points: within image boundaries and depth range
        h, w = img_shape
        valid_mask = (
            (uv_coords[:, 0] >= 0) & (uv_coords[:, 0] < w) &
            (uv_coords[:, 1] >= 0) & (uv_coords[:, 1] < h) &
            (depths > self.depth_min) & (depths < self.depth_max)
        )
        
        # Additional filtering based on point cloud range (if specified)
        if self.point_cloud_range is not None:
            x_min, y_min, z_min, x_max, y_max, z_max = self.point_cloud_range
            pc_range_mask = (
                (pts_3d[:, 0] >= x_min) & (pts_3d[:, 0] <= x_max) &
                (pts_3d[:, 1] >= y_min) & (pts_3d[:, 1] <= y_max) &
                (pts_3d[:, 2] >= z_min) & (pts_3d[:, 2] <= z_max)
            )
            valid_mask = valid_mask & pc_range_mask
        
        return uv_coords, depths, valid_mask
    
    def __call__(self, results):
        """Call function to generate depth supervision target.
        
        Args:
            results (dict): Result dict containing point cloud and camera data.
            
        Returns:
            dict: Updated result dict with depth supervision targets.
        """
        # Ensure required fields are available
        if 'points' not in results or not results.get('image_paths') or not results.get('lidar2image'):
            return results
        
        points = results['points'].tensor.numpy()
        lidar2img_list = results['lidar2image']
        img_shapes = []
        
        # Get image shapes
        for i, img_path in enumerate(results['image_paths']):
            if isinstance(results.get('img'), list) and i < len(results['img']):
                # If images are already loaded
                img = results['img'][i]
                if isinstance(img, np.ndarray):
                    img_shapes.append(img.shape[:2])  # (H, W)
                else:  # PIL.Image
                    img_shapes.append((img.height, img.width))
            elif img_path and osp.exists(img_path):
                # Load image dimensions without loading the full image
                img_info = mmcv.imread(img_path, backend='pillow')
                img_shapes.append((img_info.height, img_info.width))
            else:
                # Default image shape if not available
                img_shapes.append((1080, 1920))  # Default HD resolution
        
        # Create depth targets for specified cameras
        lidar_depth_targets = []
        
        for camera_name in self.camera_names:
            if camera_name in results.get('cam_names', []):
                cam_idx = results['cam_names'].index(camera_name)
            else:
                # Try to find camera index by matching name in cam_list
                cam_idx = -1
                for i, path in enumerate(results.get('image_paths', [])):
                    if path and camera_name in path.replace('_', '-'):
                        cam_idx = i
                        break
            
            if cam_idx >= 0 and cam_idx < len(lidar2img_list):
                lidar2img = lidar2img_list[cam_idx]
                img_shape = img_shapes[cam_idx] if cam_idx < len(img_shapes) else (1080, 1920)
                
                # Project points to this camera
                uv_coords, depths, valid_mask = self._project_points_to_image(points, lidar2img, img_shape)
                
                # Create depth target (uv coordinates and depth values)
                if np.any(valid_mask):
                    valid_uvs = uv_coords[valid_mask].astype(np.float32)
                    valid_depths = depths[valid_mask].astype(np.float32)
                    
                    depth_target = {
                        'camera_name': camera_name,
                        'uv_coords': valid_uvs,  # Nx2 array of pixel coordinates
                        'depths': valid_depths,  # N array of depth values
                        'img_shape': img_shape
                    }
                    lidar_depth_targets.append(depth_target)
        
        results['lidar_depth_target'] = lidar_depth_targets
        
        return results


@PIPELINES.register_module()
class ProjectLanesToBEV:
    """Project 3D lane points to BEV space.
    
    Args:
        point_cloud_range (list[float]): Point cloud range [x_min, y_min, z_min, x_max, y_max, z_max].
        voxel_size (list[float]): Voxel size [x_size, y_size, z_size].
        bev_size (list[int], optional): Size of BEV grid [width, height].
    """
    def __init__(self, 
                 point_cloud_range, 
                 voxel_size,
                 bev_size=None):
        self.point_cloud_range = np.array(point_cloud_range)
        self.voxel_size = np.array(voxel_size)
        # Calculate BEV size if not provided
        if bev_size is None:
            self.bev_size = np.array([
                int((self.point_cloud_range[3] - self.point_cloud_range[0]) / self.voxel_size[0]),
                int((self.point_cloud_range[4] - self.point_cloud_range[1]) / self.voxel_size[1])
            ])
        else:
            self.bev_size = np.array(bev_size)
    
    def __call__(self, results):
        """Call function to project 3D lanes to BEV.
        
        Args:
            results (dict): Result dict from data pipeline.
            
        Returns:
            dict: Updated result dict with BEV lane projections.
        """
        if 'gt_lanes_3d' not in results or not results['gt_lanes_3d']:
            # No lanes available
            results['gt_lanes_bev'] = []
            return results
        
        # Extract lane data
        lanes_3d = results['gt_lanes_3d']
        
        # Project each lane to BEV
        lanes_bev = []
        for lane_points in lanes_3d:
            lane_points_array = np.array(lane_points)
            
            # Convert 3D points to BEV grid indices
            x = lane_points_array[:, 0]
            y = lane_points_array[:, 1]
            
            # Convert to grid indices
            x_indices = ((x - self.point_cloud_range[0]) / self.voxel_size[0]).astype(np.int32)
            y_indices = ((y - self.point_cloud_range[1]) / self.voxel_size[1]).astype(np.int32)
            
            # Clip to valid range
            x_indices = np.clip(x_indices, 0, self.bev_size[0] - 1)
            y_indices = np.clip(y_indices, 0, self.bev_size[1] - 1)
            
            # Create BEV lane points
            bev_points = np.stack([x_indices, y_indices], axis=1)
            lanes_bev.append(bev_points)
        
        results['gt_lanes_bev'] = lanes_bev
        return results