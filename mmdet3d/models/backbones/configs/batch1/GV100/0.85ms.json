[{"layer_type": "data", "img_resolution": [288, 288], "distill": false}, {"layer_type": "head", "num_in_channels": 3, "num_out_channels": 24}, {"layer_type": "conv", "num_in_channels": 24, "num_out_channels": 24, "stride": 1, "kernel_size": 3, "act": "relu", "stage": 1}, {"layer_type": "fused_irb", "num_in_channels": 24, "num_out_channels": 64, "stride": 2, "expansion": 4, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 2}, {"layer_type": "fused_irb", "num_in_channels": 64, "num_out_channels": 64, "stride": 1, "expansion": 4, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 2}, {"layer_type": "fused_irb", "num_in_channels": 64, "num_out_channels": 96, "stride": 2, "expansion": 4, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 3}, {"layer_type": "fused_irb", "num_in_channels": 96, "num_out_channels": 96, "stride": 1, "expansion": 4, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 3}, {"layer_type": "irb", "num_in_channels": 96, "num_out_channels": 160, "stride": 2, "expansion": 2, "kernel_size": 3, "act": "swish", "use_se": true, "stage": 4}, {"layer_type": "irb", "num_in_channels": 160, "num_out_channels": 288, "stride": 1, "expansion": 5, "kernel_size": 3, "act": "relu", "use_se": false, "stage": 5}, {"layer_type": "irb", "num_in_channels": 288, "num_out_channels": 288, "stride": 1, "expansion": 5, "kernel_size": 3, "act": "relu", "use_se": false, "stage": 5}, {"layer_type": "irb", "num_in_channels": 288, "num_out_channels": 288, "stride": 1, "expansion": 5, "kernel_size": 3, "act": "relu", "use_se": false, "stage": 5}, {"layer_type": "irb", "num_in_channels": 288, "num_out_channels": 288, "stride": 1, "expansion": 5, "kernel_size": 3, "act": "relu", "use_se": false, "stage": 5}, {"layer_type": "irb", "num_in_channels": 288, "num_out_channels": 448, "stride": 2, "expansion": 4, "kernel_size": 3, "act": "relu", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 448, "num_out_channels": 448, "stride": 1, "expansion": 4, "kernel_size": 3, "act": "relu", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 448, "num_out_channels": 448, "stride": 1, "expansion": 4, "kernel_size": 3, "act": "relu", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 448, "num_out_channels": 448, "stride": 1, "expansion": 4, "kernel_size": 3, "act": "relu", "use_se": true, "stage": 6}, {"layer_type": "tail", "num_in_channels": 448, "num_out_channels": 1280, "num_classes": 1000}]