[{"layer_type": "data", "img_resolution": 456, "distill": true}, {"layer_type": "head", "num_in_channels": 3, "num_out_channels": 33, "stage": 0}, {"layer_type": "irb", "num_in_channels": 33, "num_out_channels": 33, "stride": 1, "expansion": 1, "kernel_size": 3, "groups": 1, "stage": 0}, {"layer_type": "irb", "num_in_channels": 33, "num_out_channels": 44, "stride": 2, "expansion": 3, "kernel_size": 3, "groups": 1, "stage": 1}, {"layer_type": "irb", "num_in_channels": 44, "num_out_channels": 44, "stride": 1, "expansion": 4, "kernel_size": 5, "groups": 1, "stage": 1}, {"layer_type": "irb", "num_in_channels": 44, "num_out_channels": 44, "stride": 1, "expansion": 3, "kernel_size": 3, "groups": 1, "stage": 1}, {"layer_type": "irb", "num_in_channels": 44, "num_out_channels": 67, "stride": 2, "expansion": 4, "kernel_size": 5, "groups": 1, "stage": 2}, {"layer_type": "irb", "num_in_channels": 67, "num_out_channels": 67, "stride": 1, "expansion": 4, "kernel_size": 5, "groups": 1, "stage": 2}, {"layer_type": "irb", "num_in_channels": 67, "num_out_channels": 67, "stride": 1, "expansion": 6, "kernel_size": 3, "groups": 1, "stage": 2}, {"layer_type": "irb", "num_in_channels": 67, "num_out_channels": 67, "stride": 1, "expansion": 3, "kernel_size": 5, "groups": 1, "stage": 2}, {"layer_type": "irb", "num_in_channels": 67, "num_out_channels": 134, "stride": 2, "expansion": 6, "kernel_size": 7, "groups": 1, "stage": 3}, {"layer_type": "irb", "num_in_channels": 134, "num_out_channels": 134, "stride": 1, "expansion": 6, "kernel_size": 5, "groups": 1, "stage": 3}, {"layer_type": "irb", "num_in_channels": 134, "num_out_channels": 134, "stride": 1, "expansion": 3, "kernel_size": 3, "groups": 1, "stage": 3}, {"layer_type": "irb", "num_in_channels": 134, "num_out_channels": 134, "stride": 1, "expansion": 4, "kernel_size": 5, "groups": 1, "stage": 3}, {"layer_type": "irb", "num_in_channels": 134, "num_out_channels": 190, "stride": 1, "expansion": 6, "kernel_size": 3, "groups": 1, "stage": 3}, {"layer_type": "irb", "num_in_channels": 190, "num_out_channels": 190, "stride": 1, "expansion": 6, "kernel_size": 5, "groups": 1, "stage": 3}, {"layer_type": "irb", "num_in_channels": 190, "num_out_channels": 190, "stride": 1, "expansion": 6, "kernel_size": 7, "groups": 1, "stage": 3}, {"layer_type": "irb", "num_in_channels": 190, "num_out_channels": 190, "stride": 1, "expansion": 4, "kernel_size": 3, "groups": 1, "stage": 3}, {"layer_type": "irb", "num_in_channels": 190, "num_out_channels": 268, "stride": 2, "expansion": 6, "kernel_size": 3, "groups": 1, "stage": 4}, {"layer_type": "irb", "num_in_channels": 268, "num_out_channels": 268, "stride": 1, "expansion": 6, "kernel_size": 3, "groups": 1, "stage": 4}, {"layer_type": "irb", "num_in_channels": 268, "num_out_channels": 268, "stride": 1, "expansion": 6, "kernel_size": 5, "groups": 1, "stage": 4}, {"layer_type": "irb", "num_in_channels": 268, "num_out_channels": 268, "stride": 1, "expansion": 3, "kernel_size": 3, "groups": 1, "stage": 4}, {"layer_type": "irb", "num_in_channels": 268, "num_out_channels": 268, "stride": 1, "expansion": 3, "kernel_size": 3, "groups": 1, "stage": 4}, {"layer_type": "irb", "num_in_channels": 268, "num_out_channels": 268, "stride": 1, "expansion": 3, "kernel_size": 3, "groups": 1, "stage": 4}, {"layer_type": "tail", "num_in_channels": 268, "num_out_channels": 1984, "num_classes": 1000}]