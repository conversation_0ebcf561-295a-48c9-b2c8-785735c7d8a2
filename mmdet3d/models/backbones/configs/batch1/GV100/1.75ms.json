[{"layer_type": "data", "img_resolution": 384, "distill": false}, {"layer_type": "head", "num_in_channels": 3, "num_out_channels": 32}, {"layer_type": "conv", "num_in_channels": 32, "num_out_channels": 32, "stride": 1, "kernel_size": 3, "act": "relu", "stage": 1}, {"layer_type": "conv", "num_in_channels": 32, "num_out_channels": 32, "stride": 1, "kernel_size": 3, "act": "relu", "stage": 1}, {"layer_type": "conv", "num_in_channels": 32, "num_out_channels": 32, "stride": 1, "kernel_size": 3, "act": "relu", "stage": 1}, {"layer_type": "fused_irb", "num_in_channels": 32, "num_out_channels": 32, "stride": 2, "expansion": 6, "kernel_size": 5, "act": "swish", "use_se": true, "stage": 2}, {"layer_type": "fused_irb", "num_in_channels": 32, "num_out_channels": 112, "stride": 2, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 3}, {"layer_type": "fused_irb", "num_in_channels": 112, "num_out_channels": 112, "stride": 1, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 3}, {"layer_type": "fused_irb", "num_in_channels": 112, "num_out_channels": 112, "stride": 1, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 3}, {"layer_type": "irb", "num_in_channels": 112, "num_out_channels": 144, "stride": 2, "expansion": 6, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 4}, {"layer_type": "irb", "num_in_channels": 144, "num_out_channels": 144, "stride": 1, "expansion": 6, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 4}, {"layer_type": "irb", "num_in_channels": 144, "num_out_channels": 144, "stride": 1, "expansion": 6, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 4}, {"layer_type": "irb", "num_in_channels": 144, "num_out_channels": 144, "stride": 1, "expansion": 6, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 4}, {"layer_type": "irb", "num_in_channels": 144, "num_out_channels": 144, "stride": 1, "expansion": 6, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 4}, {"layer_type": "irb", "num_in_channels": 144, "num_out_channels": 144, "stride": 1, "expansion": 6, "kernel_size": 3, "act": "swish", "use_se": false, "stage": 4}, {"layer_type": "irb", "num_in_channels": 144, "num_out_channels": 160, "stride": 1, "expansion": 3, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 5}, {"layer_type": "irb", "num_in_channels": 160, "num_out_channels": 160, "stride": 1, "expansion": 3, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 5}, {"layer_type": "irb", "num_in_channels": 160, "num_out_channels": 160, "stride": 1, "expansion": 3, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 5}, {"layer_type": "irb", "num_in_channels": 160, "num_out_channels": 160, "stride": 1, "expansion": 3, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 5}, {"layer_type": "irb", "num_in_channels": 160, "num_out_channels": 224, "stride": 2, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 224, "num_out_channels": 224, "stride": 1, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 224, "num_out_channels": 224, "stride": 1, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 224, "num_out_channels": 224, "stride": 1, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 224, "num_out_channels": 224, "stride": 1, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 224, "num_out_channels": 224, "stride": 1, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 224, "num_out_channels": 224, "stride": 1, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 224, "num_out_channels": 224, "stride": 1, "expansion": 3, "kernel_size": 3, "act": "swish", "use_se": true, "stage": 6}, {"layer_type": "irb", "num_in_channels": 224, "num_out_channels": 832, "stride": 1, "expansion": 2, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 7}, {"layer_type": "irb", "num_in_channels": 832, "num_out_channels": 832, "stride": 1, "expansion": 2, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 7}, {"layer_type": "irb", "num_in_channels": 832, "num_out_channels": 832, "stride": 1, "expansion": 2, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 7}, {"layer_type": "irb", "num_in_channels": 832, "num_out_channels": 832, "stride": 1, "expansion": 2, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 7}, {"layer_type": "irb", "num_in_channels": 832, "num_out_channels": 832, "stride": 1, "expansion": 2, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 7}, {"layer_type": "irb", "num_in_channels": 832, "num_out_channels": 832, "stride": 1, "expansion": 2, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 7}, {"layer_type": "irb", "num_in_channels": 832, "num_out_channels": 832, "stride": 1, "expansion": 2, "kernel_size": 5, "act": "relu", "use_se": false, "stage": 7}, {"layer_type": "tail", "num_in_channels": 832, "num_out_channels": 1280, "num_classes": 1000}]