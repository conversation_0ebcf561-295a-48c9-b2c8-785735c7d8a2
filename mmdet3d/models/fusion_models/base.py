from abc import <PERSON><PERSON><PERSON>
from collections import OrderedDict

import torch
import torch.distributed as dist
from mmcv.runner import BaseModule

__all__ = ["Base3DFusionModel"]


class Base3DFusionModel(BaseModule, metaclass=ABCMeta):
    """Base class for fusion_models."""

    def __init__(self, init_cfg=None):
        super().__init__(init_cfg)
        self.fp16_enabled = False

    def _parse_losses(self, losses):
        """Parse the raw outputs (losses) of the network.

        Args:
            losses (dict): Raw output of the network, which usually contain
                losses and other necessary infomation.

        Returns:
            tuple[Tensor, dict]: (loss, log_vars), loss is the loss tensor \
                which may be a weighted sum of all losses, log_vars contains \
                all the variables to be sent to the logger.
        """
        # =============================================================================
        # AUTOGRAD DEBUGGING: Track loss tensor connectivity
        # =============================================================================
        # print(f"[PARSE_LOSSES_DEBUG] === LOSS PARSING START ===")
        # print(f"[PARSE_LOSSES_DEBUG] Input losses keys: {list(losses.keys())}")
        
        log_vars = OrderedDict()
        for loss_name, loss_value in losses.items():
            # print(f"[PARSE_LOSSES_DEBUG] Processing {loss_name}: type={type(loss_value)}")
            
            if isinstance(loss_value, torch.Tensor):
                # print(f"[PARSE_LOSSES_DEBUG] {loss_name} tensor: requires_grad={loss_value.requires_grad}, grad_fn={loss_value.grad_fn}, shape={loss_value.shape}")
                log_vars[loss_name] = loss_value.mean()
            elif isinstance(loss_value, list):
                # print(f"[PARSE_LOSSES_DEBUG] {loss_name} is a list of {len(loss_value)} tensors")
                log_vars[loss_name] = sum(_loss.mean() for _loss in loss_value)
            else:
                # The head should now guarantee all losses are tensors
                # If we hit this, it's a bug in the head implementation
                raise TypeError(f"{loss_name} is not a tensor or list of tensors, but got {type(loss_value)}. "
                              f"Check the head implementation to ensure all returned losses are tensors.")

        # Sum only the actual loss terms (filter out metrics)
        loss_terms = {_key: _value for _key, _value in log_vars.items() if "loss" in _key}
        # print(f"[PARSE_LOSSES_DEBUG] Loss terms for aggregation: {list(loss_terms.keys())}")
        
        # Compute total loss
        loss = sum(_value for _key, _value in loss_terms.items())
        
        # =============================================================================
        # CRITICAL AUTOGRAD VERIFICATION: Ensure final loss has gradient connectivity
        # =============================================================================
        # print(f"[PARSE_LOSSES_DEBUG] === FINAL LOSS AUTOGRAD CHECK ===")
        # print(f"[PARSE_LOSSES_DEBUG] Final loss type: {type(loss)}")
        # print(f"[PARSE_LOSSES_DEBUG] Final loss requires_grad: {loss.requires_grad}")
        # print(f"[PARSE_LOSSES_DEBUG] Final loss grad_fn: {loss.grad_fn}")
        # print(f"[PARSE_LOSSES_DEBUG] Final loss value: {loss.item():.6f}")
        # print(f"[PARSE_LOSSES_DEBUG] Final loss device: {loss.device}")
        
        # Critical check: The final loss MUST have gradient connectivity
        if not loss.requires_grad or loss.grad_fn is None:
            error_msg = (f"CRITICAL AUTOGRAD ERROR: Final aggregated loss lacks gradient connectivity!\n"
                        f"  requires_grad: {loss.requires_grad}\n"
                        f"  grad_fn: {loss.grad_fn}\n"
                        f"  This will cause RuntimeError when backward() is called.\n"
                        f"  Check that all component losses are connected to the computation graph.")
            print(f"[PARSE_LOSSES_ERROR] {error_msg}")
            # In production, you might want to raise an exception here:
            # raise RuntimeError(error_msg)
        else:
            print(f"[PARSE_LOSSES_DEBUG] ✓ Final loss has valid gradient connectivity")
        
        log_vars["loss"] = loss
        
        # =============================================================================
        # DISTRIBUTED TRAINING HANDLING
        # =============================================================================
        for loss_name, loss_value in log_vars.items():
            # reduce loss when distributed training
            if dist.is_available() and dist.is_initialized():
                loss_value = loss_value.data.clone()
                dist.all_reduce(loss_value.div_(dist.get_world_size()))
            print(f"[PARSE_LOSSES_DEBUG] {loss_name} final value: {loss_value.item():.6f}")
            log_vars[loss_name] = loss_value.item()

        print(f"[PARSE_LOSSES_DEBUG] === LOSS PARSING COMPLETE ===")
        return loss, log_vars

    def train_step(self, data, optimizer):
        """The iteration step during training.

        This method defines an iteration step during training, except for the
        back propagation and optimizer updating, which are done in an optimizer
        hook. Note that in some complicated cases or models, the whole process
        including back propagation and optimizer updating is also defined in
        this method, such as GAN.

        Args:
            data (dict): The output of dataloader.
            optimizer (:obj:`torch.optim.Optimizer` | dict): The optimizer of
                runner is passed to ``train_step()``. This argument is unused
                and reserved.

        Returns:
            dict: It should contain at least 3 keys: ``loss``, ``log_vars``, \
                ``num_samples``.

                - ``loss`` is a tensor for back propagation, which can be a \
                weighted sum of multiple losses.
                - ``log_vars`` contains all the variables to be sent to the
                logger.
                - ``num_samples`` indicates the batch size (when the model is \
                DDP, it means the batch size on each GPU), which is used for \
                averaging the logs.
        """
        losses = self(**data)
        loss, log_vars = self._parse_losses(losses)

        outputs = dict(loss=loss, log_vars=log_vars, num_samples=len(data["metas"]))

        return outputs

    def val_step(self, data, optimizer):
        """The iteration step during validation.

        This method shares the same signature as :func:`train_step`, but used
        during val epochs. Note that the evaluation after training epochs is
        not implemented with this method, but an evaluation hook.
        """
        losses = self(**data)
        loss, log_vars = self._parse_losses(losses)

        outputs = dict(loss=loss, log_vars=log_vars, num_samples=len(data["metas"]))

        return outputs
