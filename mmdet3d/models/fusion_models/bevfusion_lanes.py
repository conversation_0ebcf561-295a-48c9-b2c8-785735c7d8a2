from typing import Any, Dict, List
import torch
import torch.nn as nn
from torch.nn import functional as F
from mmcv.runner import auto_fp16, force_fp32

from mmdet3d.models.builder import FUSIONMODELS, build_backbone, build_fuser, build_head, build_neck, build_vtransform
from .bevfusion import BEVFusion
from mmdet3d.models.fusion_models.base import Base3DFusionModel
from mmdet3d.ops import Voxelization, DynamicScatter

__all__ = ["BEVFusionForLanes"]


@FUSIONMODELS.register_module()
class BEVFusionForLanes(Base3DFusionModel):
    """BEVFusion specialized for 3D lane detection.
    
    Args:
        encoders (Dict[str, Any]): Dictionary containing encoder configurations.
        fuser (Dict[str, Any]): Configuration for fusing multi-modal features.
        fuser_for_seg (Dict[str, Any], optional): Configuration for fusing features 
            specifically for segmentation-based lane detection.
        decoder (Dict[str, Any]): Configuration for BEV feature decoding.
        heads (Dict[str, Any]): Configuration for prediction heads.
    """
    
    def __init__(
        self,
        encoders: Dict[str, Any],
        fuser: Dict[str, Any],
        decoder: Dict[str, Any],
        heads: Dict[str, Any],
        fuser_for_seg: Dict[str, Any] = None,
        train_cfg=None,
        test_cfg=None,
        init_cfg=None,
        **kwargs,
    ) -> None:
        # Extract train_cfg and test_cfg from kwargs to match the behavior of BEVFusion
        super().__init__(init_cfg=init_cfg)
        
        # Initialize encoders (following BEVFusion pattern)
        self.encoders = nn.ModuleDict()
        if encoders.get("camera") is not None:
            self.encoders["camera"] = nn.ModuleDict(
                {
                    "backbone": build_backbone(encoders["camera"]["backbone"]),
                    "neck": build_neck(encoders["camera"]["neck"]),
                    "vtransform": build_vtransform(encoders["camera"]["vtransform"]),
                }
            )
            
            # Add backbone-to-neck adapter layers to handle potential channel mismatches
            # between GPUNetWrapper output and GeneralizedLSSFPN input
            self.backbone_adapters = nn.ModuleList()
            
            # Get expected input channels for the neck
            expected_channels = encoders["camera"]["neck"].get("in_channels", [])
            
            # We'll initialize adapters based on the first run's output shape
            # For now, we just store the expected channel counts
            self.expected_neck_channels = expected_channels
            self.adapters_initialized = False
            
        if encoders.get("lidar") is not None:
            if encoders["lidar"]["voxelize"].get("max_num_points", -1) > 0:
                voxelize_module = Voxelization(**encoders["lidar"]["voxelize"])
            else:
                voxelize_module = DynamicScatter(**encoders["lidar"]["voxelize"])
                
            self.encoders["lidar"] = nn.ModuleDict(
                {
                    "voxelize": voxelize_module,
                    "backbone": build_backbone(encoders["lidar"]["backbone"]),
                }
            )
            self.voxelize_reduce = encoders["lidar"].get("voxelize_reduce", True)

        # Build fuser
        if fuser is not None:
            self.fuser = build_fuser(fuser)
        else:
            self.fuser = None

        # Build segmentation fuser
        if fuser_for_seg is not None:
            self.fuser_for_seg = build_fuser(fuser_for_seg)
        else:
            self.fuser_for_seg = None

        # Build decoder
        self.decoder = nn.ModuleDict(
            {
                "backbone": build_backbone(decoder["backbone"]),
                "neck": build_neck(decoder["neck"]),
            }
        )
        
        # Build heads
        self.heads = nn.ModuleDict()
        for name in heads:
            if heads[name] is not None:
                self.heads[name] = build_head(heads[name])
                
        # Check if we're using the dense depth transform
        self.use_dense_depth_transform = False
        if "camera" in self.encoders and "vtransform" in self.encoders["camera"]:
            vtransform_type = self.encoders["camera"]["vtransform"].__class__.__name__
            self.use_dense_depth_transform = vtransform_type == "DepthLSSTransformWithDepthMap"
            
        # Loss scaling similar to BEVFusion
        if "loss_scale" in kwargs:
            self.loss_scale = kwargs["loss_scale"]
        else:
            self.loss_scale = dict()
            for name in heads:
                if heads[name] is not None:
                    self.loss_scale[name] = 1.0
        
        self.init_weights()
    
    def init_weights(self) -> None:
        if "camera" in self.encoders:
            self.encoders["camera"]["backbone"].init_weights()
    
    @auto_fp16(apply_to=("img", "points"))
    def forward(
        self,
        points,
        lidar2ego,
        lidar_aug_matrix=None,
        metas=None,
        img=None,
        camera2ego=None,
        lidar2camera=None,
        lidar2image=None,
        camera_intrinsics=None,
        camera2lidar=None,
        img_aug_matrix=None,
        lane_targets=None,
        depth_map=None,
        depth_map_valid=None,
        gt_lanes_3d=None,
        gt_lane_labels=None,
        **kwargs,
    ):
        """Forward function for training and inference.
        
        Args:
            points (List[torch.Tensor]): Point clouds for each sample.
            lidar2ego (torch.Tensor): Transformation from LiDAR to ego coordinate.
            lidar_aug_matrix (torch.Tensor): Augmentation matrix for LiDAR.
            metas (List[dict]): Meta information for each sample.
            img (torch.Tensor, optional): Images. Defaults to None.
            camera2ego (torch.Tensor, optional): Transformation from camera to ego coordinate.
                Defaults to None.
            lidar2camera (torch.Tensor, optional): Transformation from LiDAR to camera coordinate.
                Defaults to None.
            lidar2image (torch.Tensor, optional): Transformation from LiDAR to image coordinate.
                Defaults to None.
            camera_intrinsics (torch.Tensor, optional): Camera intrinsics. Defaults to None.
            camera2lidar (torch.Tensor, optional): Transformation from camera to LiDAR coordinate.
                Defaults to None.
            img_aug_matrix (torch.Tensor, optional): Augmentation matrix for images.
                Defaults to None.
            lane_targets (dict, optional): Lane annotation targets. Defaults to None.
            depth_map (torch.Tensor, optional): Depth map for depth supervision. Defaults to None.
            depth_map_valid (torch.Tensor, optional): Valid mask of depth map. Defaults to None.
            gt_lanes_3d (List[torch.Tensor], optional): Ground truth 3D lanes for evaluation.
                Defaults to None.
            gt_lane_labels (List[torch.Tensor], optional): Ground truth lane labels for evaluation.
                Defaults to None.
                
        Returns:
            Dict or List[Dict]: Loss dict for training or prediction results for inference.
        """
        
        if isinstance(img, list):
            raise NotImplementedError
        else:
            outputs = self.forward_single(
                img=img,
                points=points,
                camera2ego=camera2ego,
                lidar2ego=lidar2ego,
                lidar2camera=lidar2camera,
                lidar2image=lidar2image,
                camera_intrinsics=camera_intrinsics,
                camera2lidar=camera2lidar,
                img_aug_matrix=img_aug_matrix,
                lidar_aug_matrix=lidar_aug_matrix,
                metas=metas,
                lane_targets=lane_targets,
                depth_map=depth_map,
                depth_map_valid=depth_map_valid,
                gt_lanes_3d=gt_lanes_3d,
                gt_lane_labels=gt_lane_labels,
                **kwargs,
            )
            return outputs
    
    def extract_camera_features(
        self,
        img,
        points,
        camera2ego,
        lidar2ego,
        lidar2camera,
        lidar2image,
        camera_intrinsics,
        camera2lidar,
        img_aug_matrix,
        lidar_aug_matrix,
        metas,
        depth_map=None,
    ):
        """Extract features from camera branch.
        
        This function overrides the original to add direct depth map support.

        Args:
            img (torch.Tensor): Image tensor.
            points (torch.Tensor): Point cloud tensor.
            camera2ego (torch.Tensor): Camera to ego transformation.
            lidar2ego (torch.Tensor): LiDAR to ego transformation.
            lidar2camera (torch.Tensor): Transformation from LiDAR to camera coordinate.
            lidar2image (torch.Tensor): Transformation from LiDAR to image coordinate.
            camera_intrinsics (torch.Tensor): Camera intrinsic parameters.
            camera2lidar (torch.Tensor): Camera to LiDAR transformation.
            img_aug_matrix (torch.Tensor): Image augmentation matrix.
            lidar_aug_matrix (torch.Tensor): LiDAR augmentation matrix.
            metas (List[dict]): Meta information.
            depth_map (torch.Tensor, optional): Pre-computed dense depth maps.
        
        Returns:
            torch.Tensor: Camera features.
        """
        B, N, C, H, W = img.size()
        img = img.view(B * N, C, H, W)

        # Process through backbone
        backbone_out = self.encoders["camera"]["backbone"](img)
        
        if isinstance(backbone_out, (list, tuple)):
            # Initialize adapters if this is the first forward pass
            if not self.adapters_initialized:
                # print(f"Initializing backbone-to-neck adapters for {N} cameras:")
                # print(f"Expected neck input channels: {self.expected_neck_channels}")
                for i, feat in enumerate(backbone_out):
                    # print(f"Backbone feature {i} shape: {feat.shape[1]} channels")
                    
                    # Create adapters to match expected neck input channels
                    if i < len(self.expected_neck_channels):
                        expected_c = self.expected_neck_channels[i]
                        actual_c = feat.shape[1]
                        
                        if actual_c != expected_c:
                            # print(f"Creating adapter {i}: {actual_c} -> {expected_c} channels")
                            adapter = torch.nn.Conv2d(
                                actual_c, expected_c, kernel_size=1, stride=1, padding=0, bias=False
                            ).to(feat.device)
                            # Initialize weights close to identity mapping
                            torch.nn.init.normal_(adapter.weight, mean=0, std=0.01)
                            self.backbone_adapters.append(adapter)
                        else:
                            # Identity adapter (no transformation needed)
                            self.backbone_adapters.append(torch.nn.Identity())
                
                self.adapters_initialized = True
            
            # Use adapters to match backbone output to neck input
            adapted_features = []
            for i, feat in enumerate(backbone_out):
                if i < len(self.backbone_adapters):
                    adapted_feat = self.backbone_adapters[i](feat)
                    adapted_features.append(adapted_feat)
                else:
                    # If there's more backbone outputs than adapters, use features as-is
                    # (this shouldn't happen if initialization was correct)
                    adapted_features.append(feat)
            
            # Process through neck with adapted features
            img = self.encoders["camera"]["neck"](adapted_features)
        else:
            # Single tensor output (unlikely for image backbone)
            if not self.adapters_initialized:
                # print(f"Backbone output is a single tensor with shape: {backbone_out.shape}")
                # Handle the case where backbone returns a single feature map
                # This is unlikely for image backbones, but we handle it just in case
                self.adapters_initialized = True
                
            # Process through neck
            img = self.encoders["camera"]["neck"](backbone_out)

        if not isinstance(img, torch.Tensor):
            img = img[0]

        BN, C, H, W = img.size()
        img = img.view(B, int(BN / B), C, H, W)

        # If using dense depth transform, pass depth map directly
        if self.use_dense_depth_transform and hasattr(self.encoders["camera"]["vtransform"], "forward"):
            x = self.encoders["camera"]["vtransform"](
                img,
                points,
                camera2ego,
                lidar2ego,
                lidar2camera,
                lidar2image,
                camera_intrinsics,
                camera2lidar,
                img_aug_matrix,
                lidar_aug_matrix,
                metas,
                dense_depth=depth_map,
            )
            return x
        else:
            # Fall back to original vtransform implementation
            x = self.encoders["camera"]["vtransform"](
                img,
                points,
                camera2ego,
                lidar2ego,
                lidar2camera,
                lidar2image,
                camera_intrinsics,
                camera2lidar,
                img_aug_matrix,
                lidar_aug_matrix,
                metas,
            )
            return x
            
    @torch.no_grad()
    @force_fp32()
    def voxelize(self, points):
        feats, coords, sizes = [], [], []
        for k, res in enumerate(points):
            ret = self.encoders["lidar"]["voxelize"](res)
            if len(ret) == 3:
                # hard voxelize
                f, c, n = ret
            else:
                assert len(ret) == 2
                f, c = ret
                n = None
            feats.append(f)
            coords.append(F.pad(c, (1, 0), mode="constant", value=k))
            if n is not None:
                sizes.append(n)

        feats = torch.cat(feats, dim=0)
        coords = torch.cat(coords, dim=0)
        # # print(f"[VOXELIZE_DEBUG] Final concatenated feats shape: {feats.shape}")
        # # print(f"[VOXELIZE_DEBUG] Final concatenated coords shape: {coords.shape}")
        
        if len(sizes) > 0:
            sizes = torch.cat(sizes, dim=0)
            # # print(f"[VOXELIZE_DEBUG] Sizes shape: {sizes.shape}")
            if self.voxelize_reduce:
                # # print(f"[VOXELIZE_DEBUG] Before max reduction: {feats.shape}")
                feats = torch.max(feats, dim=1, keepdim=False)[0]
                feats = feats.contiguous()
        # #         print(f"[VOXELIZE_DEBUG] After max reduction: {feats.shape}")
        # # else:
        # #     print(f"[VOXELIZE_DEBUG] No sizes to concatenate")

        return feats, coords, sizes
        
    def extract_lidar_features(self, points):
        """Extract features from LiDAR branch.
        
        Args:
            points (torch.Tensor): Point cloud tensor.
            
        Returns:
            tuple: (lidar_features, point_coords) containing:
                - lidar_features: Features from LiDAR branch
                - point_coords: Point coordinates for segmentation
        """
        # # Debug voxelization process
        # # print(f"[LIDAR_DEBUG] Input points: {len(points)} batches")
        # # for i, pts in enumerate(points):
        # #     print(f"[LIDAR_DEBUG] Batch {i}: {pts.shape[0]} points, shape: {pts.shape}")
        
        feats, coords, sizes = self.voxelize(points)
        batch_size = coords[-1, 0].item() + 1
        
        # # print(f"[LIDAR_DEBUG] After voxelization:")
        # # print(f"[LIDAR_DEBUG]   feats shape: {feats.shape}")
        # # print(f"[LIDAR_DEBUG]   coords shape: {coords.shape}")
        # # print(f"[LIDAR_DEBUG]   batch_size: {batch_size}")
        # # if sizes is not None:
        # #     print(f"[LIDAR_DEBUG]   sizes shape: {sizes.shape}")
        # # else:
        # #     print(f"[LIDAR_DEBUG]   sizes: None")
        
        # # CRITICAL DEBUG: Track spatial dimensions through backbone
        # print(f"[LIDAR_BACKBONE_DEBUG] Input to backbone:")
        # print(f"[LIDAR_BACKBONE_DEBUG]   feats: {feats.shape}")
        # print(f"[LIDAR_BACKBONE_DEBUG]   coords: {coords.shape}")
        # print(f"[LIDAR_BACKBONE_DEBUG]   batch_size: {batch_size}")
        
        x = self.encoders["lidar"]["backbone"](feats, coords, batch_size, sizes=sizes)
        
        # Create additional feature for segmentation
        point_coords = None
        if isinstance(x, tuple):
            x_sparse = x[0]
            point_coords = x[1]  # Point coordinates for segmentation tasks
        else:
            x_sparse = x
            # If point_coords not available, we'll use the BEV features directly
        
        # CRITICAL DEBUG: Check LiDAR output dimensions
        # print(f"[LIDAR_BACKBONE_DEBUG] Output from backbone:")
        # print(f"[LIDAR_BACKBONE_DEBUG]   x_sparse shape: {x_sparse.shape}")
        # if point_coords is not None:
            # print(f"[LIDAR_BACKBONE_DEBUG]   point_coords shape: {point_coords.shape}")
        # print(f"[FUSION_DEBUG] LiDAR backbone output shape: {x_sparse.shape}")
        
        return x_sparse, point_coords if point_coords is not None else x_sparse
    
    @auto_fp16(apply_to=("img", "points"))
    def forward_single(
        self,
        img,
        points,
        camera2ego,
        lidar2ego,
        lidar2camera,
        lidar2image,
        camera_intrinsics,
        camera2lidar,
        img_aug_matrix,
        lidar_aug_matrix=None,
        metas=None,
        lane_targets=None,
        depth_map=None,
        depth_map_valid=None,
        gt_lanes_3d=None,
        gt_lane_labels=None,
        **kwargs,
    ):
        """Forward function for a single sample.
        
        Args:
            img (torch.Tensor): Images.
            points (List[torch.Tensor]): Point clouds for each sample.
            camera2ego (torch.Tensor): Transformation from camera to ego coordinate.
            lidar2ego (torch.Tensor): Transformation from LiDAR to ego coordinate.
            lidar2camera (torch.Tensor): Transformation from LiDAR to camera coordinate.
            lidar2image (torch.Tensor): Transformation from LiDAR to image coordinate.
            camera_intrinsics (torch.Tensor): Camera intrinsic parameters.
            camera2lidar (torch.Tensor): Camera to LiDAR transformation.
            img_aug_matrix (torch.Tensor): Image augmentation matrix.
            lidar_aug_matrix (torch.Tensor): Augmentation matrix for LiDAR.
            metas (List[dict]): Meta information for each sample.
            lane_targets (dict, optional): Lane annotation targets. Defaults to None.
            depth_map (torch.Tensor, optional): Depth map for depth supervision. Defaults to None.
            depth_map_valid (torch.Tensor, optional): Valid mask of depth map. Defaults to None.
            gt_lanes_3d (List[torch.Tensor], optional): Ground truth 3D lanes for evaluation.
                Defaults to None.
            gt_lane_labels (List[torch.Tensor], optional): Ground truth lane labels for evaluation.
                Defaults to None.
                
        Returns:
            Dict or List[Dict]: Loss dict for training or prediction results for inference.
        """
        
        features = []
        for sensor in (
            self.encoders if self.training else list(self.encoders.keys())[::-1]
        ):
            if sensor == "camera":
                # 支持7相机设置: ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
                # 深度图处理优先使用'120_front'相机（由LoadDenseDepthMapFromFile自动选择）
                feature = self.extract_camera_features(
                    img,
                    points,
                    camera2ego,
                    lidar2ego,
                    lidar2camera,
                    lidar2image,
                    camera_intrinsics,
                    camera2lidar,
                    img_aug_matrix,
                    lidar_aug_matrix,
                    metas,
                    depth_map=depth_map,
                )
                
                # CRITICAL DEBUG: Check camera feature dimensions
                # print(f"[FUSION_DEBUG] Camera feature shape: {feature.shape}")
                
                # Handle depth supervision if available from previous transform modules
                if self.training and not self.use_dense_depth_transform and depth_map is not None:
                    if hasattr(self.encoders["camera"]["vtransform"], "get_depth_loss"):
                        depth_losses = self.encoders["camera"]["vtransform"].get_depth_loss(
                            depth_map, depth_map_valid
                        )
                        for k, v in depth_losses.items():
                            if not hasattr(self, k):
                                setattr(self, k, v)
                            else:
                                getattr(self, k).update(v)
                
            elif sensor == "lidar":
                feature, x_for_seg = self.extract_lidar_features(points)
            else:
                raise ValueError(f"unsupported sensor: {sensor}")
            features.append(feature)
        
        if not self.training:
            # avoid OOM
            features = features[::-1]
        
        # CRITICAL DEBUG: Check feature alignment before fusion
        # print(f"[FUSION_DEBUG] ===== FEATURE FUSION CHECK =====")
        for i, feat in enumerate(features):
            sensor_name = "Camera" if i == 0 else "LiDAR"
            # print(f"[FUSION_DEBUG] {sensor_name} feature shape: {feat.shape}")
        # print(f"[FUSION_DEBUG] ================================")
        
        if self.fuser is not None:
            x = self.fuser(features)
        else:
            assert len(features) == 1, features
            x = features[0]
        
        if self.fuser is not None:
            x = self.fuser(features)
        else:
            assert len(features) == 1, features
            x = features[0]
        
        batch_size = x.shape[0]
        
        # === CRITICAL DEBUG: Track decoder backbone input ===
        # print(f"[DECODER_BACKBONE_DEBUG] === DECODER BACKBONE START ===")
        # print(f"[DECODER_BACKBONE_DEBUG] Input to decoder backbone: {x.shape}")
        
        x = self.decoder["backbone"](x)
        
        # === CRITICAL DEBUG: Track decoder backbone output dimensions ===
        # print(f"[DECODER_BACKBONE_DEBUG] Decoder backbone output:")
        if isinstance(x, (list, tuple)):
            for i, feat in enumerate(x):
                # print(f"[DECODER_BACKBONE_DEBUG]   Feature {i}: {feat.shape}")
                # Check if spatial dimensions are compatible with FPN expectations
                h, w = feat.shape[2], feat.shape[3]
                # if h % 4 != 0 or w % 4 != 0:
                #     print(f"[DECODER_BACKBONE_WARN] Feature {i} spatial dims ({h}, {w}) not divisible by 4!")
                #     print(f"[DECODER_BACKBONE_WARN] This may cause FPN upsampling alignment issues.")
                    
                # Predict potential downsampling pattern
                if i > 0:
                    prev_h, prev_w = x[i-1].shape[2], x[i-1].shape[3]
                    h_ratio = prev_h / h if h > 0 else float('inf')
                    w_ratio = prev_w / w if w > 0 else float('inf')
                    # print(f"[DECODER_BACKBONE_DEBUG] Downsampling ratio from level {i-1} to {i}: H={h_ratio:.2f}x, W={w_ratio:.2f}x")
        # else:
        #     print(f"[DECODER_BACKBONE_DEBUG]   Single feature: {x.shape}")
        
        # print(f"[DECODER_BACKBONE_DEBUG] === DECODER BACKBONE END ===")
        
        x = self.decoder["neck"](x)
        
        # Check if x is a list or tuple (outputs from FPN)
        if isinstance(x, (list, tuple)):
            main_feat = x[0]  # Usually we take the first (highest resolution) feature map
        else:
            main_feat = x
        
        # Prepare feature for lane detection
        if self.fuser_for_seg is not None and "lane" in self.heads:
            if 'lidar' in self.encoders:  # Replace 'sensor == "lidar"'
                # Use the seg features from lidar and main feature from decoder
                x_for_lane = self.fuser_for_seg([x_for_seg, main_feat])
            else:
                # Just use the main feature if no point coords available
                x_for_lane = self.fuser_for_seg([main_feat])
        else:
            x_for_lane = main_feat
        
        if self.training:
            outputs = {}
            
            # Handle depth loss from camera encoder if available
            if hasattr(self, "depth_loss"):
                for name, val in self.depth_loss.items():
                    outputs[f"loss/depth/{name}"] = val
            
            # Add lane detection losses
            if "lane" in self.heads:
                lane_head = self.heads["lane"]
                
                # Get predictions from head
                lane_preds = lane_head(x_for_lane, depth_features=x_for_seg if 'lidar' in self.encoders else None)
                
                # Process based on head type
                if isinstance(lane_preds, tuple) and len(lane_preds) in [4, 5]:
                    # This is a heatmap-based head (heatmap, offset, z, cls) or (heatmap, offset, z, cls, embed)
                    # print(f"[BEVFUSION_DEBUG] Processing heatmap-based head with {len(lane_preds)} predictions")
                    
                    # Pass lane_targets directly to the loss function
                    lane_losses = lane_head.loss(lane_preds, lane_targets)
                    
                    # Add losses to output
                    for name, val in lane_losses.items():
                        outputs[f"loss/lane/{name}"] = val
                
                # If lane_preds is a tuple, it's Y_3d_reg, Y_3d_cls, Y_3d_vis
                elif isinstance(lane_preds, tuple) and len(lane_preds) == 3:
                    # print(f"[BEVFUSION_DEBUG] Processing anchor-based head with 3 predictions")
                    Y_3d_reg, Y_3d_cls, Y_3d_vis = lane_preds
                    
                    # Process gt_lanes data into targets format needed by loss
                    if lane_targets is not None:
                        targets = lane_targets
                    elif gt_lanes_3d is not None and gt_lane_labels is not None:
                        # Prepare targets from gt data if lane_targets not available
                        targets = {
                            'lanes': gt_lanes_3d,
                            'labels': gt_lane_labels,
                            'visibilities': torch.ones_like(gt_lanes_3d[:, :, 0])  # Assume all visible by default
                        }
                    else:
                        targets = None
                    
                    if targets is not None:
                        lane_losses = lane_head.loss(Y_3d_reg, Y_3d_cls, Y_3d_vis, targets)
                        
                        # Add losses to output
                        for name, val in lane_losses.items():
                            outputs[f"loss/lane/{name}"] = val
            
            loss_outputs = {k: v for k, v in outputs.items() if 'loss' in k}
            # print(f"[BEVFUSION_DEBUG] Loss outputs:")
            # print(f"[BEVFUSION_DEBUG] === TRAINING FORWARD COMPLETE ===")
            return outputs
        else:
            # Inference mode
            outputs = [{} for _ in range(batch_size)]
            
            if "lane" in self.heads:
                lane_head = self.heads["lane"]
                
                # Get predictions from head
                lane_preds = lane_head(x_for_lane, depth_features=x_for_seg if 'lidar' in self.encoders else None)
                
                # Process based on head type
                if isinstance(lane_preds, tuple) and len(lane_preds) in [4, 5]:
                    # This is a heatmap-based head (heatmap, offset, z, cls) or (heatmap, offset, z, cls, embed)
                    lane_results = lane_head.get_lanes(lane_preds, metas)
                    
                    # Format results for each sample in the batch
                    for batch_idx, batch_results in enumerate(lane_results):
                        pred_lanes = []
                        pred_lane_types = []
                        pred_lane_scores = []
                        
                        for lane_instance in batch_results:
                            pred_lanes.append(lane_instance['points_3d'])
                            pred_lane_types.append(lane_instance['class_id'])
                            pred_lane_scores.append(lane_instance['score'])
                        
                        outputs[batch_idx].update({
                            "pred_lanes": pred_lanes,
                            "pred_lane_types": pred_lane_types,
                            "pred_scores": pred_lane_scores
                        })
                
                # If lane_preds is a tuple, it's Y_3d_reg, Y_3d_cls, Y_3d_vis
                elif isinstance(lane_preds, tuple) and len(lane_preds) == 3:
                    Y_3d_reg, Y_3d_cls, Y_3d_vis = lane_preds
                    
                    # Get final results
                    results = lane_head.get_results(Y_3d_reg, Y_3d_cls, Y_3d_vis)
                    
                    # Add to output
                    for k in range(batch_size):
                        outputs[k].update({
                            "pred_lanes": results[k]["pred_lanes"],
                            "pred_lane_types": results[k]["pred_lane_types"],
                            "pred_visibilities": results[k]["pred_visibilities"],
                            "pred_scores": results[k]["pred_scores"] if "pred_scores" in results[k] else None
                        })
                        
                        # Add ground truth if available (for visualization/evaluation)
                        if gt_lanes_3d is not None:
                            gt_lanes = gt_lanes_3d[k]
                            if isinstance(gt_lanes, torch.Tensor):
                                gt_lanes = gt_lanes.cpu()
                            outputs[k]["gt_lanes_3d"] = gt_lanes
                        
                        if gt_lane_labels is not None:
                            gt_labels = gt_lane_labels[k]
                            if isinstance(gt_labels, torch.Tensor):
                                gt_labels = gt_labels.cpu()
                            outputs[k]["gt_lane_labels"] = gt_labels
            
            return outputs 

    # 这两个方法可以被前面更全面的forward_single替代，保留它们是为了向后兼容
    def forward_train(self, img, points, depth_map, depth_map_valid, lane_targets, **data):
        """Training forward function wrapper that redirects to forward_single."""
        return self.forward(
            img=img, 
            points=points, 
            depth_map=depth_map, 
            depth_map_valid=depth_map_valid, 
            lane_targets=lane_targets, 
            **data
        )

    def forward_test(self, img, points, depth_map, depth_map_valid, **data):
        """Testing forward function wrapper that redirects to forward_single."""
        return self.forward(
            img=img, 
            points=points, 
            depth_map=depth_map, 
            depth_map_valid=depth_map_valid, 
            **data
        )
