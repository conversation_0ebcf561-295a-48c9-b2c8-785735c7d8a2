import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from mmcv.runner import force_fp32
from mmdet3d.models.builder import HEADS, build_loss
# from mmdet3d.models.utils import clip_sigmoid
# from mmdet3d.core.post_processing import nms_bev
import math

@HEADS.register_module()
class BEVLaneAnchorHead(nn.Module):
    """Anchor-based Head for 3D Lane Detection in BEVFusion.

    This head predicts 3D lane lines using an anchor-based approach inspired by
    M2-3DLaneNet. Each anchor represents a potential lane with N_y points along
    the y-axis, and the model predicts regression offsets (x, z), lane category,
    and visibility for each point.

    Args:
        in_channels (int): Number of input channels from BEV features.
        grid_conf (dict): BEV grid configuration with xbound and ybound.
        N_y (int): Number of points per lane anchor.
        anchor_x_min (float): Minimum x-coordinate for anchors.
        anchor_x_max (float): Maximum x-coordinate for anchors.
        dx_anchor (float): Step size for anchor placement in x. 
        num_classes (int): Number of lane categories (e.g., 14 for OpenLane).
        use_depth_features (bool): Whether to explicitly use depth features.
        nms_thres (float): IoU threshold for NMS.
        loss_reg (dict): Configuration for regression loss.
        loss_cls (dict): Configuration for classification loss.
        loss_vis (dict): Configuration for visibility loss.
    """

    def __init__(self,
                 in_channels,
                 grid_conf,
                 N_y=50,
                 anchor_x_min=-20.0,
                 anchor_x_max=20.0,
                 dx_anchor=1.0,
                 num_classes=14,
                 use_depth_features=True,
                 nms_thres=0.3,
                 loss_reg=dict(type='SmoothL1Loss', beta=1.0 / 9.0, reduction='mean', loss_weight=1.0),
                 loss_cls=dict(type='CrossEntropyLoss', use_sigmoid=False, loss_weight=1.0),
                 loss_vis=dict(type='BCEWithLogitsLoss', reduction='mean', loss_weight=1.0)):
        super(BEVLaneAnchorHead, self).__init__()
        self.in_channels = in_channels
        self.grid_conf = grid_conf
        self.N_y = N_y
        self.anchor_x_min = anchor_x_min
        self.anchor_x_max = anchor_x_max
        self.dx_anchor = dx_anchor
        self.num_classes = num_classes
        self.use_depth_features = use_depth_features
        self.nms_thres = nms_thres

        # Generate anchors
        self.anchors = self._generate_anchors()
        self.N_anchors = self.anchors.shape[0]

        # Build losses
        self.loss_reg = build_loss(loss_reg)
        self.loss_cls = build_loss(loss_cls)
        self.loss_vis = build_loss(loss_vis)

        # Calculate proper output dimensions
        W = int((grid_conf['xbound'][1] - grid_conf['xbound'][0]) / grid_conf['xbound'][2])
        
        # Define head layers
        self.conv1 = nn.Conv2d(in_channels, 256, kernel_size=3, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(256)
        self.conv2 = nn.Conv2d(256, 256, kernel_size=3, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(256)
        
        # Pool along H to reduce height dimension
        self.pool_h = nn.AdaptiveAvgPool2d((1, None))
        
        # Use 1x1 conv followed by fully connected layer to ensure correct output size
        self.conv_reduce = nn.Conv2d(256, 128, kernel_size=1)
        
        # Adaptive pooling to match anchor counts
        self.pool_w = nn.AdaptiveMaxPool2d((1, self.N_anchors))
        
        # Final prediction layers
        out_channels = 2 * N_y + num_classes + N_y  # x,z offsets + class + visibility
        self.pred_layer = nn.Conv2d(128, out_channels, kernel_size=1)
        
        # Optional depth-aware layer if using depth features
        if self.use_depth_features:
            self.depth_fusion = nn.Sequential(
                nn.Conv2d(256, 128, kernel_size=1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True),
                nn.Conv2d(128, 128, kernel_size=3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True)
            )

    def _generate_anchors(self):
        """Generate anchors as sequences of points in BEV space.

        Returns:
            torch.Tensor: Anchors of shape (N_anchors, N_y, 3) with (x, y, z).
        """
        y_min, y_max, _ = self.grid_conf['ybound']
        y_values = torch.linspace(y_min, y_max, self.N_y)
        x_min = self.anchor_x_min
        x_max = self.anchor_x_max
        N_anchors = int((x_max - x_min) / self.dx_anchor) + 1
        x_values = torch.linspace(x_min, x_max, N_anchors)
        anchors = torch.zeros(N_anchors, self.N_y, 3)
        for i in range(N_anchors):
            x_a = x_values[i]
            for k in range(self.N_y):
                y_k = y_values[k]
                anchors[i, k] = [x_a, y_k, 0]
        return anchors

    def forward(self, x, depth_features=None):
        """Forward pass to predict lane parameters.

        Args:
            x (torch.Tensor): Input BEV features of shape (B, C_in, H, W).
            depth_features (torch.Tensor, optional): Depth features if available.

        Returns:
            tuple: (Y_3d_reg, Y_3d_cls, Y_3d_vis)
                - Y_3d_reg: Regression offsets (B, N_anchors, N_y, 2).
                - Y_3d_cls: Classification logits (B, N_anchors, num_classes).
                - Y_3d_vis: Visibility logits (B, N_anchors, N_y).
        """
        # Initial feature extraction
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        
        # Integrate depth features if available and enabled
        if self.use_depth_features and depth_features is not None:
            depth_feats = self.depth_fusion(depth_features)
            x = x + depth_feats
        
        # Pool along height dimension
        x = self.pool_h(x)  # (B, 256, 1, W)
        
        # Reduce channels and adapt to anchor count
        x = F.relu(self.conv_reduce(x))
        x = self.pool_w(x)  # (B, 128, 1, N_anchors)
        
        # Final predictions
        x = self.pred_layer(x)  # (B, out_channels, 1, N_anchors)
        
        B = x.shape[0]
        
        # Split outputs with clear channel mappings
        # First 2*N_y channels are for x,z regression (2 values per point)
        Y_3d_reg = x[:, :2*self.N_y, 0, :].reshape(B, self.N_anchors, self.N_y, 2)
        
        # Next num_classes channels are for classification
        Y_3d_cls = x[:, 2*self.N_y:2*self.N_y+self.num_classes, 0, :].permute(0, 2, 1)
        
        # Last N_y channels are for visibility
        Y_3d_vis = x[:, 2*self.N_y+self.num_classes:, 0, :].reshape(B, self.N_anchors, self.N_y)

        return Y_3d_reg, Y_3d_cls, Y_3d_vis

    @force_fp32(apply_to=('Y_3d_reg', 'Y_3d_cls', 'Y_3d_vis'))
    def loss(self, Y_3d_reg, Y_3d_cls, Y_3d_vis, targets):
        """Compute losses for training.

        Args:
            Y_3d_reg (torch.Tensor): Predicted regression offsets (B, N_anchors, N_y, 2).
            Y_3d_cls (torch.Tensor): Predicted classification logits (B, N_anchors, num_classes).
            Y_3d_vis (torch.Tensor): Predicted visibility logits (B, N_anchors, N_y).
            targets (dict): Ground truth with keys:
                - lanes: (B, max_num_lanes, N_y, 3) for (x, y, z).
                - labels: (B, max_num_lanes) for lane types.
                - visibilities: (B, max_num_lanes, N_y) for visibility flags.

        Returns:
            dict: Loss dictionary with regression, classification, and visibility losses.
        """
        loss_dict = {}
        B = Y_3d_reg.shape[0]
        gt_lanes = targets['lanes']  # (B, max_num_lanes, N_y, 3)
        gt_labels = targets['labels']  # (B, max_num_lanes)
        gt_vis = targets['visibilities']  # (B, max_num_lanes, N_y)

        # Initialize loss accumulators
        reg_loss = 0.0
        cls_loss = 0.0
        vis_loss = 0.0
        num_pos = 0

        for b in range(B):
            # Get valid ground truth lanes
            valid_mask = gt_labels[b] >= 0
            if not valid_mask.any():
                continue

            gt_lanes_b = gt_lanes[b, valid_mask]  # (num_lanes, N_y, 3)
            gt_labels_b = gt_labels[b, valid_mask]  # (num_lanes,)
            gt_vis_b = gt_vis[b, valid_mask]  # (num_lanes, N_y)

            # Assign ground truth lanes to anchors with improved strategy
            assignments, iou_scores = self._assign_anchors_improved(gt_lanes_b)  # (num_lanes,)

            # Prepare targets
            reg_targets = torch.zeros_like(Y_3d_reg[b])  # (N_anchors, N_y, 2)
            cls_targets = torch.full((self.N_anchors,), -1, dtype=torch.long, device=Y_3d_reg.device)
            vis_targets = torch.zeros_like(Y_3d_vis[b])  # (N_anchors, N_y)

            # Create used_anchors set to track assigned anchors
            used_anchors = set()

            # Sort assignments by IoU to prioritize best matches
            sorted_indices = torch.argsort(iou_scores, descending=True)
            
            for lane_idx in sorted_indices:
                anchor_idx = assignments[lane_idx]
                if anchor_idx < 0:
                    continue
                    
                # Check if this anchor is already assigned
                if anchor_idx in used_anchors:
                    # Find the next best anchor
                    distances = torch.abs(self.anchors[:, :, 0] - gt_lanes_b[lane_idx, :, 0][None, :]).mean(dim=1)
                    for alt_anchor_idx in torch.argsort(distances):
                        if alt_anchor_idx.item() not in used_anchors:
                            anchor_idx = alt_anchor_idx.item()
                            used_anchors.add(anchor_idx)
                            break
                else:
                    used_anchors.add(anchor_idx)
                
                # Continue if no available anchor found
                if anchor_idx in used_anchors:
                    # Regression targets: (x_gt - x_a, z_gt)
                    reg_targets[anchor_idx, :, 0] = gt_lanes_b[lane_idx, :, 0] - self.anchors[anchor_idx, :, 0]
                    reg_targets[anchor_idx, :, 1] = gt_lanes_b[lane_idx, :, 2]
                    cls_targets[anchor_idx] = gt_labels_b[lane_idx]
                    vis_targets[anchor_idx] = gt_vis_b[lane_idx]

            # Compute losses
            pos_mask = cls_targets >= 0
            if pos_mask.any():
                num_pos += pos_mask.sum()
                reg_loss += self.loss_reg(Y_3d_reg[b, pos_mask], reg_targets[pos_mask])
                cls_loss += self.loss_cls(Y_3d_cls[b, pos_mask], cls_targets[pos_mask])
                vis_loss += self.loss_vis(Y_3d_vis[b, pos_mask].view(-1), vis_targets[pos_mask].view(-1))

        # Average losses
        if num_pos > 0:
            reg_loss /= num_pos
            cls_loss /= num_pos
            vis_loss /= num_pos
        else:
            reg_loss = Y_3d_reg.new_tensor(0.0)
            cls_loss = Y_3d_cls.new_tensor(0.0)
            vis_loss = Y_3d_vis.new_tensor(0.0)

        loss_dict['loss_reg'] = reg_loss
        loss_dict['loss_cls'] = cls_loss
        loss_dict['loss_vis'] = vis_loss
        loss_dict['loss'] = reg_loss + cls_loss + vis_loss

        return loss_dict

    def _assign_anchors_improved(self, gt_lanes):
        """Assign ground truth lanes to anchors with improved strategy.
        
        Takes into account both distance and shape similarity.

        Args:
            gt_lanes (torch.Tensor): Ground truth lanes (num_lanes, N_y, 3).

        Returns:
            tuple: (assignments, iou_scores) where:
                - assignments is a list of anchor indices for each lane
                - iou_scores is the matching score for each assignment
        """
        num_lanes = gt_lanes.shape[0]
        assignments = []
        iou_scores = torch.zeros(num_lanes, device=gt_lanes.device)
        
        for i in range(num_lanes):
            gt_x = gt_lanes[i, :, 0]  # (N_y,)
            gt_z = gt_lanes[i, :, 2]  # (N_y,)
            
            # Distance term (as before)
            distances = torch.abs(self.anchors[:, :, 0] - gt_x[None, :])  # (N_anchors, N_y)
            mean_distances = distances.mean(dim=1)  # (N_anchors,)
            
            # Shape similarity term (consider z values and curve shape)
            similarity_scores = torch.zeros(self.N_anchors, device=gt_lanes.device)
            
            for a in range(self.N_anchors):
                # Calculate a shape similarity score based on x-profile
                x_diff = gt_x - self.anchors[a, :, 0]
                x_profile_similarity = torch.exp(-torch.var(x_diff) / 5.0)  # Scale factor 5.0 is empirical
                
                # Combine with distance
                similarity_scores[a] = x_profile_similarity
            
            # Combine distance and similarity (lower distance is better)
            combined_scores = -mean_distances + similarity_scores
            anchor_idx = combined_scores.argmax()
            assignments.append(anchor_idx.item())
            
            # Calculate an IoU-like score for sorting
            # Best score is 1.0 (perfect match), worst approaches 0
            best_distance = mean_distances.min()
            normalized_distance = torch.exp(-best_distance / 10.0)  # Scale factor 10.0 is empirical
            iou_scores[i] = normalized_distance * similarity_scores[anchor_idx]
            
        return assignments, iou_scores

    def get_results(self, Y_3d_reg, Y_3d_cls, Y_3d_vis, conf_threshold=0.5):
        """Generate final lane predictions during inference with NMS.

        Args:
            Y_3d_reg (torch.Tensor): Predicted regression offsets (B, N_anchors, N_y, 2).
            Y_3d_cls (torch.Tensor): Predicted classification logits (B, N_anchors, num_classes).
            Y_3d_vis (torch.Tensor): Predicted visibility logits (B, N_anchors, N_y).
            conf_threshold (float): Confidence threshold for filtering predictions.

        Returns:
            list[dict]: Predictions for each batch sample with keys:
                - pred_lanes: List of 3D lane points (N_points, 3).
                - pred_lane_types: List of lane type indices.
                - pred_visibilities: List of visibility flags.
                - pred_scores: Confidence scores for each lane.
        """
        B = Y_3d_reg.shape[0]
        results = []

        for b in range(B):
            result_dict = {
                'pred_lanes': [], 
                'pred_lane_types': [], 
                'pred_visibilities': [],
                'pred_scores': []
            }
            
            cls_probs = F.softmax(Y_3d_cls[b], dim=-1)  # (N_anchors, num_classes)
            vis_probs = torch.sigmoid(Y_3d_vis[b])  # (N_anchors, N_y)
            
            # Collect all valid predictions before NMS
            valid_predictions = []
            
            for i in range(self.N_anchors):
                max_prob, lane_type = cls_probs[i].max(dim=-1)
                if max_prob < conf_threshold:
                    continue

                # Apply regression offsets
                pred_points = self.anchors[i].clone()  # (N_y, 3)
                pred_points[:, 0] += Y_3d_reg[b, i, :, 0]  # x = x_a + Δx
                pred_points[:, 2] = Y_3d_reg[b, i, :, 1]  # z = Δz
                vis_flags = (vis_probs[i] > 0.5).float()  # (N_y,)
                
                # Create prediction entry
                pred = {
                    'points': pred_points,
                    'lane_type': lane_type.item(),
                    'visibility': vis_flags,
                    'score': max_prob.item()
                }
                valid_predictions.append(pred)
            
            # Apply NMS if we have valid predictions
            if valid_predictions:
                # Convert to format for NMS
                boxes = []
                for pred in valid_predictions:
                    # Get the bounding box of the lane in BEV
                    visible_points = pred['points'][pred['visibility'] > 0.5]
                    if len(visible_points) < 2:  # Need at least 2 points
                        # Use all points if not enough visible ones
                        visible_points = pred['points']
                        
                    x_min = visible_points[:, 0].min().item()
                    x_max = visible_points[:, 0].max().item()
                    y_min = visible_points[:, 1].min().item()
                    y_max = visible_points[:, 1].max().item()
                    
                    # Format: [x_min, y_min, x_max, y_max, score]
                    boxes.append([x_min, y_min, x_max, y_max, pred['score']])
                
                if boxes:
                    boxes = torch.tensor(boxes, device=Y_3d_reg.device)
                    # Get NMS indices
                    keep_indices = self._lane_nms(boxes, self.nms_thres)
                    
                    # Keep only the predictions that survived NMS
                    for idx in keep_indices:
                        pred = valid_predictions[idx]
                        result_dict['pred_lanes'].append(pred['points'])
                        result_dict['pred_lane_types'].append(pred['lane_type'])
                        result_dict['pred_visibilities'].append(pred['visibility'])
                        result_dict['pred_scores'].append(pred['score'])
            
            results.append(result_dict)

        return results
        
    def _lane_nms(self, boxes, iou_threshold):
        """Non-maximum suppression for lanes based on BEV boxes.
        
        Args:
            boxes (torch.Tensor): Boxes in format [x_min, y_min, x_max, y_max, score].
            iou_threshold (float): IoU threshold for suppression.
            
        Returns:
            list: Indices of boxes to keep after NMS.
        """
        if boxes.shape[0] == 0:
            return []
            
        # Extract coordinates and scores
        x1 = boxes[:, 0]
        y1 = boxes[:, 1]
        x2 = boxes[:, 2]
        y2 = boxes[:, 3]
        scores = boxes[:, 4]
        
        # Calculate areas
        areas = (x2 - x1) * (y2 - y1)
        
        # Sort by score
        order = scores.argsort(descending=True)
        
        keep = []
        while order.size(0) > 0:
            # Pick the box with highest score
            i = order[0].item()
            keep.append(i)
            
            # If only one box left, break
            if order.size(0) == 1:
                break
                
            # Calculate IoU with remaining boxes
            xx1 = torch.max(x1[i], x1[order[1:]])
            yy1 = torch.max(y1[i], y1[order[1:]])
            xx2 = torch.min(x2[i], x2[order[1:]])
            yy2 = torch.min(y2[i], y2[order[1:]])
            
            # Calculate intersection area
            w = torch.clamp(xx2 - xx1, min=0.0)
            h = torch.clamp(yy2 - yy1, min=0.0)
            inter = w * h
            
            # Calculate IoU
            iou = inter / (areas[i] + areas[order[1:]] - inter)
            
            # Keep boxes with IoU below threshold
            inds = torch.where(iou <= iou_threshold)[0]
            order = order[inds + 1]
            
        return keep