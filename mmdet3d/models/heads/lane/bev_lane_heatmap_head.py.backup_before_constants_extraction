import torch
import torch.nn as nn
import torch.nn.functional as F
from mmcv.cnn import ConvModule
from mmcv.runner import BaseModule, force_fp32
import numpy as np
from sklearn.cluster import DBSCAN
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import os

from mmdet3d.models.builder import HEADS, build_loss
from mmdet.models.builder import LOSSES


# === Logging Configuration ===
import logging

# 创建logger
logger = logging.getLogger(__name__)

# 配置日志级别（可通过环境变量控制）
import os
log_level = os.getenv('BEV_LANE_LOG_LEVEL', 'WARNING').upper()
logger.setLevel(getattr(logging, log_level, logging.WARNING))

# 如果还没有handler，添加一个
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# 防止重复日志
logger.propagate = False



@LOSSES.register_module()
class DiscriminativeLoss(nn.Module):
    """Push-Pull loss for instance embeddings.
    
    This loss encourages embeddings from the same instance to be close together
    (pull) and embeddings from different instances to be far apart (push).
    """
    
    def __init__(self, 
                 delta_v=0.5, 
                 delta_d=1.5, 
                 norm=1, 
                 alpha=1.0, 
                 beta=1.0, 
                 gamma=0.001, 
                 loss_weight=1.0,
                 cls_consistency_weight=0.1,
                 **kwargs):
        super(DiscriminativeLoss, self).__init__()
        self.delta_v = delta_v
        self.delta_d = delta_d
        self.norm = norm
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.loss_weight = loss_weight
        self.weight = loss_weight  # Fix: Map loss_weight to weight for forward method
        self.cls_consistency_weight = cls_consistency_weight
    
    @force_fp32()
    def forward(self, embeddings, instance_ids, valid_mask=None):
        """Vectorized forward pass for the discriminative loss."""
        batch_size, embed_dim, height, width = embeddings.size()
        
        # =============================================================================
        # CRITICAL DTYPE FIX: Ensure proper tensor dtypes for bitwise operations
        # =============================================================================
        # logger.debug(f"[EMBEDDING_LOSS_DEBUG] === DISCRIMINATIVE LOSS FORWARD ===")
        # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Input tensor dtypes:")
        # logger.debug(f"[EMBEDDING_LOSS_DEBUG]   embeddings: {embeddings.dtype}, shape: {embeddings.shape}")
        # logger.debug(f"[EMBEDDING_LOSS_DEBUG]   instance_ids: {instance_ids.dtype}, shape: {instance_ids.shape}")
        # if valid_mask is not None:
        #     logger.debug(f"[EMBEDDING_LOSS_DEBUG]   valid_mask: {valid_mask.dtype}, shape: {valid_mask.shape}")
        #     logger.debug(f"[EMBEDDING_LOSS_DEBUG]   valid_mask range: [{valid_mask.min():.3f}, {valid_mask.max():.3f}]")
        
        # Reshape tensors for batch processing
        embeddings = embeddings.permute(0, 2, 3, 1).reshape(batch_size, -1, embed_dim)
        instance_ids = instance_ids.reshape(batch_size, -1)
        
        if valid_mask is not None:
            valid_mask = valid_mask.reshape(batch_size, -1)
            # CRITICAL FIX: Convert float mask to boolean for bitwise operations
            if valid_mask.dtype != torch.bool:
                # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Converting valid_mask from {valid_mask.dtype} to bool")
                valid_mask = valid_mask > self.THRESHOLD_HALF  # Convert to boolean
                # logger.debug(f"[EMBEDDING_LOSS_DEBUG] After conversion: {valid_mask.dtype}")
        else:
            valid_mask = torch.ones_like(instance_ids, dtype=torch.bool)
            # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Created default valid_mask with dtype: {valid_mask.dtype}")
        
        # Debug instance ID statistics
        unique_instances = torch.unique(instance_ids)
        # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Instance ID statistics:")
        # logger.debug(f"[EMBEDDING_LOSS_DEBUG]   Unique instance IDs: {unique_instances.tolist()}")
        # logger.debug(f"[EMBEDDING_LOSS_DEBUG]   Valid mask positive count: {valid_mask.sum().item()}")
        
        # Check foreground instance availability
        foreground_mask = valid_mask & (instance_ids > 0)
        # logger.debug(f"[EMBEDDING_LOSS_DEBUG]   Foreground instances count: {foreground_mask.sum().item()}")
        
        if foreground_mask.sum() == 0:
            # logger.debug(f"[EMBEDDING_LOSS_WARN] No foreground instances found - returning zero loss")
            zero_loss = torch.tensor(0., device=embeddings.device, requires_grad=True)
            return {
                'loss': zero_loss,
                'pull_loss': zero_loss,
                'push_loss': zero_loss,
                'reg_loss': zero_loss
            }
            
        pull_loss = torch.tensor(0., device=embeddings.device, requires_grad=True)
        push_loss = torch.tensor(0., device=embeddings.device, requires_grad=True)
        reg_loss = torch.tensor(0., device=embeddings.device, requires_grad=True)
        
        valid_batches = 0
        
        for b in range(batch_size):
            # Select valid foreground points for the current sample (instance_ids > 0)
            # FIXED: Now both tensors are boolean, so bitwise AND will work
            sample_mask = valid_mask[b] & (instance_ids[b] > 0)
            
            # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Batch {b}: sample_mask sum = {sample_mask.sum().item()}")
            
            if not sample_mask.any():
                # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Batch {b}: No valid samples, skipping")
                continue
                
            sample_embeddings = embeddings[b][sample_mask]
            sample_instances = instance_ids[b][sample_mask]
            
            # Get unique instances and their counts
            unique_instances, unique_inverse, unique_counts = torch.unique(
                sample_instances, return_inverse=True, return_counts=True
            )
            num_instances = unique_instances.size(0)
            
            # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Batch {b}: {num_instances} unique instances")
            # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Batch {b}: Instance counts: {unique_counts.tolist()}")

            if num_instances <= 1:
                # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Batch {b}: Not enough instances for discriminative loss")
                continue

            valid_batches += 1

            # --- Calculate instance centroids (means) using scatter_add for efficiency ---
            # DTYPE_FIX: Ensure sum_embeddings has the same dtype as sample_embeddings
            sum_embeddings = torch.zeros(num_instances, embed_dim, device=embeddings.device, dtype=sample_embeddings.dtype)
            # logger.debug(f"[DTYPE_DEBUG] sum_embeddings dtype: {sum_embeddings.dtype}, sample_embeddings dtype: {sample_embeddings.dtype}")
            sum_embeddings.scatter_add_(0, unique_inverse.unsqueeze(1).expand_as(sample_embeddings), sample_embeddings)
            centroids = sum_embeddings / (unique_counts.unsqueeze(1).float() + self.EPSILON_SMALL)

            # --- Calculate Pull Loss (intra-instance variance) ---
            dists_to_centroids = torch.norm(sample_embeddings - centroids[unique_inverse], p=self.norm, dim=1)
            pull_per_point = torch.clamp(dists_to_centroids - self.delta_v, min=0)**2
            # DTYPE_FIX: Ensure pull_per_instance has the same dtype as pull_per_point
            pull_per_instance = torch.zeros(num_instances, device=embeddings.device, dtype=pull_per_point.dtype)
            pull_per_instance.scatter_add_(0, unique_inverse, pull_per_point)
            # Average variance per instance, then average over all instances
            batch_pull_loss = torch.mean(pull_per_instance / (unique_counts.float() + self.EPSILON_SMALL))
            pull_loss = pull_loss + batch_pull_loss

            # --- Calculate Push Loss (inter-instance distance) ---
            # Pairwise distance between centroids
            centroid_dists = torch.cdist(centroids, centroids, p=self.norm)
            # Penalize centroids that are too close, ensuring we only consider each pair once (upper triangle)
            push = torch.clamp(2 * self.delta_d - centroid_dists, min=0)**2
            batch_push_loss = torch.sum(torch.triu(push, diagonal=1)) / (num_instances * (num_instances - 1) / 2 + self.EPSILON_SMALL)
            push_loss = push_loss + batch_push_loss
            
            # --- Calculate Regularization Loss ---
            batch_reg_loss = torch.mean(torch.norm(centroids, p=self.norm, dim=1))
            reg_loss = reg_loss + batch_reg_loss
            
            # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Batch {b} losses: pull={batch_pull_loss.item():.6f}, push={batch_push_loss.item():.6f}, reg={batch_reg_loss.item():.6f}")

        # Average losses over valid batches
        if valid_batches > 0:
            pull_loss = pull_loss / valid_batches
            push_loss = push_loss / valid_batches
            reg_loss = reg_loss / valid_batches
            
            # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Final averaged losses over {valid_batches} batches:")
            # logger.debug(f"[EMBEDDING_LOSS_DEBUG]   pull_loss: {pull_loss.item():.6f}")
            # logger.debug(f"[EMBEDDING_LOSS_DEBUG]   push_loss: {push_loss.item():.6f}")
            # logger.debug(f"[EMBEDDING_LOSS_DEBUG]   reg_loss: {reg_loss.item():.6f}")

        # Combine losses with weights
        total_loss = (self.alpha * pull_loss + self.beta * push_loss + self.gamma * reg_loss) * self.weight

        # logger.debug(f"[EMBEDDING_LOSS_DEBUG] Total loss: {total_loss.item():.6f}")
        # logger.debug(f"[EMBEDDING_LOSS_DEBUG] =======================================")

        return {
            'loss': total_loss,
            'pull_loss': self.alpha * pull_loss,
            'push_loss': self.beta * push_loss,
            'reg_loss': self.gamma * reg_loss
        }


@HEADS.register_module()
class BEVLaneHeatmapHead(BaseModule):

    # === 类常量定义 ===
    # 数学常量
    COS_45_DEGREES = self.COS_45_DEGREES  # cos(45°) 用于角度一致性检查
    
    # 阈值常量
    THRESHOLD_HALF = self.THRESHOLD_HALF  # 阈值常量，用于掩码转换和有效性检查
    TOLERANCE_SMALL = 0.1  # 小的容差值，用于比较操作
    
    # 数值稳定性常量
    EPSILON_SMALL = self.EPSILON_SMALL  # 小的epsilon值，用于防止除零错误
    
    # Discriminative Loss 参数
    DELTA_V_DEFAULT = 0.5   # 默认delta_v参数
    DELTA_D_DEFAULT = self.DELTA_D_DEFAULT   # 默认delta_d参数
    GAMMA_DEFAULT = self.GAMMA_DEFAULT   # 默认gamma参数
    ALPHA_DEFAULT = 1.0     # 默认alpha参数
    BETA_DEFAULT = 1.0      # 默认beta参数
    NORM_DEFAULT = 1        # 默认norm参数
    LOSS_WEIGHT_DEFAULT = 1.0  # 默认损失权重
    
    # 编码常量
    CLASS_MULTIPLIER = 100  # 类别感知实例ID的乘数
    
    # 配置常量
    DEFAULT_LINE_LIMIT = 1000  # 默认行数限制
    
    # 计算常量
    PERCENT_MULTIPLIER = 100.0  # 百分比计算乘数
    
    """Anchor-free BEV lane detection head using heatmap-based approach.
    
    This head predicts lane lines as a set of keypoints at predefined longitudinal (y) positions.
    For each y-position, it predicts:
    1. A heatmap indicating lane presence probability
    2. Local y-offsets for precise localization
    3. Z-height values for 3D reconstruction
    4. Lane classification logits for lane type
    5. Instance embeddings for lane grouping (new!)
    
    This representation is efficient and easily interpretable, making it suitable for
    real-time inference on edge devices like NVIDIA Orin.
    """

    def __init__(self,
                 in_channels=256,
                 cls_consistency_weight=0.1,
                 feat_channels=64,
                 num_classes=13,
                 grid_conf=None,
                 task_area_scope=None,
                 row_points=100,
                 z_range=[-1.0, 3.0],
                 max_lanes=20,
                 hm_thres=0.5,
                 nms_kernel_size=5,
                 use_sigmoid=True,
                 loss_heatmap=None,
                 loss_offset=None,
                 loss_z=None,
                 loss_cls=None,
                 loss_embedding=None,
                 use_embedding=False,
                 embedding_dim=8,
                 group_lanes=True,
                 lane_group_min_distance=1.0,
                 clustering_method='dbscan',
                 clustering_epsilon=0.5,
                 clustering_min_points=3,
                 debug_clustering=False,
                 init_cfg=None,
                 **kwargs):
        """Initialize BEVLaneHeatmapHead.
        
        Args:
            in_channels (int): Number of input channels.
            feat_channels (int): Number of feature channels in conv layers.
            num_classes (int): Number of lane classes.
            grid_conf (dict): Configuration of BEV grid, including dimensions and ranges.
            task_area_scope (dict): Task-specific area configuration for BEV cropping.
                Should contain:
                - x_range (list): [x_min, x_max] in meters (forward direction)
                - y_range (list): [y_min, y_max] in meters (lateral direction)  
                - z_range (list): [z_min, z_max] in meters (vertical direction)
                - enable_crop (bool): Whether to enable BEV feature cropping
                - crop_method (str): Cropping method ('slice', 'interpolate', 'conv')
            row_points (int): Number of points to sample along x-axis (longitudinal).
            z_range (list): Range of z-height prediction [min, max].
            max_lanes (int): Maximum number of lane instances to detect.
            hm_thres (float): Heatmap threshold for detecting lane points.
            nms_kernel_size (int): Kernel size for NMS on heatmap.
            use_sigmoid (bool): Whether to use sigmoid for heatmap.
            loss_heatmap (dict): Config of heatmap loss.
            loss_offset (dict): Config of offset loss.
            loss_z (dict): Config of z-height loss.
            loss_cls (dict): Config of classification loss.
            loss_embedding (dict): Config of embedding loss.
            use_embedding (bool): Whether to use learned embeddings for lane grouping.
            embedding_dim (int): Dimension of embedding vectors.
            group_lanes (bool): Whether to group lane points into lane instances.
            lane_group_min_distance (float): Minimum distance (in meters) to group lane points.
            clustering_method (str): Method for clustering embeddings ('dbscan' or 'geometric').
            clustering_epsilon (float): Epsilon parameter for DBSCAN clustering.
            clustering_min_points (int): Minimum points parameter for DBSCAN clustering.
            debug_clustering (bool): Whether to enable debug visualization for clustering.
            init_cfg (dict): Initialization config.
        """
        super(BEVLaneHeatmapHead, self).__init__(init_cfg=init_cfg)
        self.in_channels = in_channels
        self.feat_channels = feat_channels
        self.num_classes = num_classes
        self.row_points = row_points
        self.z_range = z_range
        self.max_lanes = max_lanes
        self.hm_thres = hm_thres
        self.nms_kernel_size = nms_kernel_size
        self.use_sigmoid = use_sigmoid
        self.group_lanes = group_lanes
        self.lane_group_min_distance = lane_group_min_distance
        
        # New parameters for instance embedding
        self.use_embedding = use_embedding
        self.embedding_dim = embedding_dim
        self.clustering_method = clustering_method
        self.clustering_epsilon = clustering_epsilon
        self.clustering_min_points = clustering_min_points
        self.debug_clustering = debug_clustering
        
        # Validate grid configuration
        if grid_conf is None:
            raise ValueError("`grid_conf` must be provided to define BEV grid dimensions.")
        required_keys = ['xbound', 'ybound']
        for key in required_keys:
            if key not in grid_conf:
                raise ValueError(f"`grid_conf` is missing required key: `{key}`")
        
        self.grid_conf = grid_conf
        
        # Extract global grid configuration
        self.global_x_min = grid_conf['xbound'][0]
        self.global_x_max = grid_conf['xbound'][1]
        self.x_res = grid_conf['xbound'][2]
        self.global_y_min = grid_conf['ybound'][0]
        self.global_y_max = grid_conf['ybound'][1]
        self.y_res = grid_conf['ybound'][2]
        
        # Global grid dimensions
        self.global_nx = int((self.global_x_max - self.global_x_min) / self.x_res)
        self.global_ny = int((self.global_y_max - self.global_y_min) / self.y_res)
        
        # === Task Area Scope Configuration ===
        self.task_area_scope = task_area_scope or {}
        self.enable_crop = self.task_area_scope.get('enable_crop', False)
        self.crop_method = self.task_area_scope.get('crop_method', 'slice')
        
        if self.enable_crop:
            # Task-specific area bounds
            task_x_range = self.task_area_scope.get('x_range', [self.global_x_min, self.global_x_max])
            task_y_range = self.task_area_scope.get('y_range', [self.global_y_min, self.global_y_max])
            
            self.task_x_min, self.task_x_max = task_x_range
            self.task_y_min, self.task_y_max = task_y_range
            
            # Compute crop indices for BEV feature tensor
            self._compute_crop_indices()
            
            # Task area grid dimensions (after cropping)
            self.nx = int((self.task_x_max - self.task_x_min) / self.x_res)
            self.ny = int((self.task_y_max - self.task_y_min) / self.y_res)
            
            # Use task area bounds for sampling
            self.x_min, self.x_max = self.task_x_min, self.task_x_max
            self.y_min, self.y_max = self.task_y_min, self.task_y_max
            
            logger.info(f"[BEV_HEAD_INIT] Task area cropping enabled:")
            logger.info(f"[BEV_HEAD_INIT]   Global BEV: [{self.global_x_min:.1f}, {self.global_y_min:.1f}] to [{self.global_x_max:.1f}, {self.global_y_max:.1f}]")
            logger.info(f"[BEV_HEAD_INIT]   Task area: [{self.task_x_min:.1f}, {self.task_y_min:.1f}] to [{self.task_x_max:.1f}, {self.task_y_max:.1f}]")
            logger.info(f"[BEV_HEAD_INIT]   Crop indices: x[{self.crop_x_start}:{self.crop_x_end}], y[{self.crop_y_start}:{self.crop_y_end}]")
            logger.info(f"[BEV_HEAD_INIT]   Task grid size: [{self.nx}, {self.ny}] (vs global [{self.global_nx}, {self.global_ny}])")
        else:
            # Use global bounds
            self.x_min, self.x_max = self.global_x_min, self.global_x_max
            self.y_min, self.y_max = self.global_y_min, self.global_y_max
            self.nx, self.ny = self.global_nx, self.global_ny
            logger.info(f"[BEV_HEAD_INIT] Using global BEV grid: [{self.nx}, {self.ny}]")
        
        # Build network
        self._build_layers()
        
        # === 详细调试信息：BEV头配置总结 ===
        logger.info(f"[BEV_HEAD_INIT] ========== BEV Lane Head Configuration ===========")
        logger.info(f"[BEV_HEAD_INIT] Network configuration:")
        logger.info(f"[BEV_HEAD_INIT]   in_channels: {self.in_channels}, feat_channels: {self.feat_channels}")
        logger.info(f"[BEV_HEAD_INIT]   num_classes: {self.num_classes}, use_embedding: {self.use_embedding}")
        logger.info(f"[BEV_HEAD_INIT] Grid configuration:")
        logger.info(f"[BEV_HEAD_INIT]   Global grid: [{self.global_nx}, {self.global_ny}] = {self.global_nx * self.global_ny} cells")
        logger.info(f"[BEV_HEAD_INIT]   Global bounds: X[{self.global_x_min}, {self.global_x_max}], Y[{self.global_y_min}, {self.global_y_max}]")
        logger.info(f"[BEV_HEAD_INIT]   Resolution: x_res={self.x_res}, y_res={self.y_res}")
        logger.info(f"[BEV_HEAD_INIT] Task area configuration:")
        logger.info(f"[BEV_HEAD_INIT]   Crop enabled: {self.enable_crop}")
        if self.enable_crop:
            logger.info(f"[BEV_HEAD_INIT]   Crop method: {self.crop_method}")
            logger.info(f"[BEV_HEAD_INIT]   Task bounds: X[{self.task_x_min}, {self.task_x_max}], Y[{self.task_y_min}, {self.task_y_max}]")
            logger.info(f"[BEV_HEAD_INIT]   Task grid: [{self.nx}, {self.ny}] = {self.nx * self.ny} cells")
            logger.info(f"[BEV_HEAD_INIT]   Compression ratio: {(self.nx * self.ny) / (self.global_nx * self.global_ny):.3f}")
        else:
            logger.info(f"[BEV_HEAD_INIT]   Using full global grid: [{self.nx}, {self.ny}]")
        logger.info(f"[BEV_HEAD_INIT] Effective BEV bounds: X[{self.x_min}, {self.x_max}], Y[{self.y_min}, {self.y_max}]")
        logger.info(f"[BEV_HEAD_INIT] Effective grid size: [{self.nx}, {self.ny}]")
        logger.info(f"[BEV_HEAD_INIT] =================================================")
        
        # Define losses
        from mmdet.models.losses import GaussianFocalLoss, L1Loss, CrossEntropyLoss
        
        self.loss_heatmap = GaussianFocalLoss(reduction='mean') if loss_heatmap is None else \
            build_loss(loss_heatmap)
        self.loss_offset = L1Loss(reduction='mean') if loss_offset is None else \
            build_loss(loss_offset)
        self.loss_z = L1Loss(reduction='mean') if loss_z is None else \
            build_loss(loss_z)
        self.loss_cls = CrossEntropyLoss(use_sigmoid=False, reduction='mean', ignore_index=0) if loss_cls is None else \
            build_loss(loss_cls)
        
        # Initialize embedding loss if using embeddings
        if self.use_embedding:
            if loss_embedding is None:
                self.loss_embedding = DiscriminativeLoss()
            else:
                # Check if 'type' is in loss_embedding
                if isinstance(loss_embedding, dict) and 'type' not in loss_embedding:
                    # If no type is provided, directly use DiscriminativeLoss with provided params
                    delta_v = loss_embedding.get('delta_v', 0.5)
                    delta_d = loss_embedding.get('delta_d', 1.5)
                    norm = loss_embedding.get('norm', 1)
                    alpha = loss_embedding.get('alpha', 1.0)
                    beta = loss_embedding.get('beta', 1.0)
                    gamma = loss_embedding.get('gamma', 0.001)
                    weight = loss_embedding.get('loss_weight', 1.0)
                    self.loss_embedding = DiscriminativeLoss(
                        delta_v=delta_v,
                        delta_d=delta_d,
                        norm=norm,
                        alpha=alpha,
                        beta=beta,
                        gamma=gamma,
                        weight=weight
                    )
                else:
                    self.loss_embedding = build_loss(loss_embedding)
        
        # CORRECT: Pre-compute x-positions for sampling (along forward direction)
        # 车道线沿x轴(前进方向)延伸，应该在固定x位置进行采样
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        x_vals = torch.linspace(self.x_min, self.x_max, self.row_points, device=device)
        self.register_buffer('x_positions', x_vals)
        logger.info(f"[BEV_HEAD_INIT] Registered x_positions buffer: {self.x_min} to {self.x_max}, {self.row_points} points")
        
        # === 关键配置验证检查 ===
        logger.debug(f"[CONFIG_VALIDATION] === 配置验证 ===")
        logger.debug(f"[CONFIG_VALIDATION] 全局网格尺寸: [{self.global_nx}, {self.global_ny}]")
        if self.enable_crop:
            task_x_range = [self.task_x_min, self.task_x_max]
            task_y_range = [self.task_y_min, self.task_y_max]
            logger.debug(f"[CONFIG_VALIDATION] 任务区域物理范围: X{task_x_range}m, Y{task_y_range}m")
            logger.debug(f"[CONFIG_VALIDATION] 任务区域像素数: [{self.nx}, {self.ny}]")
            logger.debug(f"[CONFIG_VALIDATION] 等效分辨率: X{(task_x_range[1]-task_x_range[0])/self.nx:.3f}m, Y{(task_y_range[1]-task_y_range[0])/self.ny:.3f}m")
            logger.debug(f"[CONFIG_VALIDATION] 注意: 如果检测到下采样，等效分辨率将自动调整")
        logger.debug(f"[CONFIG_VALIDATION] ==================")
    

    # === 辅助函数 ===

    def _squeeze_spatial(self, tensor, tensor_name="tensor"):
        """
        安全地执行spatial squeeze操作，将[B, 1, H, W]转换为[B, H, W]
        
        Args:
            tensor (torch.Tensor): 输入张量，期望形状为[B, 1, H, W]
            tensor_name (str): 张量名称，用于调试信息
            
        Returns:
            torch.Tensor: 压缩后的张量，形状为[B, H, W]
        """
        if tensor is None:
            logger.warning(f"[SQUEEZE_SPATIAL] {tensor_name} is None")
            return None
            
        original_shape = tensor.shape
        
        # 检查维度是否符合预期
        if len(original_shape) != 4:
            logger.error(f"[SQUEEZE_SPATIAL] {tensor_name} expected 4D tensor, got {len(original_shape)}D: {original_shape}")
            raise ValueError(f"Expected 4D tensor for {tensor_name}, got {original_shape}")
            
        if original_shape[1] != 1:
            logger.error(f"[SQUEEZE_SPATIAL] {tensor_name} expected dim 1 to be 1, got {original_shape[1]}: {original_shape}")
            raise ValueError(f"Expected dim 1 to be 1 for {tensor_name}, got {original_shape}")
        
        # 执行squeeze操作
        squeezed = tensor.squeeze(1)
        
        logger.debug(f"[SQUEEZE_SPATIAL] {tensor_name}: {original_shape} -> {squeezed.shape}")
        
        return squeezed


    def _ensure_batch_dimension(self, tensor, target_batch_size, tensor_name="tensor"):
        """
        确保张量具有正确的批次维度
        
        Args:
            tensor (torch.Tensor): 输入张量
            target_batch_size (int): 目标批次大小
            tensor_name (str): 张量名称，用于调试信息
            
        Returns:
            torch.Tensor: 具有正确批次维度的张量
        """
        if tensor is None:
            logger.warning(f"[ENSURE_BATCH_DIM] {tensor_name} is None")
            return None
            
        original_shape = tensor.shape
        
        # 如果没有批次维度，添加一个
        if len(original_shape) == 3:  # [H, W, C] -> [1, H, W, C]
            tensor = tensor.unsqueeze(0)
            logger.debug(f"[ENSURE_BATCH_DIM] {tensor_name}: Added batch dim {original_shape} -> {tensor.shape}")
        
        current_batch_size = tensor.shape[0]
        
        # 如果批次大小不匹配，扩展到目标大小
        if current_batch_size != target_batch_size:
            if current_batch_size == 1:
                # 扩展单个样本到目标批次大小
                tensor = tensor.expand(target_batch_size, *tensor.shape[1:])
                logger.debug(f"[ENSURE_BATCH_DIM] {tensor_name}: Expanded batch {original_shape} -> {tensor.shape}")
            else:
                logger.error(f"[ENSURE_BATCH_DIM] {tensor_name} batch size mismatch: got {current_batch_size}, expected {target_batch_size}")
                raise ValueError(f"Batch size mismatch for {tensor_name}: got {current_batch_size}, expected {target_batch_size}")
        
        return tensor


    def _validate_tensor_dimensions(self, pred_tensor, gt_tensor, tensor_type="tensor"):
        """
        验证预测张量和真值张量的维度是否匹配
        
        Args:
            pred_tensor (torch.Tensor): 预测张量
            gt_tensor (torch.Tensor): 真值张量
            tensor_type (str): 张量类型，用于错误信息
            
        Returns:
            bool: 维度是否匹配
            
        Raises:
            ValueError: 当维度不匹配时
        """
        if pred_tensor is None or gt_tensor is None:
            logger.error(f"[VALIDATE_DIMS] {tensor_type} contains None tensor")
            raise ValueError(f"None tensor found in {tensor_type} validation")
        
        pred_shape = pred_tensor.shape
        gt_shape = gt_tensor.shape
        
        if pred_shape != gt_shape:
            logger.error(f"[VALIDATE_DIMS] {tensor_type} dimension mismatch!")
            logger.error(f"[VALIDATE_DIMS]   Prediction: {pred_shape}")
            logger.error(f"[VALIDATE_DIMS]   Ground truth: {gt_shape}")
            logger.error(f"[VALIDATE_DIMS]   Difference: H={pred_shape[-2]} vs {gt_shape[-2]}, W={pred_shape[-1]} vs {gt_shape[-1]}")
            
            raise ValueError(f"Dimension mismatch for {tensor_type}: pred {pred_shape} vs gt {gt_shape}")
        
        logger.debug(f"[VALIDATE_DIMS] {tensor_type} dimensions match: {pred_shape}")
        return True


    def _compute_crop_indices(self, input_tensor=None):
        """Enhanced crop indices computation with downsample detection and adaptive adjustment.
        
        BEV tensor convention: [B, C, H, W] where:
        - H (height) corresponds to X-axis (forward direction): [global_x_min, global_x_max]
        - W (width) corresponds to Y-axis (lateral direction): [global_y_min, global_y_max]
        
        Args:
            input_tensor (torch.Tensor, optional): Input BEV tensor for downsample detection
        """
        logger.debug(f"[DEBUG_CROP] === Enhanced Crop Indices Computation ===")
        logger.debug(f"[DEBUG_CROP] Original task area: X=[{self.task_x_min}, {self.task_x_max}], Y=[{self.task_y_min}, {self.task_y_max}]")
        logger.debug(f"[DEBUG_CROP] Global bounds: X=[{self.global_x_min}, {self.global_x_max}], Y=[{self.global_y_min}, {self.global_y_max}]")
        logger.debug(f"[DEBUG_CROP] Global grid size: nx={self.global_nx}, ny={self.global_ny}")
        logger.debug(f"[DEBUG_CROP] Base resolution: x_res={self.x_res}, y_res={self.y_res}")
        
        # Detect downsample factor from input tensor if provided
        downsample_factor = 1
        if input_tensor is not None:
            actual_h, actual_w = input_tensor.shape[-2:]
            expected_h, expected_w = self.global_nx, self.global_ny
            
            # Calculate downsample factors for both dimensions
            downsample_h = expected_h / actual_h
            downsample_w = expected_w / actual_w
            
            logger.debug(f"[DEBUG_CROP] Input tensor shape: {input_tensor.shape}")
            logger.debug(f"[DEBUG_CROP] Expected global grid: [{expected_h}, {expected_w}]")
            logger.debug(f"[DEBUG_CROP] Actual input grid: [{actual_h}, {actual_w}]")
            logger.debug(f"[DEBUG_CROP] Downsample factors: H={downsample_h:.2f}, W={downsample_w:.2f}")
            
            # Use the average downsample factor (should be consistent for both dimensions)
            if abs(downsample_h - downsample_w) < self.TOLERANCE_SMALL:  # Allow small tolerance
                downsample_factor = int(round((downsample_h + downsample_w) / 2))
                logger.debug(f"[DEBUG_CROP] Detected consistent downsample factor: {downsample_factor}")
            else:
                logger.debug(f"[DEBUG_CROP] WARNING: Inconsistent downsample factors, using H factor: {downsample_h}")
                downsample_factor = int(round(downsample_h))
        else:
            logger.debug(f"[DEBUG_CROP] No input tensor provided, assuming no downsampling")
        
        # Store downsample factor for later use
        self.downsample_factor = downsample_factor
        
        # Calculate adjusted resolution based on downsample factor
        adjusted_x_res = self.x_res * downsample_factor
        adjusted_y_res = self.y_res * downsample_factor
        
        logger.debug(f"[DEBUG_CROP] Adjusted resolution: x_res={adjusted_x_res}m, y_res={adjusted_y_res}m")
        
        # Clamp task area to global bounds
        task_x_min = max(self.task_x_min, self.global_x_min)
        task_x_max = min(self.task_x_max, self.global_x_max)
        task_y_min = max(self.task_y_min, self.global_y_min)
        task_y_max = min(self.task_y_max, self.global_y_max)
        
        logger.debug(f"[DEBUG_CROP] Clamped task area: X=[{task_x_min}, {task_x_max}], Y=[{task_y_min}, {task_y_max}]")
        
        # Convert world coordinates to grid indices using adjusted resolution
        # X-axis (forward) maps to tensor height dimension
        self.crop_x_start = int((task_x_min - self.global_x_min) / adjusted_x_res)
        self.crop_x_end = int((task_x_max - self.global_x_min) / adjusted_x_res)
        
        # Y-axis (lateral) maps to tensor width dimension  
        self.crop_y_start = int((task_y_min - self.global_y_min) / adjusted_y_res)
        self.crop_y_end = int((task_y_max - self.global_y_min) / adjusted_y_res)
        
        logger.debug(f"[DEBUG_CROP] Raw crop indices (adjusted): X=[{self.crop_x_start}:{self.crop_x_end}], Y=[{self.crop_y_start}:{self.crop_y_end}]")
        
        # Calculate actual grid size after downsampling
        actual_global_nx = self.global_nx // downsample_factor
        actual_global_ny = self.global_ny // downsample_factor
        
        logger.debug(f"[DEBUG_CROP] Actual global grid after downsampling: [{actual_global_nx}, {actual_global_ny}]")
        
        # Ensure indices are within bounds of downsampled grid
        self.crop_x_start = max(0, min(self.crop_x_start, actual_global_nx))
        self.crop_x_end = max(self.crop_x_start, min(self.crop_x_end, actual_global_nx))
        self.crop_y_start = max(0, min(self.crop_y_start, actual_global_ny))
        self.crop_y_end = max(self.crop_y_start, min(self.crop_y_end, actual_global_ny))
        
        logger.debug(f"[DEBUG_CROP] Final crop indices: X=[{self.crop_x_start}:{self.crop_x_end}], Y=[{self.crop_y_start}:{self.crop_y_end}]")
        logger.debug(f"[DEBUG_CROP] Cropped grid size: H={self.crop_x_end - self.crop_x_start}, W={self.crop_y_end - self.crop_y_start}")
        
        # Update actual task bounds based on grid alignment and adjusted resolution
        self.task_x_min = self.global_x_min + self.crop_x_start * adjusted_x_res
        self.task_x_max = self.global_x_min + self.crop_x_end * adjusted_x_res
        self.task_y_min = self.global_y_min + self.crop_y_start * adjusted_y_res
        self.task_y_max = self.global_y_min + self.crop_y_end * adjusted_y_res
        
        logger.debug(f"[DEBUG_CROP] Updated task bounds: X=[{self.task_x_min}, {self.task_x_max}], Y=[{self.task_y_min}, {self.task_y_max}]")
        
        # Update effective grid dimensions for the task area
        self.nx = self.crop_x_end - self.crop_x_start
        self.ny = self.crop_y_end - self.crop_y_start
        
        logger.debug(f"[DEBUG_CROP] Updated task grid dimensions: nx={self.nx}, ny={self.ny}")
        logger.debug(f"[DEBUG_CROP] Effective resolution: X={(task_x_max-task_x_min)/self.nx:.3f}m, Y={(task_y_max-task_y_min)/self.ny:.3f}m")
        logger.debug(f"[DEBUG_CROP] === Crop Indices Computation Complete ===")
    
    def _crop_bev_features(self, x):
        """Crop BEV features to task area scope with dynamic downsample detection.
        
        Args:
            x (torch.Tensor): Input BEV feature tensor [B, C, H, W]
            
        Returns:
            torch.Tensor: Cropped BEV feature tensor [B, C, H_crop, W_crop]
        """
        logger.debug(f"[DEBUG_CROP] Input feature shape: {x.shape}, dtype: {x.dtype}")
        logger.debug(f"[DEBUG_CROP] Crop enabled: {self.enable_crop}")
        
        if not self.enable_crop:
            logger.debug(f"[DEBUG_CROP] No cropping applied, returning original feature")
            return x
        
        # Dynamic downsample detection and crop indices recomputation
        if not hasattr(self, 'downsample_factor') or not hasattr(self, 'crop_x_start'):
            logger.debug(f"[DEBUG_CROP] First time cropping, detecting downsample factor...")
            self._compute_crop_indices(input_tensor=x)
        else:
            # Verify if the input tensor matches expected dimensions
            expected_h = self.global_nx // getattr(self, 'downsample_factor', 1)
            expected_w = self.global_ny // getattr(self, 'downsample_factor', 1)
            actual_h, actual_w = x.shape[-2:]
            
            if actual_h != expected_h or actual_w != expected_w:
                logger.debug(f"[DEBUG_CROP] Tensor dimension mismatch detected, recomputing crop indices...")
                logger.debug(f"[DEBUG_CROP] Expected: [{expected_h}, {expected_w}], Got: [{actual_h}, {actual_w}]")
                self._compute_crop_indices(input_tensor=x)
        
        logger.debug(f"[DEBUG_CROP] Crop method: {self.crop_method}")
        logger.debug(f"[DEBUG_CROP] Crop indices - X: [{self.crop_x_start}:{self.crop_x_end}], Y: [{self.crop_y_start}:{self.crop_y_end}]")
        logger.debug(f"[DEBUG_CROP] Expected cropped size: H={self.crop_x_end - self.crop_x_start}, W={self.crop_y_end - self.crop_y_start}")
            
        if self.crop_method == 'slice':
            # Direct tensor slicing (most efficient)
            cropped = x[..., self.crop_x_start:self.crop_x_end, self.crop_y_start:self.crop_y_end]
            logger.debug(f"[DEBUG_CROP] After slice cropping: {cropped.shape}")
            return cropped
        
        elif self.crop_method == 'interpolate':
            # Bilinear interpolation to exact task area size
            target_h = int((self.task_x_max - self.task_x_min) / self.x_res)
            target_w = int((self.task_y_max - self.task_y_min) / self.y_res)
            logger.debug(f"[DEBUG_CROP] Interpolate target size: H={target_h}, W={target_w}")
            
            # First crop roughly, then interpolate to exact size
            x_cropped = x[..., self.crop_x_start:self.crop_x_end, self.crop_y_start:self.crop_y_end]
            logger.debug(f"[DEBUG_CROP] After initial crop: {x_cropped.shape}")
            
            interpolated = F.interpolate(x_cropped, size=(target_h, target_w), mode='bilinear', align_corners=False)
            logger.debug(f"[DEBUG_CROP] After interpolation: {interpolated.shape}")
            return interpolated
        
        elif self.crop_method == 'conv':
            # Learnable cropping via 1x1 convolution (for future extension)
            # This could be useful for adaptive task area selection
            if not hasattr(self, 'crop_conv'):
                self.crop_conv = nn.Conv2d(x.shape[1], x.shape[1], kernel_size=1, bias=False)
                nn.init.eye_(self.crop_conv.weight.squeeze())
                logger.debug(f"[DEBUG_CROP] Created crop_conv layer")
            
            x_cropped = x[..., self.crop_x_start:self.crop_x_end, self.crop_y_start:self.crop_y_end]
            logger.debug(f"[DEBUG_CROP] After initial crop: {x_cropped.shape}")
            
            conv_result = self.crop_conv(x_cropped)
            logger.debug(f"[DEBUG_CROP] After conv cropping: {conv_result.shape}")
            return conv_result
        
        else:
            raise ValueError(f"Unknown crop_method: {self.crop_method}")
    
    def _get_crop_info(self):
        """Get cropping information for debugging and visualization.
        
        Returns:
            dict: Cropping information including indices and world coordinates.
        """
        if not self.enable_crop:
            return {'enabled': False}
            
        return {
            'enabled': True,
            'method': self.crop_method,
            'global_bounds': {
                'x': [self.global_x_min, self.global_x_max],
                'y': [self.global_y_min, self.global_y_max],
                'grid_size': [self.global_nx, self.global_ny]
            },
            'task_bounds': {
                'x': [self.task_x_min, self.task_x_max], 
                'y': [self.task_y_min, self.task_y_max],
                'grid_size': [self.nx, self.ny]
            },
            'crop_indices': {
                'x': [self.crop_x_start, self.crop_x_end],
                'y': [self.crop_y_start, self.crop_y_end]
            },
            'compression_ratio': (self.nx * self.ny) / (self.global_nx * self.global_ny)
        }
    
    def _build_layers(self):
        """Build network layers."""
        # Shared feature extraction layers
        self.feat_conv = nn.Sequential(
            ConvModule(
                self.in_channels,
                int(self.in_channels/2),
                kernel_size=3,
                padding=1,
                conv_cfg=dict(type='Conv2d'),
                norm_cfg=dict(type='BN2d'),
                act_cfg=dict(type='ReLU')),
            ConvModule(
                int(self.in_channels/2),
                self.feat_channels,
                kernel_size=3,
                padding=1,
                conv_cfg=dict(type='Conv2d'),
                norm_cfg=dict(type='BN2d'),
                act_cfg=dict(type='ReLU'))
        )
        
        # Task-specific heads
        # Heatmap for lane presence at each location
        self.heatmap_head = nn.Conv2d(
            self.feat_channels, 1, kernel_size=3, padding=1)
        
        # Y-offset prediction (local offset from grid center)
        self.offset_head = nn.Conv2d(
            self.feat_channels, 1, kernel_size=3, padding=1)
        
        # Z-height prediction for 3D coordinates
        self.z_head = nn.Conv2d(
            self.feat_channels, 1, kernel_size=3, padding=1)
        
        # Classification head for lane types
        self.cls_head = nn.Conv2d(
            self.feat_channels, self.num_classes, kernel_size=3, padding=1)
        
        # Instance embedding head for lane grouping (new!)
        if self.use_embedding:
            self.embedding_head = nn.Conv2d(
                self.feat_channels, self.embedding_dim, kernel_size=3, padding=1)
    
    def forward(self, x, depth_features=None):
        """Forward function.
        
        Args:
            x (torch.Tensor): Input BEV feature map of shape (B, C, H, W).
            depth_features (torch.Tensor, optional): Additional depth features.
        
        Returns:
            tuple: A tuple containing output predictions:
                - heatmap: Lane presence heatmap.
                - offset: Y-coordinate offset predictions.
                - z_pred: Z-height predictions.
                - cls_pred: Lane classification logits.
                - embed_pred: Instance embedding predictions (if use_embedding=True).
        """
        # === CRITICAL DEBUG: BEV特征处理前的维度信息 ===
        logger.debug(f"[BEV_FORWARD_DEBUG] === BEV头前向传播开始 ===")
        logger.debug(f"[BEV_FORWARD_DEBUG] 输入特征维度: {x.shape} (dtype: {x.dtype})")
        logger.debug(f"[BEV_FORWARD_DEBUG] 期望输出维度: nx={self.nx}, ny={self.ny}")
        logger.debug(f"[BEV_FORWARD_DEBUG] 是否启用裁剪: {self.enable_crop}")
        
        # === ELEGANT CROPPING: Apply task area scope if enabled ===
        if self.enable_crop:
            logger.debug(f"[BEV_FORWARD_DEBUG] 开始执行BEV特征裁剪...")
            logger.debug(f"[BEV_FORWARD_DEBUG] 裁剪前特征维度: {x.shape}")
            x = self._crop_bev_features(x)
            logger.debug(f"[BEV_FORWARD_DEBUG] 裁剪后特征维度: {x.shape}")
            
            # Debug: Verify cropped tensor shape matches expected task grid
            expected_shape = (x.shape[0], x.shape[1], self.nx, self.ny)
            if x.shape != expected_shape:
                logger.debug(f"[CROP_ERROR] *** 裁剪后特征维度不匹配! ***")
                logger.debug(f"[CROP_ERROR]   实际维度: {x.shape}")
                logger.debug(f"[CROP_ERROR]   期望维度: {expected_shape}")
                logger.debug(f"[CROP_ERROR]   差异: H {x.shape[-2]} vs {self.nx}, W {x.shape[-1]} vs {self.ny}")
        else:
            logger.debug(f"[BEV_FORWARD_DEBUG] 未启用裁剪，直接使用原始特征")
        
        # Shared feature extraction
        logger.debug(f"[BEV_FORWARD_DEBUG] 共享特征提取前: {x.shape}")
        feat = self.feat_conv(x)
        logger.debug(f"[BEV_FORWARD_DEBUG] 共享特征提取后: {feat.shape}")
        
        # Task-specific prediction
        logger.debug(f"[BEV_FORWARD_DEBUG] === 开始任务特定预测 ===")
        heatmap = self.heatmap_head(feat)
        logger.debug(f"[BEV_FORWARD_DEBUG] 热图预测维度: {heatmap.shape} (dtype: {heatmap.dtype})")
        if self.use_sigmoid:
            heatmap = torch.sigmoid(heatmap)
            logger.debug(f"[BEV_FORWARD_DEBUG] Sigmoid后热图维度: {heatmap.shape}")
        
        # Apply tanh to constrain offset predictions to [-0.5, 0.5] grid cells
        offset = 0.5 * torch.tanh(self.offset_head(feat))
        logger.debug(f"[BEV_FORWARD_DEBUG] 偏移预测维度: {offset.shape} (dtype: {offset.dtype})")
        
        z_pred = self.z_head(feat)
        # Scale z prediction to proper range
        z_pred = (self.z_range[1] - self.z_range[0]) * torch.sigmoid(z_pred) + self.z_range[0]
        logger.debug(f"[BEV_FORWARD_DEBUG] Z预测维度: {z_pred.shape} (dtype: {z_pred.dtype})")
        
        cls_pred = self.cls_head(feat)
        logger.debug(f"[BEV_FORWARD_DEBUG] 分类预测维度: {cls_pred.shape} (dtype: {cls_pred.dtype})")
        
        # If using embeddings, predict them
        if self.use_embedding:
            embed_pred = self.embedding_head(feat)
            return heatmap, offset, z_pred, cls_pred, embed_pred
        else:
            return heatmap, offset, z_pred, cls_pred
    
    def _transform_lane_targets(self, lane_targets):
        """Transform lane targets dictionary from data pipeline to expected formats.
        
        Args:
            lane_targets (dict): Lane targets from data pipeline.
            
        Returns:
            dict: Transformed lane targets.
        """
        # logger.debug(f"[LANE_HEAD_DEBUG] lane_targets keys available: {list(lane_targets.keys() if lane_targets else [])}")
        
        if lane_targets is None or not lane_targets:
            # logger.debug(f"[LANE_HEAD_DEBUG] lane_targets is None or empty")
            return None
            
        device = None
        for k, v in lane_targets.items():
            if isinstance(v, torch.Tensor):
                device = v.device
                break
            elif isinstance(v, list) and len(v) > 0 and isinstance(v[0], torch.Tensor):
                device = v[0].device
                break
        
        # 如果没有找到torch.Tensor，使用默认设备（CPU）
        if device is None:
            device = torch.device('cpu')
            logger.debug(f"[LANE_HEAD_DEBUG] No torch.Tensor found in lane_targets, using default device: {device}")

        transformed_targets = {}
        
        # CRITICAL FIX: Handle both old 'segmentation' and new 'gt_heatmap' naming
        # Avoid boolean conversion issues with tensors by checking for None explicitly
        heatmap_source = None
        if 'gt_heatmap' in lane_targets and lane_targets['gt_heatmap'] is not None:
            heatmap_source = lane_targets['gt_heatmap']
            logger.debug(f"[LANE_HEAD_DEBUG] Found gt_heatmap in lane_targets")
        elif 'segmentation' in lane_targets and lane_targets['segmentation'] is not None:
            heatmap_source = lane_targets['segmentation']
            logger.debug(f"[LANE_HEAD_DEBUG] Found segmentation in lane_targets") 
        else:
            logger.debug(f"[LANE_HEAD_DEBUG] No gt_heatmap or segmentation in lane_targets")
            logger.debug(f"[LANE_HEAD_DEBUG] Available keys: {list(lane_targets.keys())}")
            # 不直接返回None，而是继续处理其他字段，热图字段可以为空
            # return None
            
        # 统一的数据提取函数
        def _extract_tensor_from_dc(value):
            """统一处理 DataContainer、numpy.ndarray 和其他数据类型的提取"""
            from mmcv.parallel import DataContainer as DC
            import numpy as np
            
            if isinstance(value, DC):
                actual_data = value.data
                if isinstance(actual_data, (list, tuple)) and len(actual_data) > 0:
                    extracted = actual_data[0]
                else:
                    extracted = actual_data
            elif isinstance(value, (list, tuple)) and len(value) > 0:
                item = value[0]
                if isinstance(item, DC):
                    extracted = item.data
                else:
                    extracted = item
            else:
                extracted = value
            
            # 将numpy.ndarray转换为torch.Tensor
            if isinstance(extracted, np.ndarray):
                extracted = torch.from_numpy(extracted).to(device)
            elif isinstance(extracted, torch.Tensor):
                extracted = extracted.to(device)
            
            return extracted
        
        # 处理热图源数据
        if heatmap_source is not None:
            transformed_targets['gt_heatmap'] = _extract_tensor_from_dc(heatmap_source)
        else:
            logger.debug(f"[LANE_HEAD_DEBUG] No heatmap source available, skipping gt_heatmap")
            
        # === 简化的键映射处理，确保数据流前后字段对应清楚 ===
        # 基于lane_processing.py的输出字段，统一映射到头部期望的字段名
        key_mappings = [
            # 偏移量映射：lane_processing输出gt_offset，头部期望gt_offset
            ('gt_offset', 'gt_offset'),
            # 高度映射：lane_processing输出gt_z，头部期望gt_height  
            ('gt_z', 'gt_height'),
            # 掩码映射：lane_processing输出gt_mask，头部期望gt_mask
            ('gt_mask', 'gt_mask'),
            # 分类映射：lane_processing输出gt_cls，头部期望gt_cls
            ('gt_cls', 'gt_cls'),
            # 实例ID映射：lane_processing输出gt_instance_ids，头部期望gt_instance_ids
            ('gt_instance_ids', 'gt_instance_ids'),
            
            # === 向后兼容的旧命名映射（保留以防配置文件使用旧版本） ===
            ('embedding', 'gt_offset'),      # 兼容旧命名
            ('height_map', 'gt_height'),     # 兼容旧命名
            ('mask', 'gt_mask'),             # 兼容旧命名
        ]
        
        for src_key, dst_key in key_mappings:
            if src_key in lane_targets and lane_targets[src_key] is not None:
                value = lane_targets[src_key]
                
                # 使用统一的数据提取函数
                extracted_value = _extract_tensor_from_dc(value)
                
                # 验证提取的数据
                if isinstance(extracted_value, torch.Tensor) and extracted_value.numel() == 0:
                    continue  # 跳过空张量
                
                transformed_targets[dst_key] = extracted_value
                
                # tensor = transformed_targets[dst_key]
                # logger.debug(f"[LANE_HEAD_DEBUG] {src_key} shape: {tensor.shape}")
                
                # if src_key == 'gt_cls' and tensor.shape[0] > 1:
                #     logger.debug(f"[LANE_HEAD_DEBUG] Class distribution:")
                #     for i in range(tensor.shape[0]):
                #         class_sum = tensor[i].sum().item()
                #         if class_sum > 0:
                #             logger.debug(f"[LANE_HEAD_DEBUG]   - Class {i}: {class_sum} points")
                
                # if src_key == 'mask':
                #     mask_sum = tensor.sum().item()
                #     logger.debug(f"[LANE_HEAD_DEBUG] Mask has {mask_sum} positive points")
                
                # if src_key == 'gt_instance_ids':
                #     unique_ids = torch.unique(tensor)
                #     logger.debug(f"[LANE_HEAD_DEBUG] Instance IDs: {unique_ids.tolist()}")
                #     for id_value in unique_ids:
                #         if id_value == 0: 
                #             continue
                #         points_count = (tensor == id_value).sum().item()
                #         logger.debug(f"[LANE_HEAD_DEBUG]   - Instance {id_value}: {points_count} points")
        
        # if 'gt_heatmap' in transformed_targets and torch.max(transformed_targets['gt_heatmap']) > 0:
        #     heatmap = transformed_targets['gt_heatmap']
            # logger.debug(f"[LANE_HEAD_DEBUG] Valid heatmap found: min={torch.min(heatmap)}, max={torch.max(heatmap)}")
            # logger.debug(f"[LANE_HEAD_DEBUG] Number of values > 0.5: {(heatmap > 0.5).sum().item()}")
            # logger.debug(f"[LANE_HEAD_DEBUG] Number of values > 0.9: {(heatmap > 0.9).sum().item()}")
        # else:
            # logger.debug(f"[LANE_HEAD_DEBUG] No valid values in heatmap") # Keep this important warning
        
        # === 数值验证确保target不为全0 ===
        logger.debug(f"[DEBUG_TRANSFORM] Raw lane_targets keys: {list(lane_targets.keys())}")
        if transformed_targets:
            logger.debug(f"[DEBUG_TRANSFORM] Transformed target shapes and validation:")
            for key, value in transformed_targets.items():
                if isinstance(value, torch.Tensor):
                    # 基本形状信息
                    logger.debug(f"  {key}: {value.shape}, dtype: {value.dtype}, device: {value.device}")
                    
                    # 数值范围验证
                    if value.numel() > 0:
                        min_val = value.min().item()
                        max_val = value.max().item()
                        non_zero_count = (value != 0).sum().item()
                        total_elements = value.numel()
                        
                        logger.debug(f"    Range: [{min_val:.4f}, {max_val:.4f}], Non-zero: {non_zero_count}/{total_elements} ({100*non_zero_count/total_elements:.1f}%)")
                        
                        # 关键字段的全零检查和警告
                        if key == 'gt_heatmap' and non_zero_count == 0:
                            logger.debug(f"    [WARNING] {key} is all zeros! This may cause training issues.")
                        elif key == 'gt_mask' and non_zero_count == 0:
                            logger.debug(f"    [WARNING] {key} is all zeros! No valid lane targets found.")
                        elif key in ['gt_offset', 'gt_height'] and non_zero_count == 0:
                            logger.debug(f"    [INFO] {key} is all zeros (expected if no valid lane points).")
                        
                        # 数值异常检查
                        if torch.isnan(value).any():
                            nan_count = torch.isnan(value).sum().item()
                            logger.debug(f"    [ERROR] {key} contains {nan_count} NaN values!")
                        if torch.isinf(value).any():
                            inf_count = torch.isinf(value).sum().item()
                            logger.debug(f"    [ERROR] {key} contains {inf_count} infinite values!")
                    else:
                        logger.debug(f"    [WARNING] {key} is empty tensor!")
                else:
                    logger.debug(f"  {key}: {type(value)} (not a tensor)")
        else:
            logger.debug(f"[DEBUG_TRANSFORM] No transformed targets generated!")
        
        return transformed_targets
        
    def loss(self, preds, lane_targets, **kwargs):
        """Compute losses following the BEVFusion framework pattern.
        
        Args:
            preds (tuple): Tuple of predictions (heatmap, offset, z_pred, cls_pred, [embed_pred]).
            lane_targets (dict): Ground truth targets from the dataset pipeline.
                                 Expected keys from GenerateBEVLaneHeatmapTargets:
                                 'segmentation', 'embedding', 'height_map', 'mask', 'gt_cls', ['gt_instance_ids'].
            
        Returns:
            dict: Dictionary of losses where all values are torch.Tensor objects.
        """
        # Handle predictions based on whether we're using embeddings
        if self.use_embedding:
            heatmap_pred, offset_pred, z_pred, cls_pred, embed_pred = preds
        else:
            heatmap_pred, offset_pred, z_pred, cls_pred = preds
        
        # =============================================================================
        # CRITICAL AUTOGRAD FIX: Create dummy loss tensor attached to computation graph
        # =============================================================================
        # Instead of using `heatmap_pred.new_tensor(0.0)` which creates a detached leaf node,
        # we compute a "dummy" zero loss by multiplying model predictions by 0.0.
        # This ensures the tensor remains connected to the computation graph with valid grad_fn.
        dummy_loss = (heatmap_pred.sum() + offset_pred.sum() + z_pred.sum() + cls_pred.sum()) * 0.0
        if self.use_embedding:
            dummy_loss += embed_pred.sum() * 0.0
        
        # DEBUG: Verify autograd connectivity
        # logger.debug(f"[AUTOGRAD_DEBUG] dummy_loss requires_grad: {dummy_loss.requires_grad}")
        # logger.debug(f"[AUTOGRAD_DEBUG] dummy_loss grad_fn: {dummy_loss.grad_fn}")
        # logger.debug(f"[AUTOGRAD_DEBUG] dummy_loss device: {dummy_loss.device}")
        # logger.debug(f"[AUTOGRAD_DEBUG] dummy_loss value: {dummy_loss.item()}")
        
        # Initialize loss dictionary with graph-connected dummy tensors
        loss_dict = {
            'loss_heatmap': dummy_loss.clone(),
            'loss_offset': dummy_loss.clone(),
            'loss_z': dummy_loss.clone(),
            'loss_cls': dummy_loss.clone()
        }
        if self.use_embedding:
            loss_dict['loss_embedding'] = dummy_loss.clone()
        
        # =============================================================================
        # EDGE CASE HANDLING: All branches return graph-connected tensors
        # =============================================================================
        
        # Early return with dummy loss if no targets available
        if lane_targets is None:
            logger.debug(f"[LANE_HEAD_LOSS] lane_targets is None, returning graph-connected dummy zero losses")
            # logger.debug(f"[AUTOGRAD_DEBUG] Returning dummy losses - all have grad_fn: {all(v.grad_fn is not None for v in loss_dict.values())}")
            return loss_dict

        # Transform targets
        transformed_targets = self._transform_lane_targets(lane_targets)
        if transformed_targets is None:
            logger.debug(f"[LANE_HEAD_LOSS] transformed_targets is None, returning graph-connected dummy zero losses")
            # logger.debug(f"[AUTOGRAD_DEBUG] Returning dummy losses - all have grad_fn: {all(v.grad_fn is not None for v in loss_dict.values())}")
            return loss_dict

        # Extract ground truth data
        gt_heatmap = transformed_targets['gt_heatmap']
        gt_offset = transformed_targets['gt_offset']
        gt_height = transformed_targets['gt_height']
        gt_mask = transformed_targets['gt_mask']
        gt_cls = transformed_targets['gt_cls']
        
        # =============================================================================
        # DEFENSIVE: Ensure all GT tensors have correct batch dimension
        # =============================================================================
        # Get the actual batch size from predictions
        actual_batch_size = heatmap_pred.shape[0]
        
        def ensure_batch_dim(tensor, name, target_batch_size):
            """Ensure tensor has correct batch dimension matching predictions."""
            if isinstance(tensor, torch.Tensor):
                if name in ['gt_heatmap', 'gt_offset', 'gt_height', 'gt_mask'] and tensor.dim() == 3:
                    # These should be [B, C=1, H, W], but we got [C=1, H, W]
                    logger.debug(f"[AUTOGRAD_DEFENSIVE] Warning: GT tensor '{name}' missing batch dim. Adding and expanding to batch_size={target_batch_size}.")
                    tensor = tensor.unsqueeze(0)  # [C=1, H, W] -> [1, C=1, H, W]
                    # Expand to match actual batch size
                    if target_batch_size > 1:
                        tensor = tensor.expand(target_batch_size, -1, -1, -1)
                    return tensor
                elif name == 'gt_cls' and tensor.dim() == 3:
                    # gt_cls should be [B, C, H, W], but we got [C, H, W]
                    logger.debug(f"[AUTOGRAD_DEFENSIVE] Warning: GT tensor '{name}' missing batch dim. Adding and expanding to batch_size={target_batch_size}.")
                    tensor = tensor.unsqueeze(0)  # [C, H, W] -> [1, C, H, W]
                    # Expand to match actual batch size
                    if target_batch_size > 1:
                        tensor = tensor.expand(target_batch_size, -1, -1, -1)
                    return tensor
                elif name == 'gt_instance_ids' and tensor.dim() == 2:
                    # instance_ids should be [B, H, W], but we got [H, W]
                    logger.debug(f"[AUTOGRAD_DEFENSIVE] Warning: GT tensor '{name}' missing batch dim. Adding and expanding to batch_size={target_batch_size}.")
                    tensor = tensor.unsqueeze(0)  # [H, W] -> [1, H, W]
                    # Expand to match actual batch size
                    if target_batch_size > 1:
                        tensor = tensor.expand(target_batch_size, -1, -1)
                    return tensor
                elif tensor.shape[0] == 1 and target_batch_size > 1:
                    # Tensor already has batch dim but batch_size=1, need to expand
                    logger.debug(f"[AUTOGRAD_DEFENSIVE] Warning: GT tensor '{name}' has batch_size=1, expanding to batch_size={target_batch_size}.")
                    if name in ['gt_heatmap', 'gt_offset', 'gt_height', 'gt_mask', 'gt_cls']:
                        return tensor.expand(target_batch_size, -1, -1, -1)
                    elif name == 'gt_instance_ids':
                        return tensor.expand(target_batch_size, -1, -1)
            return tensor
        
        gt_heatmap = ensure_batch_dim(gt_heatmap, 'gt_heatmap', actual_batch_size)
        gt_offset = ensure_batch_dim(gt_offset, 'gt_offset', actual_batch_size) 
        gt_height = ensure_batch_dim(gt_height, 'gt_height', actual_batch_size)
        gt_mask = ensure_batch_dim(gt_mask, 'gt_mask', actual_batch_size)
        gt_cls = ensure_batch_dim(gt_cls, 'gt_cls', actual_batch_size)
        
        # DEBUG: Print tensor shapes after ensure_batch_dim
        logger.debug(f"[DEBUG_BATCH_DIM] After ensure_batch_dim:")
        logger.debug(f"  Prediction shapes:")
        logger.debug(f"    heatmap_pred: {heatmap_pred.shape}")
        logger.debug(f"    offset_pred: {offset_pred.shape}")
        logger.debug(f"    z_pred: {z_pred.shape}")
        logger.debug(f"    cls_pred: {cls_pred.shape}")
        if self.use_embedding and 'embed_pred' in locals():
            logger.debug(f"    embed_pred: {embed_pred.shape}")
        logger.debug(f"  Ground truth shapes:")
        logger.debug(f"    gt_heatmap: {gt_heatmap.shape}")
        logger.debug(f"    gt_offset: {gt_offset.shape}")
        logger.debug(f"    gt_height: {gt_height.shape}")
        logger.debug(f"    gt_mask: {gt_mask.shape}")
        logger.debug(f"    gt_cls: {gt_cls.shape}")
        
        # Check if we have any valid targets (non-zero mask)
        has_valid_targets = gt_mask.sum() > 0
        
        # DEBUG: Check target shapes and device placement
        # logger.debug(f"[LANE_HEAD_LOSS] Target shapes: heatmap={gt_heatmap.shape}, mask={gt_mask.shape}, offset={gt_offset.shape}")
        # logger.debug(f"[LANE_HEAD_LOSS] Pred shapes: heatmap={heatmap_pred.shape}, offset={offset_pred.shape}, cls={cls_pred.shape}")
        # logger.debug(f"[LANE_HEAD_LOSS] has_valid_targets: {has_valid_targets} (mask_sum={gt_mask.sum().item()})")
        
        # =============================================================================
        # VALID TARGET PROCESSING: Compute actual losses
        # =============================================================================
        
        if has_valid_targets:
            # logger.debug(f"[LANE_HEAD_LOSS] Computing actual losses for valid targets")
            
            # === CRITICAL DEBUG: 添加详细的维度调试信息 ===
            logger.debug(f"[DIMENSION_DEBUG] === 开始处理损失计算前的维度变换 ===")
            logger.debug(f"[DIMENSION_DEBUG] 原始预测张量维度:")
            logger.debug(f"[DIMENSION_DEBUG]   heatmap_pred: {heatmap_pred.shape} (dtype: {heatmap_pred.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   offset_pred: {offset_pred.shape} (dtype: {offset_pred.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   z_pred: {z_pred.shape} (dtype: {z_pred.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   cls_pred: {cls_pred.shape} (dtype: {cls_pred.dtype})")
            
            logger.debug(f"[DIMENSION_DEBUG] 原始真值张量维度:")
            logger.debug(f"[DIMENSION_DEBUG]   gt_heatmap: {gt_heatmap.shape} (dtype: {gt_heatmap.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   gt_offset: {gt_offset.shape} (dtype: {gt_offset.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   gt_height: {gt_height.shape} (dtype: {gt_height.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   gt_mask: {gt_mask.shape} (dtype: {gt_mask.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   gt_cls: {gt_cls.shape} (dtype: {gt_cls.dtype})")
            
            # === 张量压缩操作调试 ===
            logger.debug(f"[SQUEEZE_DEBUG] 开始张量压缩操作...")
            
            # Squeeze predictions to match ground truth dimensions
            heatmap_pred_squeezed = self._squeeze_spatial(heatmap_pred, "heatmap_pred")  # [B, 1, H, W] -> [B, H, W]
            offset_pred_squeezed = self._squeeze_spatial(offset_pred, "offset_pred")    # [B, 1, H, W] -> [B, H, W]
            z_pred_squeezed = self._squeeze_spatial(z_pred, "z_pred")              # [B, 1, H, W] -> [B, H, W]
            
            logger.debug(f"[SQUEEZE_DEBUG] 预测张量压缩后维度:")
            logger.debug(f"[SQUEEZE_DEBUG]   heatmap_pred: {heatmap_pred.shape} -> {heatmap_pred_squeezed.shape}")
            logger.debug(f"[SQUEEZE_DEBUG]   offset_pred: {offset_pred.shape} -> {offset_pred_squeezed.shape}")
            logger.debug(f"[SQUEEZE_DEBUG]   z_pred: {z_pred.shape} -> {z_pred_squeezed.shape}")
            
            # Squeeze ground truth to match prediction dimensions
            gt_heatmap_squeezed = self._squeeze_spatial(gt_heatmap, "gt_heatmap")      # [B, 1, H, W] -> [B, H, W]
            gt_offset_squeezed = self._squeeze_spatial(gt_offset, "gt_offset")        # [B, 1, H, W] -> [B, H, W]
            gt_height_squeezed = self._squeeze_spatial(gt_height, "gt_height")        # [B, 1, H, W] -> [B, H, W]
            gt_mask_squeezed = self._squeeze_spatial(gt_mask, "gt_mask")            # [B, 1, H, W] -> [B, H, W]
            
            logger.debug(f"[SQUEEZE_DEBUG] 真值张量压缩后维度:")
            logger.debug(f"[SQUEEZE_DEBUG]   gt_heatmap: {gt_heatmap.shape} -> {gt_heatmap_squeezed.shape}")
            logger.debug(f"[SQUEEZE_DEBUG]   gt_offset: {gt_offset.shape} -> {gt_offset_squeezed.shape}")
            logger.debug(f"[SQUEEZE_DEBUG]   gt_height: {gt_height.shape} -> {gt_height_squeezed.shape}")
            logger.debug(f"[SQUEEZE_DEBUG]   gt_mask: {gt_mask.shape} -> {gt_mask_squeezed.shape}")
            
            logger.debug(f"[DIMENSION_DEBUG] 压缩后的张量维度:")
            logger.debug(f"[DIMENSION_DEBUG]   heatmap_pred_squeezed: {heatmap_pred_squeezed.shape} (dtype: {heatmap_pred_squeezed.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   gt_heatmap_squeezed: {gt_heatmap_squeezed.shape} (dtype: {gt_heatmap_squeezed.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   offset_pred_squeezed: {offset_pred_squeezed.shape} (dtype: {offset_pred_squeezed.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   gt_offset_squeezed: {gt_offset_squeezed.shape} (dtype: {gt_offset_squeezed.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   z_pred_squeezed: {z_pred_squeezed.shape} (dtype: {z_pred_squeezed.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   gt_height_squeezed: {gt_height_squeezed.shape} (dtype: {gt_height_squeezed.dtype})")
            logger.debug(f"[DIMENSION_DEBUG]   gt_mask_squeezed: {gt_mask_squeezed.shape} (dtype: {gt_mask_squeezed.dtype})")
            
            # === 关键维度不匹配检查 ===
            self._validate_tensor_dimensions(heatmap_pred_squeezed, gt_heatmap_squeezed, "heatmap")
                logger.error(f"[DIMENSION_ERROR] *** 热图维度不匹配! ***")
                logger.error(f"[DIMENSION_ERROR]   预测热图: {heatmap_pred_squeezed.shape}")
                logger.error(f"[DIMENSION_ERROR]   真值热图: {gt_heatmap_squeezed.shape}")
                logger.error(f"[DIMENSION_ERROR]   差异: 预测H={heatmap_pred_squeezed.shape[-2]}, 真值H={gt_heatmap_squeezed.shape[-2]}")
                logger.error(f"[DIMENSION_ERROR]   差异: 预测W={heatmap_pred_squeezed.shape[-1]}, 真值W={gt_heatmap_squeezed.shape[-1]}")
            
            # === 损失计算调试 ===
            logger.debug(f"[LOSS_COMPUTE_DEBUG] 开始计算各项损失...")
            
            # 热图损失计算
            logger.debug(f"[LOSS_COMPUTE_DEBUG] 计算热图损失:")
            logger.debug(f"[LOSS_COMPUTE_DEBUG]   输入维度: pred={heatmap_pred_squeezed.shape}, gt={gt_heatmap_squeezed.shape}")
            logger.debug(f"[LOSS_COMPUTE_DEBUG]   输入数据类型: pred={heatmap_pred_squeezed.dtype}, gt={gt_heatmap_squeezed.dtype}")
            logger.debug(f"[LOSS_COMPUTE_DEBUG]   输入数据范围: pred=[{heatmap_pred_squeezed.min().item():.4f}, {heatmap_pred_squeezed.max().item():.4f}], gt=[{gt_heatmap_squeezed.min().item():.4f}, {gt_heatmap_squeezed.max().item():.4f}]")
            loss_heatmap = self.loss_heatmap(heatmap_pred_squeezed, gt_heatmap_squeezed)
            logger.debug(f"[LOSS_COMPUTE_DEBUG]   热图损失值: {loss_heatmap.item():.6f}, requires_grad: {loss_heatmap.requires_grad}")
            
            # 偏移损失计算
            logger.debug(f"[LOSS_COMPUTE_DEBUG] 计算偏移损失:")
            logger.debug(f"[LOSS_COMPUTE_DEBUG]   输入维度: pred={offset_pred_squeezed.shape}, gt={gt_offset_squeezed.shape}, weight={gt_mask_squeezed.shape}")
            logger.debug(f"[LOSS_COMPUTE_DEBUG]   权重统计: 有效像素数={gt_mask_squeezed.sum().item()}, 总像素数={gt_mask_squeezed.numel()}")
            loss_offset = self.loss_offset(offset_pred_squeezed, gt_offset_squeezed, weight=gt_mask_squeezed)
            logger.debug(f"[LOSS_COMPUTE_DEBUG]   偏移损失值: {loss_offset.item():.6f}, requires_grad: {loss_offset.requires_grad}")
            
            # 高度损失计算
            logger.debug(f"[LOSS_COMPUTE_DEBUG] 计算高度损失:")
            logger.debug(f"[LOSS_COMPUTE_DEBUG]   输入维度: pred={z_pred_squeezed.shape}, gt={gt_height_squeezed.shape}, weight={gt_mask_squeezed.shape}")
            logger.debug(f"[LOSS_COMPUTE_DEBUG]   高度数据范围: pred=[{z_pred_squeezed.min().item():.4f}, {z_pred_squeezed.max().item():.4f}], gt=[{gt_height_squeezed.min().item():.4f}, {gt_height_squeezed.max().item():.4f}]")
            loss_z = self.loss_z(z_pred_squeezed, gt_height_squeezed, weight=gt_mask_squeezed)
            logger.debug(f"[LOSS_COMPUTE_DEBUG]   高度损失值: {loss_z.item():.6f}, requires_grad: {loss_z.requires_grad}")
            
            # DEBUG: Verify computed losses have gradients
            # logger.debug(f"[AUTOGRAD_DEBUG] loss_heatmap requires_grad: {loss_heatmap.requires_grad}, grad_fn: {loss_heatmap.grad_fn}")
            # logger.debug(f"[AUTOGRAD_DEBUG] loss_offset requires_grad: {loss_offset.requires_grad}, grad_fn: {loss_offset.grad_fn}")
            # logger.debug(f"[AUTOGRAD_DEBUG] loss_z requires_grad: {loss_z.requires_grad}, grad_fn: {loss_z.grad_fn}")
            
            # === 分类损失计算调试 ===
            logger.debug(f"[CLS_LOSS_DEBUG] 开始分类损失计算...")
            B, num_classes_pred, H, W = cls_pred.shape
            logger.debug(f"[CLS_LOSS_DEBUG] 分类预测张量维度解析: B={B}, C={num_classes_pred}, H={H}, W={W}")
            
            valid_mask = gt_mask_squeezed > self.THRESHOLD_HALF  # Shape: [B, H, W]
            logger.debug(f"[CLS_LOSS_DEBUG] 有效掩码统计:")
            logger.debug(f"[CLS_LOSS_DEBUG]   valid_mask shape: {valid_mask.shape}")
            logger.debug(f"[CLS_LOSS_DEBUG]   有效像素数: {valid_mask.sum().item()}/{valid_mask.numel()}")
            logger.debug(f"[CLS_LOSS_DEBUG]   有效像素比例: {valid_mask.sum().item()/valid_mask.numel()*100:.2f}%")
            
            logger.debug(f"[CLS_LOSS_DEBUG] 分类真值张量分析:")
            logger.debug(f"[CLS_LOSS_DEBUG]   gt_cls shape: {gt_cls.shape}")
            gt_cls_nonzero = (gt_cls > 0).sum().item()
            logger.debug(f"[CLS_LOSS_DEBUG]   gt_cls非零元素数: {gt_cls_nonzero}")
            
            # 统计每个类别的GT像素数
            logger.debug(f"[CLS_LOSS_DEBUG] 各类别像素统计:")
            for c in range(gt_cls.shape[1]):
                class_pixels = (gt_cls[:, c, :, :] > 0).sum().item()
                if class_pixels > 0:
                    logger.debug(f"[CLS_LOSS_DEBUG]   类别 {c}: {class_pixels} 像素")
            
            # Only compute classification loss if there are valid pixels
            logger.debug(f"[CLS_LOSS_DEBUG] 检查是否有有效像素进行分类损失计算...")
            if valid_mask.sum() > 0:
                logger.debug(f"[CLS_LOSS_DEBUG] 发现 {valid_mask.sum().item()} 个有效像素，开始计算分类损失")
                # === 分类张量重塑和过滤调试 ===
                logger.debug(f"[CLS_LOSS_DEBUG] 张量重塑操作:")
                
                # Reshape for filtering
                cls_pred_permuted = cls_pred.permute(0, 2, 3, 1).contiguous() # Shape: [B, H, W, C]
                gt_cls_permuted = gt_cls.permute(0, 2, 3, 1).contiguous()     # Shape: [B, H, W, C]
                logger.debug(f"[CLS_LOSS_DEBUG]   cls_pred: {cls_pred.shape} -> {cls_pred_permuted.shape}")
                logger.debug(f"[CLS_LOSS_DEBUG]   gt_cls: {gt_cls.shape} -> {gt_cls_permuted.shape}")
                
                # Filter to only valid pixels
                logger.debug(f"[CLS_LOSS_DEBUG] 有效像素过滤:")
                valid_cls_pred = cls_pred_permuted[valid_mask] # Shape: [num_valid_pixels, C]
                valid_gt_cls = gt_cls_permuted[valid_mask]     # Shape: [num_valid_pixels, C]
                logger.debug(f"[CLS_LOSS_DEBUG]   过滤后预测张量: {valid_cls_pred.shape}")
                logger.debug(f"[CLS_LOSS_DEBUG]   过滤后真值张量: {valid_gt_cls.shape}")
                logger.debug(f"[CLS_LOSS_DEBUG]   预测值范围: [{valid_cls_pred.min().item():.4f}, {valid_cls_pred.max().item():.4f}]")
                logger.debug(f"[CLS_LOSS_DEBUG]   真值范围: [{valid_gt_cls.min().item():.4f}, {valid_gt_cls.max().item():.4f}]")
                
                # Convert one-hot GT to class indices
                logger.debug(f"[CLS_LOSS_DEBUG] One-hot转类别索引:")
                valid_gt_indices = torch.argmax(valid_gt_cls, dim=1)
                logger.debug(f"[CLS_LOSS_DEBUG]   类别索引张量: {valid_gt_indices.shape}")
                logger.debug(f"[CLS_LOSS_DEBUG]   唯一类别索引: {torch.unique(valid_gt_indices).tolist()}")
                logger.debug(f"[CLS_LOSS_DEBUG]   各类别像素数统计:")
                for idx in torch.unique(valid_gt_indices):
                    count = (valid_gt_indices == idx).sum().item()
                    logger.debug(f"[CLS_LOSS_DEBUG]     类别 {idx.item()}: {count} 像素")
                
                # Calculate classification loss
                logger.debug(f"[CLS_LOSS_DEBUG] 计算分类损失...")
                loss_cls = self.loss_cls(valid_cls_pred, valid_gt_indices)
                logger.debug(f"[CLS_LOSS_DEBUG]   分类损失值: {loss_cls.item():.6f}, requires_grad: {loss_cls.requires_grad}")
                # logger.debug(f"[CLS_LOSS_DEBUG] loss_cls value: {loss_cls.item():.6f}")
                
                # logger.debug(f"[CLS_LOSS_DEBUG] After permute:")
                # logger.debug(f"[CLS_LOSS_DEBUG]   cls_pred_permuted: {cls_pred_permuted.shape}")
                # logger.debug(f"[CLS_LOSS_DEBUG]   gt_cls_permuted: {gt_cls_permuted.shape}")
                # 
                # logger.debug(f"[CLS_LOSS_DEBUG] After filtering:")
                # logger.debug(f"[CLS_LOSS_DEBUG]   valid_cls_pred: {valid_cls_pred.shape}")
                # logger.debug(f"[CLS_LOSS_DEBUG]   valid_gt_cls: {valid_gt_cls.shape}")
                # 
                # logger.debug(f"[CLS_LOSS_DEBUG] valid_gt_indices shape: {valid_gt_indices.shape}")
                # logger.debug(f"[CLS_LOSS_DEBUG] valid_gt_indices unique values: {torch.unique(valid_gt_indices).tolist()}")
                # logger.debug(f"[CLS_LOSS_DEBUG] loss_cls requires_grad: {loss_cls.requires_grad}, grad_fn: {loss_cls.grad_fn}")
            else:
                # No valid pixels for classification - keep dummy loss
                logger.debug(f"[CLS_LOSS_DEBUG] 无有效像素进行分类损失计算，使用虚拟损失")
                logger.debug(f"[CLS_LOSS_DEBUG]   虚拟损失值: {dummy_loss.item():.6f}")
                loss_cls = dummy_loss.clone()
            
            # === 嵌入损失计算调试 ===
            logger.debug(f"[EMBED_LOSS_DEBUG] 检查嵌入损失配置...")
            logger.debug(f"[EMBED_LOSS_DEBUG]   use_embedding: {self.use_embedding}")
            logger.debug(f"[EMBED_LOSS_DEBUG]   gt_instance_ids存在: {'gt_instance_ids' in transformed_targets}")
            
            if self.use_embedding and 'gt_instance_ids' in transformed_targets:
                logger.debug(f"[EMBED_LOSS_DEBUG] 开始嵌入损失计算...")
                gt_instance_ids = transformed_targets['gt_instance_ids']
                gt_instance_ids = ensure_batch_dim(gt_instance_ids, 'gt_instance_ids', actual_batch_size)
                logger.debug(f"[EMBED_LOSS_DEBUG]   gt_instance_ids shape: {gt_instance_ids.shape}")
                logger.debug(f"[EMBED_LOSS_DEBUG]   gt_instance_ids dtype: {gt_instance_ids.dtype}")
                
                unique_gt_instances = torch.unique(gt_instance_ids)
                logger.debug(f"[EMBED_LOSS_DEBUG]   唯一实例ID数量: {len(unique_gt_instances)}")
                logger.debug(f"[EMBED_LOSS_DEBUG]   唯一实例ID: {unique_gt_instances.tolist()[:10]}...")  # 只显示前10个
                
                # The mask from the pipeline is (B, 1, H, W). Use already squeezed mask.
                embedding_valid_mask = gt_mask_squeezed
                logger.debug(f"[EMBED_LOSS_DEBUG]   embedding_valid_mask shape: {embedding_valid_mask.shape}")
                logger.debug(f"[EMBED_LOSS_DEBUG]   有效嵌入像素数: {embedding_valid_mask.sum().item()}")
                
                # Additional safety check: ensure mask is on correct device
                logger.debug(f"[EMBED_LOSS_DEBUG] 设备检查:")
                logger.debug(f"[EMBED_LOSS_DEBUG]   embed_pred device: {embed_pred.device}")
                logger.debug(f"[EMBED_LOSS_DEBUG]   embedding_valid_mask device: {embedding_valid_mask.device}")
                logger.debug(f"[EMBED_LOSS_DEBUG]   gt_instance_ids device: {gt_instance_ids.device}")
                
                if embedding_valid_mask.device != embed_pred.device:
                    logger.debug(f"[EMBED_LOSS_DEBUG]   移动embedding_valid_mask到 {embed_pred.device}")
                    embedding_valid_mask = embedding_valid_mask.to(embed_pred.device)
                
                if gt_instance_ids.device != embed_pred.device:
                    logger.debug(f"[EMBED_LOSS_DEBUG]   移动gt_instance_ids到 {embed_pred.device}")
                    gt_instance_ids = gt_instance_ids.to(embed_pred.device)
                
                # Only compute embedding loss if there are valid targets
                if embedding_valid_mask.sum() > 0:
                    logger.debug(f"[EMBED_LOSS_DEBUG] 计算嵌入损失 (有效像素: {embedding_valid_mask.sum().item()})...")
                    embedding_losses = self.loss_embedding(
                        embed_pred, gt_instance_ids, valid_mask=embedding_valid_mask
                    )
                    
                    if isinstance(embedding_losses, dict):
                        logger.debug(f"[EMBED_LOSS_DEBUG]   嵌入损失返回字典: {list(embedding_losses.keys())}")
                        # Extract the main loss (this will be summed in the total loss)
                        loss_embedding = embedding_losses['loss']
                    else:
                        loss_embedding = embedding_losses
                    logger.debug(f"[EMBED_LOSS_DEBUG]   嵌入损失值: {loss_embedding.item():.6f}, requires_grad: {loss_embedding.requires_grad}")
                else:
                    logger.debug(f"[EMBED_LOSS_DEBUG] 无有效目标进行嵌入损失计算，使用虚拟损失")
                    loss_embedding = dummy_loss.clone()
            else:
                # logger.debug(f"[LANE_HEAD_LOSS] No embedding configuration or gt_instance_ids, using dummy loss")
                loss_embedding = dummy_loss.clone()
                
                # Joint optimization: Class-aware instance IDs
                if self.use_embedding and 'gt_instance_ids' in transformed_targets:
                    gt_instance_ids = transformed_targets['gt_instance_ids']
                    gt_instance_ids = ensure_batch_dim(gt_instance_ids, 'gt_instance_ids', actual_batch_size)
                    
                    # Convert gt_cls to indices
                    gt_cls_indices = torch.argmax(gt_cls, dim=1)  # [B, H, W]
                    
                    # Class-aware instance IDs
                    class_aware_instance_ids = gt_instance_ids * 100 + gt_cls_indices
                    
                    # Compute embedding loss with class-aware IDs
                    if embedding_valid_mask.sum() > 0:
                        embedding_losses = self.loss_embedding(
                            embed_pred, class_aware_instance_ids, valid_mask=embedding_valid_mask
                        )
                        if isinstance(embedding_losses, dict):
                            loss_embedding = embedding_losses['loss']
                        else:
                            loss_embedding = embedding_losses
                    
                    # Consistency loss
                    # Sample pairs (simplified; in practice, use random sampling or all pairs for small batches)
                    # For demonstration, assume we have a function to compute pairwise L1 and exp-dist
                    # Note: Implement actual pairwise computation carefully to avoid OOM
                    from torch.nn.functional import l1_loss, pairwise_distance
                    valid_embed = embed_pred[embedding_valid_mask.unsqueeze(1).expand_as(embed_pred)].view(-1, embed_pred.shape[1])
                    valid_cls = cls_pred[embedding_valid_mask.unsqueeze(1).expand_as(cls_pred)].view(-1, cls_pred.shape[1])
                    if valid_embed.size(0) > 1:
                        dist = pairwise_distance(valid_embed, valid_embed)
                        cls_diff = l1_loss(valid_cls.unsqueeze(0), valid_cls.unsqueeze(1), reduction='none').mean(dim=-1)
                        loss_consistency = (cls_diff * torch.exp(-dist)).mean()
                        loss_consistency = loss_consistency * self.cls_consistency_weight  # FIXED: Use self.cls_consistency_weight
                    else:
                        loss_consistency = dummy_loss.clone()
                    
                    loss_dict['loss_cls_consistency'] = loss_consistency
                
            # Update loss dictionary with computed values
            loss_dict.update({
                'loss_heatmap': loss_heatmap,
                'loss_offset': loss_offset,
                'loss_z': loss_z,
                'loss_cls': loss_cls
            })
            
            if self.use_embedding:
                loss_dict['loss_embedding'] = loss_embedding
                
        # 保留关键的损失信息
        total_loss_sum = 0.0
        for key, value in loss_dict.items():
            loss_value = value.item()
            total_loss_sum += loss_value
            # logger.debug(f"[LOSS_DEBUG {key}: {loss_value:.6f}")
        
        # logger.debug(f"[LOSS_DEBUG] Total loss sum: {total_loss_sum:.6f}")
        
        return loss_dict
    
    def _nms(self, heat):
        """Non-maximum suppression for heatmap.
        
        Args:
            heat (torch.Tensor): Input heatmap.
            
        Returns:
            torch.Tensor: Heatmap after NMS.
        """
        # Get kernel for max pooling
        kernel = self.nms_kernel_size
        pad = (kernel - 1) // 2
        
        # Apply max pooling to get local maximum
        hmax = F.max_pool2d(
            heat, kernel_size=kernel, stride=1, padding=pad)
        # Keep only the exact local maximum
        keep = (hmax == heat).float()
        
        return heat * keep
    
    def _topk(self, heatmap, k=20):
        """Extract top-k points from heatmap.
        
        Args:
            heatmap (torch.Tensor): Input heatmap.
            k (int): Number of top points to extract.
            
        Returns:
            tuple: A tuple containing topk scores, indices, classes, ys, xs.
        """
        batch, cat, height, width = heatmap.size()
        
        # Reshape and find top k scores
        heatmap = heatmap.view(batch, -1)
        topk_scores, topk_inds = torch.topk(heatmap, k)
        
        # Get coordinates from indices
        topk_ys = (topk_inds // width).float()
        topk_xs = (topk_inds % width).float()
        
        # Note: topk_classes is placeholder to maintain API compatibility
        # It's not actually used in our implementation
        topk_classes = torch.zeros_like(topk_ys)
        
        return topk_scores, topk_inds, topk_classes, topk_ys, topk_xs

    
    def _group_lane_points(self, points, class_ids, scores, max_dist=1.0):
        """Group lane points into continuous lane instances using geometric approach.
        
        Args:
            points (torch.Tensor): 3D points (N, 3).
            class_ids (torch.Tensor): Class IDs for each point (N,).
            scores (torch.Tensor): Detection scores for each point (N,).
            max_dist (float): Maximum distance to consider points part of the same lane.
            
        Returns:
            list: List of grouped lane instances.
        """
        if len(points) == 0:
            return []
            
        # Sort points by y-coordinate
        sorted_indices = torch.argsort(points[:, 1])
        sorted_points = points[sorted_indices]
        sorted_class_ids = class_ids[sorted_indices]
        sorted_scores = scores[sorted_indices]
        
        # Group by both distance and angle consistency
        lanes = []
        current_lane_indices = [0]
        current_lane_class = sorted_class_ids[0].item()
        current_direction = None
        
        for i in range(1, len(sorted_points)):
            curr_point = sorted_points[i]
            curr_class = sorted_class_ids[i].item()
            
            # Get last point in current lane
            last_idx = current_lane_indices[-1]
            last_point = sorted_points[last_idx]
            
            # Calculate spatial distance
            dist = torch.norm(curr_point[:2] - last_point[:2])
            
            # Calculate direction if we have enough points
            if len(current_lane_indices) >= 2:
                prev_point = sorted_points[current_lane_indices[-2]]
                new_direction = (last_point[:2] - prev_point[:2])
                new_direction = new_direction / (torch.norm(new_direction) + 1e-6)
                
                if current_direction is None:
                    current_direction = new_direction
                
                # Check direction consistency
                direction_sim = torch.dot(current_direction, new_direction)
                direction_consistent = direction_sim > 0.707  # cos(45°)
            else:
                direction_consistent = True
            
            # Determine if point belongs to current lane
            if (dist < max_dist and curr_class == current_lane_class and direction_consistent):
                current_lane_indices.append(i)
                
                # Update direction as moving average
                if len(current_lane_indices) >= 2 and current_direction is not None:
                    prev_point = sorted_points[current_lane_indices[-2]]
                    new_direction = (curr_point[:2] - prev_point[:2])
                    new_direction = new_direction / (torch.norm(new_direction) + 1e-6)
                    current_direction = 0.7 * current_direction + 0.3 * new_direction
                    current_direction = current_direction / (torch.norm(current_direction) + 1e-6)
            else:
                # Create new lane if current has enough points
                if len(current_lane_indices) >= 3:
                    lane_points = sorted_points[current_lane_indices]
                    lane_scores = sorted_scores[current_lane_indices]
                    
                    lane = {
                        'points_3d': lane_points,
                        'class_id': current_lane_class,
                        'score': lane_scores.mean().item()
                    }
                    lanes.append(lane)
                
                # Start new lane
                current_lane_indices = [i]
                current_lane_class = curr_class
                current_direction = None
        
        # Add the last lane
        if len(current_lane_indices) >= 3:
            lane_points = sorted_points[current_lane_indices]
            lane_scores = sorted_scores[current_lane_indices]
            
            lane = {
                'points_3d': lane_points,
                'class_id': current_lane_class,
                'score': lane_scores.mean().item()
            }
            lanes.append(lane)
        
        return lanes
    
    def _debug_visualize_clustering(self, points, embeddings, class_ids, scores, cluster_labels, cls_id, save_dir="debug_clustering"):
        """Debug visualization to show clustering results."""
        if not hasattr(self, '_debug_counter'):
            self._debug_counter = 0
        self._debug_counter += 1
        
        # Create debug directory
        os.makedirs(save_dir, exist_ok=True)
        
        # Convert to numpy for plotting
        points_np = points.detach().cpu().numpy()
        embeddings_np = embeddings.detach().cpu().numpy()
        
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # Plot 1: Spatial distribution colored by cluster
        unique_clusters = np.unique(cluster_labels)
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_clusters)))
        
        for i, cluster_id in enumerate(unique_clusters):
            mask = cluster_labels == cluster_id
            if cluster_id == -1:  # Noise points
                ax1.scatter(points_np[mask, 0], points_np[mask, 1], 
                           c='black', marker='x', s=50, alpha=0.7, label='Noise')
            else:
                ax1.scatter(points_np[mask, 0], points_np[mask, 1], 
                           c=[colors[i]], s=60, alpha=0.8, label=f'Cluster {cluster_id}')
        
        ax1.set_xlabel('X (meters)')
        ax1.set_ylabel('Y (meters)')
        ax1.set_title(f'Spatial Clustering Results (Class {cls_id})\neps={self.clustering_epsilon}, min_samples={self.clustering_min_points}')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Embedding space (first 2 dimensions)
        if embeddings_np.shape[1] >= 2:
            for i, cluster_id in enumerate(unique_clusters):
                mask = cluster_labels == cluster_id
                if cluster_id == -1:  # Noise points
                    ax2.scatter(embeddings_np[mask, 0], embeddings_np[mask, 1], 
                               c='black', marker='x', s=50, alpha=0.7, label='Noise')
                else:
                    ax2.scatter(embeddings_np[mask, 0], embeddings_np[mask, 1], 
                               c=[colors[i]], s=60, alpha=0.8, label=f'Cluster {cluster_id}')
            
            ax2.set_xlabel('Embedding Dim 0')
            ax2.set_ylabel('Embedding Dim 1')
            ax2.set_title(f'Embedding Space Clustering (Class {cls_id})')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        # Add statistics text
        stats_text = f"Total points: {len(points_np)}\n"
        stats_text += f"Clusters found: {len(unique_clusters) - (1 if -1 in unique_clusters else 0)}\n"
        stats_text += f"Noise points: {np.sum(cluster_labels == -1)}\n"
        stats_text += f"Embedding variance: {np.var(embeddings_np, axis=0).mean():.4f}"
        
        fig.text(0.02, 0.98, stats_text, fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        save_path = os.path.join(save_dir, f'clustering_debug_{self._debug_counter:04d}_class_{cls_id}.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        logger.debug(f"[CLUSTERING_DEBUG] Saved visualization: {save_path}")
        logger.debug(f"[CLUSTERING_DEBUG] Class {cls_id}: {len(unique_clusters) - (1 if -1 in unique_clusters else 0)} clusters, {np.sum(cluster_labels == -1)} noise points")
    
    def _group_lane_points_embedding(self, points, embeddings, class_ids, scores):
        """Improved embedding-based clustering with debug visualization"""
        if len(points) == 0:
            return []
        
        # Normalize embeddings for better clustering performance
        embeddings_norm = F.normalize(embeddings, p=2, dim=1)
        
        # Convert to numpy for DBSCAN
        embeddings_np = embeddings_norm.detach().cpu().numpy()
        
        # Group by class first, then cluster embeddings
        lanes = []
        unique_classes = torch.unique(class_ids)
        
        for cls_id in unique_classes:
            # Extract points for this class
            cls_mask = class_ids == cls_id
            if not cls_mask.sum() > self.clustering_min_points:
                continue
            
            cls_points = points[cls_mask]
            cls_embeddings = embeddings_np[cls_mask.cpu().numpy()]
            cls_scores = scores[cls_mask]
            
            # Apply DBSCAN clustering
            clustering = DBSCAN(
                eps=self.clustering_epsilon,
                min_samples=self.clustering_min_points,
                metric='euclidean'
            ).fit(cls_embeddings)
            
            # DEBUG: Visualize clustering results
            cluster_labels = clustering.labels_
            if hasattr(self, 'debug_clustering') and self.debug_clustering:
                self._debug_visualize_clustering(
                    cls_points, embeddings_norm[cls_mask], class_ids[cls_mask], 
                    cls_scores, cluster_labels, cls_id.item()
                )
            
            # Process clusters
            for cluster_id in np.unique(cluster_labels):
                if cluster_id == -1:  # Skip noise
                    continue
                
                # Get points for this cluster
                cluster_mask = cluster_labels == cluster_id
                lane_points = cls_points[cluster_mask]
                lane_scores = cls_scores[cluster_mask]
                
                if len(lane_points) < 3:  # Skip too small clusters
                    continue
                
                # Sort by y coordinate
                sort_idx = torch.argsort(lane_points[:, 1])
                lane_points = lane_points[sort_idx]
                
                lanes.append({
                    'points_3d': lane_points,
                    'class_id': cls_id.item(),
                    'score': lane_scores.mean().item()
                })
        
        return lanes
    
    def get_lanes(self, preds, img_metas, rescale=False):
        """Post-process predictions to get lane instances.
        
        Args:
            preds (tuple): Prediction tuple (heatmap, offset, z, cls, [embed]).
            img_metas (list): List of image meta information.
            rescale (bool): Whether to rescale output.
            
        Returns:
            list: List of detected lanes with 3D coordinates and class labels.
        """
        # Handle predictions based on whether we're using embeddings
        if self.use_embedding:
            heatmap, offset, z_pred, cls_pred, embed_pred = preds
        else:
            heatmap, offset, z_pred, cls_pred = preds
            
        batch_size = heatmap.size(0)
        
        # Apply NMS to heatmap
        heatmap = self._nms(heatmap)
        
        # Extract top k lane points from each sample
        scores, inds, _, ys_grid, xs_grid = self._topk(heatmap, k=self.max_lanes)
        
        # Only keep points with score above threshold
        keep = scores > self.hm_thres
        
        lane_instances = []
        for b in range(batch_size):
            # Lanes for this sample
            sample_lanes = []
            
            mask = keep[b]
            if not mask.any():
                # No lanes detected for this sample
                lane_instances.append(sample_lanes)
                continue
                
            # Get coordinates for valid detections
            xs_b_grid = xs_grid[b][mask]  # Width dimension indices (X-axis)
            ys_b_grid = ys_grid[b][mask]  # Height dimension indices (Y-axis)
            scores_b = scores[b][mask]
            
            # Convert grid indices to world coordinates
            x_coords_world = xs_b_grid * self.x_res + self.x_min
            y_coords_world = ys_b_grid * self.y_res + self.y_min
            
            # Get corresponding offsets, z-heights, and class predictions
            inds_b = inds[b][mask]
            
            # Convert to integer grid indices for feature extraction
            ys_b_int = ys_b_grid.long()
            xs_b_int = xs_b_grid.long()
            
            ys_b_int = torch.clamp(ys_b_int, 0, heatmap.shape[2] - 1)
            xs_b_int = torch.clamp(xs_b_int, 0, heatmap.shape[3] - 1)
            
            offsets_b = offset[b, 0, ys_b_int, xs_b_int]
            z_vals_b = z_pred[b, 0, ys_b_int, xs_b_int]
            
            y_coords_world_adjusted = y_coords_world + offsets_b * self.y_res
            x_coords_world_final = x_coords_world
            
            # Extract class predictions ONCE
            cls_scores = cls_pred[b, :, ys_b_int, xs_b_int].transpose(0, 1)  # [N, C]
            cls_ids = torch.argmax(cls_scores, dim=1)  # [N]
            
            # Prepare points_3d ONCE: [N, 3] with x, y_adjusted, z
            points_3d = torch.stack([x_coords_world_final, y_coords_world_adjusted, z_vals_b], dim=1)
            
            # Handle embedding vs non-embedding cases
            if self.use_embedding:
                embeddings_b = embed_pred[b, :, ys_b_int, xs_b_int].transpose(0, 1)  # [N, embed_dim]
                
                # Group by class ID and cluster within each class
                for class_id in torch.unique(cls_ids):
                    class_mask = cls_ids == class_id
                    if class_mask.sum() < self.clustering_min_points:
                        continue
                    
                    class_points = points_3d[class_mask]
                    class_embeddings = embeddings_b[class_mask]
                    class_scores = scores_b[class_mask]
                    
                    # Cluster within same class
                    clusters = self._group_lane_points_embedding(
                        class_points, class_embeddings,
                        torch.full((len(class_points),), class_id, device=class_points.device),
                        class_scores
                    )
                    sample_lanes.extend(clusters)
            else:
                # Non-embedding case: Use geometric clustering
                # FIXED: Use _group_lane_points instead of undefined _group_lane_points_geometric
                sample_lanes = self._group_lane_points(points_3d, cls_ids, scores_b, max_dist=self.lane_group_min_distance)
            
            lane_instances.append(sample_lanes)
        
        return lane_instances
