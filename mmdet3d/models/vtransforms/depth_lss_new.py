from typing import Tuple, Optional

import torch
import torch.nn.functional as F
from mmcv.runner import force_fp32
from torch import nn

from mmdet3d.models.builder import VTRANSFORMS

from .base import BaseDepthTransform

__all__ = ["DepthLSSTransform", "DepthLSSTransformWithDepthMap"]

class DepthLSSBase(BaseDepthTransform):
    """基础深度LSS变换类，包含共享的初始化和下采样逻辑"""
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        image_size: Tuple[int, int],
        feature_size: Tuple[int, int],
        xbound: Tuple[float, float, float],
        ybound: Tuple[float, float, float],
        zbound: Tu<PERSON>[float, float, float],
        dbound: Tuple[float, float, float],
        downsample: int = 1,
    ) -> None:
        super().__init__(
            in_channels=in_channels,
            out_channels=out_channels,
            image_size=image_size,
            feature_size=feature_size,
            xbound=xbound,
            ybound=ybound,
            zbound=zbound,
            dbound=dbound,
        )
        # 深度变换网络
        self.dtransform = nn.Sequential(
            nn.Conv2d(1, 8, 1),
            nn.BatchNorm2d(8),
            nn.ReLU(True),
            nn.Conv2d(8, 32, 5, stride=4, padding=2),
            nn.BatchNorm2d(32),
            nn.ReLU(True),
            nn.Conv2d(32, 64, 5, stride=2, padding=2),
            nn.BatchNorm2d(64),
            nn.ReLU(True),
        )
        # 深度网络
        self.depthnet = nn.Sequential(
            nn.Conv2d(in_channels + 64, in_channels, 3, padding=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(True),
            nn.Conv2d(in_channels, in_channels, 3, padding=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(True),
            nn.Conv2d(in_channels, self.D + self.C, 1),
        )
        # 下采样网络
        if downsample > 1:
            assert downsample == 2, downsample
            self.downsample = nn.Sequential(
                nn.Conv2d(out_channels, out_channels, 3, padding=1, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(True),
                nn.Conv2d(
                    out_channels,
                    out_channels,
                    3,                 
                    stride=downsample,
                    padding=1,
                    bias=False,
                ),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(True),
                nn.Conv2d(out_channels, out_channels, 3, padding=1, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(True),
            )
        else:
            self.downsample = nn.Identity()
            
        # 前视相机固定为索引0，简化参数传递
        # 基于cam_list: ['120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
        # 其中120_front始终位于索引0位置

    def forward(self, *args, **kwargs):
        """基础前向传播函数，应用下采样"""
        x = super().forward(*args, **kwargs)  # 调用BaseDepthTransform的forward函数
        x = self.downsample(x)  # 应用下采样
        return x


@VTRANSFORMS.register_module()
class DepthLSSTransform(DepthLSSBase):
    """标准深度LSS变换，不使用密集深度图"""
    @force_fp32()
    def get_cam_feats(self, x, d):
        """处理相机特征和深度图"""
        B, N, C, fH, fW = x.shape  # batch_size、camera numbers、feature_chanel、feature_h、feature_w

        d = d.view(B * N, *d.shape[2:])  # 重塑深度图
        x = x.view(B * N, C, fH, fW)  # 重塑特征图

        d = self.dtransform(d)  # 应用深度变换
        x = torch.cat([d, x], dim=1)  # 连接深度特征和图像特征
        x = self.depthnet(x)  # 应用深度网络

        depth = x[:, : self.D].softmax(dim=1)  # 获取深度概率分布
        x = depth.unsqueeze(1) * x[:, self.D : (self.D + self.C)].unsqueeze(2)  # 加权特征

        x = x.view(B, N, self.C, self.D, fH, fW)  # 重塑输出
        x = x.permute(0, 1, 3, 4, 5, 2)  # 调整维度顺序
        return x


@VTRANSFORMS.register_module()
class DepthLSSTransformWithDepthMap(DepthLSSBase):
    """使用密集深度图的深度LSS变换"""

    @force_fp32()
    def get_cam_feats(self, x, d, dense_depth=None):
        """处理相机特征和深度图，支持密集深度图
        
        Args:
            x: 图像特征 [B, N, C, fH, fW]
            d: 稀疏深度图 [B, N, H, W]
            dense_depth: 密集深度图 [B, H, W] 或 None
        """
        B, N, C, fH, fW = x.shape
        
        # 初始化结果张量
        result = torch.zeros(B, N, self.C, self.D, fH, fW, device=x.device)
        
        # 处理每个相机视角
        for cam_idx in range(N):
            # 如果是前视相机（第一个相机）且提供了密集深度图，使用密集深度图
            if cam_idx == 0 and dense_depth is not None:  # 简化：直接使用索引0作为前视相机
                result[:, cam_idx] = self._process_front_camera_with_depth(
                    x[:, cam_idx], dense_depth, fH, fW
                )
            else:
                # 对其他相机使用原始LSS处理
                result[:, cam_idx] = self._process_other_camera(
                    x[:, cam_idx], d[:, cam_idx], fH, fW
                )
        
        # 重排维度以匹配预期输出
        result = result.permute(0, 1, 3, 4, 5, 2)  # [B, N, D, fH, fW, C]
        return result

    def _process_front_camera_with_depth(self, cam_feats, dense_depth, fH, fW):
        """处理前视相机的密集深度图，优化版本
        
        Args:
            cam_feats: 前视相机特征 [B, C, fH, fW]
            dense_depth: 密集深度图 [B, H, W] 或 list of tensors
            fH, fW: 特征图的高度和宽度
            
        Returns:
            torch.Tensor: 加权特征 [B, C, D, fH, fW]
        """
        B, C = cam_feats.shape[:2]
        device = cam_feats.device
        
        print(f"🔍 Processing front camera with dense depth:")
        print(f"  Cam features shape: {cam_feats.shape}")
        print(f"  Target feature size: {fH}x{fW}")
        print(f"  Depth bounds: {self.dbound}")
        print(f"  Number of depth bins (D): {self.D}")
        
        # 处理密集深度图 - 添加类型检查和转换
        # Handle case where dense_depth is a list (due to batching)
        if isinstance(dense_depth, list): 
            # Convert list of tensors to a single tensor
            import torch
            depth_map = torch.stack(dense_depth, dim=0)
            print(f"Converted list to tensor with shape: {depth_map.shape}")
        else:
            depth_map = dense_depth
            
        # Convert to float tensor
        depth_map = depth_map.float()
        
        # Ensure depth_map is on the correct device
        if depth_map.device != device:
            depth_map = depth_map.to(device)
            print(f"Moved depth_map to device: {device}")
        
        print(f"  Dense depth map shape: {depth_map.shape}")
        print(f"  Dense depth range: {depth_map.min().item():.4f} to {depth_map.max().item():.4f}")
        
        # 调整深度图尺寸以匹配特征图
        if depth_map.shape[-2:] != (fH, fW):
            print(f"  Resizing depth map from {depth_map.shape[-2:]} to {fH}x{fW}")
            # 使用双线性插值调整尺寸，保持深度的连续性
            depth_map = F.interpolate(
                depth_map.unsqueeze(1), 
                size=(fH, fW), 
                mode='bilinear', 
                align_corners=False
            ).squeeze(1)
        
        # 验证深度值范围并进行裁剪
        valid_depth_mask = (depth_map > 0) & (depth_map < 1000)  # Reasonable depth range
        valid_depth_percentage = valid_depth_mask.float().mean().item() * 100
        print(f"  Valid depth pixels: {valid_depth_percentage:.2f}%")
        
        # Clamp to depth bounds but preserve original for debugging
        depth_map_clamped = torch.clamp(depth_map, self.dbound[0], self.dbound[1])
        clamped_pixels = (depth_map != depth_map_clamped).sum().item()
        print(f"  Pixels clamped to depth bounds: {clamped_pixels}")
        
        # 创建深度分布 - 使用更保守和稳定的方法
        depth_bins = torch.linspace(
            self.dbound[0], self.dbound[1], self.D, 
            device=device, dtype=depth_map.dtype
        )
        
        # 使用最近邻方法而不是高斯分布，避免数值不稳定
        # Find the closest depth bin for each pixel
        depth_map_expanded = depth_map_clamped.unsqueeze(1)  # [B, 1, fH, fW]
        depth_bins_expanded = depth_bins.view(1, -1, 1, 1)  # [1, D, 1, 1]
        
        # Calculate absolute distances
        distances = torch.abs(depth_map_expanded - depth_bins_expanded)  # [B, D, fH, fW]
        
        # Create hard assignment to closest bin (one-hot encoding)
        closest_bin_indices = torch.argmin(distances, dim=1)  # [B, fH, fW]
        depth_distribution = torch.zeros_like(distances)  # [B, D, fH, fW]
        depth_distribution.scatter_(1, closest_bin_indices.unsqueeze(1), 1.0)
        
        print(f"  Depth distribution shape: {depth_distribution.shape}")
        print(f"  Depth distribution sum per pixel (should be 1.0): {depth_distribution.sum(dim=1).mean().item():.6f}")
        
        # 使用更简单的深度特征处理
        # Create a simple depth feature by averaging across channels
        d_features = cam_feats.mean(dim=1, keepdim=True)  # [B, 1, fH, fW]
        # Expand to 64 channels as expected by depthnet
        d_features = d_features.expand(-1, 64, -1, -1)
        
        # 连接深度特征和相机特征
        cam_feats_with_d = torch.cat([d_features, cam_feats], dim=1)
        
        # Check for reasonable tensor sizes before depthnet
        print(f"  Input to depthnet shape: {cam_feats_with_d.shape}")
        
        cam_feats_processed = self.depthnet(cam_feats_with_d)
        
        # 分离深度预测和特征
        depth_pred = cam_feats_processed[:, :self.D]  # [B, D, fH, fW]
        feat = cam_feats_processed[:, self.D:self.D+self.C]  # [B, C, fH, fW]
        
        print(f"  Network depth prediction shape: {depth_pred.shape}")
        print(f"  Feature tensor shape: {feat.shape}")
        
        # 使用混合策略：主要使用密集深度，少量网络预测
        # Use primarily dense depth with small network prediction component
        alpha = 0.9  # 密集深度图的权重提高
        depth_pred_softmax = F.softmax(depth_pred, dim=1)
        final_depth_distribution = alpha * depth_distribution + (1 - alpha) * depth_pred_softmax
        
        # 确保最终分布是有效的概率分布
        final_depth_distribution = final_depth_distribution / (final_depth_distribution.sum(dim=1, keepdim=True) + 1e-8)
        
        print(f"  Final depth distribution sum: {final_depth_distribution.sum(dim=1).mean().item():.6f}")
        
        # 应用深度权重到特征
        weighted_feats = final_depth_distribution.unsqueeze(2) * feat.unsqueeze(1)  # [B, D, C, fH, fW]
        weighted_feats = weighted_feats.permute(0, 2, 1, 3, 4)  # [B, C, D, fH, fW]
        
        print(f"  Output weighted features shape: {weighted_feats.shape}")
        
        # Validate output
        if torch.isnan(weighted_feats).any():
            print("⚠️  WARNING: NaN values detected in weighted features!")
        if torch.isinf(weighted_feats).any():
            print("⚠️  WARNING: Inf values detected in weighted features!")
        
        return weighted_feats

    def _process_other_camera(self, cam_feats, cam_depth, fH, fW):
        """处理其他相机的标准LSS，优化版本"""
        B = cam_feats.shape[0]
        
        # 标准LSS处理
        if cam_depth.dim() == 3:
            cam_depth = cam_depth.unsqueeze(1)
        
        cam_depth = self.dtransform(cam_depth)
        cam_feats_with_d = torch.cat([cam_depth, cam_feats], dim=1)
        cam_feats_processed = self.depthnet(cam_feats_with_d)
        
        depth_distribution = cam_feats_processed[:, :self.D].softmax(dim=1)
        feat = cam_feats_processed[:, self.D:self.D+self.C]
        
        weighted_feats = depth_distribution.unsqueeze(1) * feat.unsqueeze(2)
        
        return weighted_feats

    def forward(self, img, points, sensor2ego, lidar2ego, lidar2camera, lidar2image, 
                cam_intrinsic, camera2lidar, img_aug_matrix, lidar_aug_matrix, 
                metas, dense_depth=None, **kwargs):
        """前向传播函数，支持密集深度图
        
        Args:
            dense_depth: 密集深度图 [B, H, W] 或 None
        """
        # 从父类方法中提取所需的变量
        rots = sensor2ego[..., :3, :3]
        trans = sensor2ego[..., :3, 3]
        intrins = cam_intrinsic[..., :3, :3]
        post_rots = img_aug_matrix[..., :3, :3]
        post_trans = img_aug_matrix[..., :3, 3]
        camera2lidar_rots = camera2lidar[..., :3, :3]
        camera2lidar_trans = camera2lidar[..., :3, 3]
        
        # 定义batch_size - 这是缺失的关键行
        batch_size = len(points)
        
        # 获取目标dtype，优先用points[0]的dtype（通常和img一致）
        target_dtype = points[0].dtype if hasattr(points[0], 'dtype') else img.dtype
        depth = torch.zeros(
            batch_size, img.shape[1], 1, *self.image_size,
            device=points[0].device,
            dtype=target_dtype
        )
        
        # 处理每个batch的点云投影到图像上，生成深度图
        for b in range(batch_size):
            cur_coords = points[b][:, :3].clone()
            cur_img_aug_matrix = img_aug_matrix[b]
            cur_lidar_aug_matrix = lidar_aug_matrix[b]
            cur_lidar2image = lidar2image[b]
            
            # inverse aug
            cur_coords -= cur_lidar_aug_matrix[:3, 3]
            cur_coords = torch.inverse(cur_lidar_aug_matrix[:3, :3]).matmul(
                cur_coords.transpose(1, 0)
            )
            # lidar2image
            cur_coords = cur_lidar2image[:, :3, :3].matmul(cur_coords)
            cur_coords += cur_lidar2image[:, :3, 3].reshape(-1, 3, 1)
            # get 2d coords
            dist = cur_coords[:, 2, :]
            cur_coords[:, 2, :] = torch.clamp(cur_coords[:, 2, :], 1e-5, 1e5)
            cur_coords[:, :2, :] /= cur_coords[:, 2:3, :]
            
            # imgaug
            cur_coords = cur_img_aug_matrix[:, :3, :3].matmul(cur_coords)
            cur_coords += cur_img_aug_matrix[:, :3, 3].reshape(-1, 3, 1)
            cur_coords = cur_coords[:, :2, :].transpose(1, 2)
            
            # normalize coords for grid sample
            cur_coords = cur_coords[..., [1, 0]]
            
            on_img = (
                (cur_coords[..., 0] < self.image_size[0])
                & (cur_coords[..., 0] >= 0)
                & (cur_coords[..., 1] < self.image_size[1])
                & (cur_coords[..., 1] >= 0)
            )
            for c in range(on_img.shape[0]):
                masked_coords = cur_coords[c, on_img[c]].long()
                masked_dist = dist[c, on_img[c]]
                depth[b, c, 0, masked_coords[:, 0], masked_coords[:, 1]] = masked_dist
        
        # 生成几何特征
        extra_rots = lidar_aug_matrix[..., :3, :3]
        extra_trans = lidar_aug_matrix[..., :3, 3]
        geom = self.get_geometry(
            camera2lidar_rots,
            camera2lidar_trans,
            intrins,
            post_rots,
            post_trans,
            extra_rots=extra_rots,
            extra_trans=extra_trans,
        )
        
        # 使用我们自定义的get_cam_feats处理特征，传入密集深度图
        x = self.get_cam_feats(img, depth, dense_depth=dense_depth)
        
        # Debug info for BEV pooling input
        print("=" * 50)
        print("BEV POOLING DEBUG INFORMATION")
        print("=" * 50)
        
        # Debug geometry features
        print(f"Geometry features shape: {geom.shape}")
        print(f"Geometry features dtype: {geom.dtype}")
        print(f"Geometry features device: {geom.device}")
        print(f"Geometry min/max values: {geom.min().item():.4f}/{geom.max().item():.4f}")
        
        # Debug camera features
        print(f"Camera features shape before BEV pooling: {x.shape}")
        print(f"Camera features dtype: {x.dtype}")
        print(f"Camera features device: {x.device}")
        print(f"Camera features min/max values: {x.min().item():.4f}/{x.max().item():.4f}")
        
        # Debug grid parameters
        print(f"BEV grid parameters:")
        print(f"  - xbound: {self.xbound}")
        print(f"  - ybound: {self.ybound}")
        print(f"  - zbound: {self.zbound}")
        print(f"  - dx: {self.dx}")
        print(f"  - bx: {self.bx}")
        print(f"  - nx: {self.nx}")
        
        # Track memory usage
        if hasattr(torch.cuda, 'memory_allocated'):
            print(f"GPU memory allocated before BEV pool: {torch.cuda.memory_allocated() / (1024**2):.2f} MB")
        
        # Time the BEV pooling operation
        import time
        start_time = time.time()
        
        # 应用BEV池化
        x = self.bev_pool(geom, x)
        
        end_time = time.time()
        
        # Debug output information
        print(f"BEV pooling took {(end_time - start_time) * 1000:.2f} ms")
        print(f"Output features shape after BEV pooling: {x.shape}")
        print(f"Output features min/max values: {x.min().item():.4f}/{x.max().item():.4f}")
        
        # Calculate density of valid values (non-zeros)
        valid_percentage = (x.abs().sum(dim=1) > 0).float().mean().item() * 100
        print(f"Percentage of valid BEV cells: {valid_percentage:.2f}%")
        
        # Track memory usage after BEV pooling
        if hasattr(torch.cuda, 'memory_allocated'):
            print(f"GPU memory allocated after BEV pool: {torch.cuda.memory_allocated() / (1024**2):.2f} MB")
        
        # For more detailed debugging, we can sample and print some values
        # from different regions of the BEV grid
        if x.shape[0] > 0:  # If batch size > 0
            # Center region
            center_h, center_w = x.shape[2] // 2, x.shape[3] // 2
            center_region = x[0, :, 
                            center_h-2:center_h+3, 
                            center_w-2:center_w+3]
            print(f"Center region (5x5) stats: " +
                  f"mean={center_region.mean().item():.4f}, " +
                  f"std={center_region.std().item():.4f}")
            
            # Check for NaN or Inf values
            has_nan = torch.isnan(x).any().item()
            has_inf = torch.isinf(x).any().item()
            print(f"Contains NaN: {has_nan}, Contains Inf: {has_inf}")
        
        print("=" * 50)
        
        # 添加详细的几何特征调试和安全检查
        # Detailed geometry feature debugging and safety checks
        print("\n" + "=" * 60)
        print("DETAILED GEOMETRY AND COORDINATE ANALYSIS")
        print("=" * 60)
        
        # Analyze geometry coordinate distribution
        geom_flat = geom.view(-1, 3)  # Flatten all spatial dimensions
        print(f"Total geometry points: {geom_flat.shape[0]}")
        
        for dim, dim_name in enumerate(['X', 'Y', 'Z']):
            coord_values = geom_flat[:, dim]
            print(f"{dim_name}-coordinates:")
            print(f"  Min: {coord_values.min().item():.4f}")
            print(f"  Max: {coord_values.max().item():.4f}")
            print(f"  Mean: {coord_values.mean().item():.4f}")
            print(f"  Std: {coord_values.std().item():.4f}")
            
            # Check how many points are within BEV bounds
            if dim == 0:  # X dimension
                valid_x = (coord_values >= self.xbound[0]) & (coord_values <= self.xbound[1])
                print(f"  Points within xbound [{self.xbound[0]}, {self.xbound[1]}]: {valid_x.sum().item()}/{len(coord_values)} ({100*valid_x.float().mean().item():.2f}%)")
            elif dim == 1:  # Y dimension
                valid_y = (coord_values >= self.ybound[0]) & (coord_values <= self.ybound[1])
                print(f"  Points within ybound [{self.ybound[0]}, {self.ybound[1]}]: {valid_y.sum().item()}/{len(coord_values)} ({100*valid_y.float().mean().item():.2f}%)")
            elif dim == 2:  # Z dimension
                valid_z = (coord_values >= self.zbound[0]) & (coord_values <= self.zbound[1])
                print(f"  Points within zbound [{self.zbound[0]}, {self.zbound[1]}]: {valid_z.sum().item()}/{len(coord_values)} ({100*valid_z.float().mean().item():.2f}%)")
        
        # Check calibration matrices for issues
        print(f"\nCalibration Matrix Analysis:")
        print(f"Camera intrinsics shape: {cam_intrinsic.shape}")
        print(f"Camera intrinsics range: {cam_intrinsic.min().item():.4f} to {cam_intrinsic.max().item():.4f}")
        print(f"Camera2lidar rotation range: {camera2lidar[..., :3, :3].abs().max().item():.4f}")
        print(f"Camera2lidar translation range: {camera2lidar[..., :3, 3].abs().max().item():.4f}")
        
        # Check augmentation matrices
        print(f"Image aug matrix range: {img_aug_matrix.abs().max().item():.4f}")
        print(f"LiDAR aug matrix range: {lidar_aug_matrix.abs().max().item():.4f}")
        
        # Apply BEV pooling with safety checks
        try:
            # CRITICAL: Pre-check to prevent IndexError
            # Compute and check the filtering condition before calling bev_pool
            B, N, D, H, W, C = x.shape
            Nprime = B * N * D * H * W
            
            # Flatten and transform geometry features to grid coordinates (same as in base.py)
            geom_feats_for_check = ((geom - (self.bx - self.dx / 2.0)) / self.dx).long()
            geom_feats_flat = geom_feats_for_check.view(Nprime, 3)
            
            # Apply the same filtering condition
            kept_check = (
                (geom_feats_flat[:, 0] >= 0) & (geom_feats_flat[:, 0] < self.nx[0]) &
                (geom_feats_flat[:, 1] >= 0) & (geom_feats_flat[:, 1] < self.nx[1]) &
                (geom_feats_flat[:, 2] >= 0) & (geom_feats_flat[:, 2] < self.nx[2])
            )
            
            num_valid_points = kept_check.sum().item()
            print(f"\nBEV Pooling Safety Check:")
            print(f"Total flattened points: {Nprime}")
            print(f"Points passing spatial bounds check: {num_valid_points}")
            print(f"Percentage of valid points: {100 * num_valid_points / Nprime:.4f}%")
            
            if num_valid_points == 0:
                print("❌ CRITICAL ERROR: No points pass spatial bounds check!")
                print("This will cause IndexError in BEV pooling.")
                print("Returning zero tensor to prevent crash...")
                
                # Return a zero tensor with the expected output shape
                output_h, output_w = self.nx[0].item(), self.nx[1].item()
                zero_output = torch.zeros(B, self.C, output_h, output_w, 
                                        device=x.device, dtype=x.dtype)
                return self.downsample(zero_output)
            else:
                print("✅ Sufficient valid points for BEV pooling")
                x = self.bev_pool(geom, x)
                
        except Exception as e:
            print(f"❌ BEV pooling failed with error: {e}")
            print("Returning zero tensor to prevent training crash...")
            
            B, N, D, H, W, C = x.shape
            output_h, output_w = self.nx[0].item(), self.nx[1].item()
            zero_output = torch.zeros(B, C, output_h, output_w, 
                                    device=x.device, dtype=x.dtype)
            return self.downsample(zero_output)
        
        print("=" * 60)
        
        # 应用下采样
        x = self.downsample(x)
        return x