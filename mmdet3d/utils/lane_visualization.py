import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import cv2
import torch
from matplotlib.patches import Rectangle

class BEVLaneVisualizer:
    """Visualizer for BEV lane data.
    
    This class provides utility methods to visualize Bird's Eye View (BEV)
    lane detection data, including heatmaps, lane types, and instance IDs.
    """
    
    def __init__(self, point_cloud_range, grid_size):
        """Initialize the BEV lane visualizer.
        
        Args:
            point_cloud_range (list[float]): Point cloud range [x_min, y_min, z_min, x_max, y_max, z_max]
            grid_size (list[int]): Grid size [x_size, y_size]
        """
        self.point_cloud_range = point_cloud_range
        self.grid_size = grid_size
        
        self.x_min, self.y_min, self.z_min = point_cloud_range[:3]
        self.x_max, self.y_max, self.z_max = point_cloud_range[3:]
        self.x_size, self.y_size = grid_size
        
        # Calculate grid cell size in world coordinates
        self.grid_cell_size_x = (self.x_max - self.x_min) / self.x_size
        self.grid_cell_size_y = (self.y_max - self.y_min) / self.y_size
        
        # Define custom colormap for lane types
        self.lane_colors = {
            'unknown': [128, 128, 128],  # gray
            'white-solid': [255, 255, 255],  # white
            'white-dashed': [220, 220, 220],  # light gray
            'white-double-solid': [200, 200, 200],  # slightly darker white
            'white-solid-dashed': [180, 180, 180],  # mix
            'white-dashed-solid': [160, 160, 160],  # mix
            'white-double-dashed': [140, 140, 140],  # light gray
            'yellow-solid': [0, 255, 255],  # yellow
            'yellow-dashed': [0, 220, 220],  # lighter yellow
            'yellow-double-solid': [0, 200, 200],  # slightly darker yellow
            'yellow-solid-dashed': [0, 180, 180],  # mix
            'left-yellow-right-white-double-solid': [0, 160, 255],  # yellow-white
            'road-edge-dashed': [0, 0, 255]  # red
        }
        
        # Default class names
        self.class_names = [
            'unknown',
            'white-solid',
            'white-dashed',
            'white-double-solid',
            'white-solid-dashed',
            'white-dashed-solid',
            'white-double-dashed',
            'yellow-solid',
            'yellow-dashed',
            'yellow-double-solid',
            'yellow-solid-dashed',
            'left-yellow-right-white-double-solid',
            'road-edge-dashed'
        ]
    
    def set_class_names(self, class_names):
        """Set the lane class names.
        
        Args:
            class_names (list[str]): List of lane class names
        """
        self.class_names = class_names
    
    def visualize_heatmap(self, heatmap, save_path=None, show=False, title='Lane Detection Heatmap'):
        """Visualize lane heatmap.
        
        Args:
            heatmap (np.ndarray or torch.Tensor): Lane heatmap of shape [H, W] or [1, H, W]
            save_path (str, optional): Path to save the visualization. Defaults to None.
            show (bool, optional): Whether to show the visualization. Defaults to False.
            title (str, optional): Title of the visualization. Defaults to 'Lane Detection Heatmap'.
            
        Returns:
            np.ndarray: Visualization image
        """
        # Convert to numpy array if tensor
        if isinstance(heatmap, torch.Tensor):
            heatmap = heatmap.detach().cpu().numpy()
        
        # Ensure 2D
        if heatmap.ndim == 3:
            heatmap = heatmap[0]
        
        # Create figure
        plt.figure(figsize=(10, 10))
        
        # Draw grid
        plt.grid(which='both', color='gray', linestyle='-', alpha=0.3)
        
        # Draw heatmap
        plt.imshow(heatmap.T, origin='lower', cmap='hot', interpolation='nearest',
                  extent=[self.x_min, self.x_max, self.y_min, self.y_max])
        
        plt.colorbar(label='Heatmap Intensity')
        plt.title(title)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        
        # Draw grid lines
        grid_x = np.linspace(self.x_min, self.x_max, self.x_size + 1)
        grid_y = np.linspace(self.y_min, self.y_max, self.y_size + 1)
        
        for x in grid_x[::5]:  # Draw every 5th line for readability
            plt.axvline(x=x, color='gray', linestyle='-', alpha=0.3)
        for y in grid_y[::5]:  # Draw every 5th line for readability
            plt.axhline(y=y, color='gray', linestyle='-', alpha=0.3)
        
        # Draw coordinate system
        plt.arrow(self.x_min, self.y_min, 2, 0, head_width=0.5, head_length=0.5, fc='r', ec='r')
        plt.arrow(self.x_min, self.y_min, 0, 2, head_width=0.5, head_length=0.5, fc='g', ec='g')
        plt.text(self.x_min + 2.5, self.y_min, 'x', color='r')
        plt.text(self.x_min, self.y_min + 2.5, 'y', color='g')
        
        # Save if path is provided
        if save_path:
            plt.savefig(save_path)
        
        # Show if requested
        if show:
            plt.show()
        
        # Convert to image
        plt.tight_layout()
        fig = plt.gcf()
        fig.canvas.draw()
        img = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
        img = img.reshape(fig.canvas.get_width_height()[::-1] + (3,))
        
        plt.close()
        
        return img
    
    def visualize_lane_classes(self, cls_map, mask=None, save_path=None, show=False, title='Lane Class Map'):
        """Visualize lane classes.
        
        Args:
            cls_map (np.ndarray or torch.Tensor): Lane class map of shape [C, H, W]
            mask (np.ndarray or torch.Tensor, optional): Valid mask of shape [H, W]. Defaults to None.
            save_path (str, optional): Path to save the visualization. Defaults to None.
            show (bool, optional): Whether to show the visualization. Defaults to False.
            title (str, optional): Title of the visualization. Defaults to 'Lane Class Map'.
            
        Returns:
            np.ndarray: Visualization image
        """
        # Convert to numpy array if tensor
        if isinstance(cls_map, torch.Tensor):
            cls_map = cls_map.detach().cpu().numpy()
        if isinstance(mask, torch.Tensor):
            mask = mask.detach().cpu().numpy()
        
        # Apply mask if provided
        if mask is not None:
            if mask.ndim == 3:
                mask = mask[0]
        
        num_classes = cls_map.shape[0]
        h, w = cls_map.shape[1:]
        
        # Create a visualization image
        vis_image = np.zeros((h, w, 3), dtype=np.uint8)
        
        # Find maximum class for each pixel
        if mask is not None:
            # Only consider masked areas
            cls_map_masked = cls_map.copy()
            for c in range(num_classes):
                cls_map_masked[c] *= mask
                
            max_class = np.argmax(cls_map_masked, axis=0)
        else:
            max_class = np.argmax(cls_map, axis=0)
        
        # Assign colors based on class
        for c in range(num_classes):
            if c < len(self.class_names):
                class_name = self.class_names[c]
                if class_name in self.lane_colors:
                    color = self.lane_colors[class_name]
                else:
                    color = np.random.randint(0, 255, 3).tolist()
            else:
                # Random color for classes without a name
                color = np.random.randint(0, 255, 3).tolist()
            
            # Set color for this class
            mask_c = (max_class == c)
            vis_image[mask_c] = color
        
        # Create figure
        plt.figure(figsize=(12, 10))
        
        # Draw grid
        plt.grid(which='both', color='gray', linestyle='-', alpha=0.3)
        
        # Draw class map
        plt.imshow(cv2.cvtColor(vis_image.transpose(1, 0, 2), cv2.COLOR_BGR2RGB), 
                  interpolation='nearest', 
                  extent=[self.x_min, self.x_max, self.y_min, self.y_max])
        
        plt.title(title)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        
        # Draw grid lines
        grid_x = np.linspace(self.x_min, self.x_max, self.x_size + 1)
        grid_y = np.linspace(self.y_min, self.y_max, self.y_size + 1)
        
        for x in grid_x[::5]:  # Draw every 5th line for readability
            plt.axvline(x=x, color='gray', linestyle='-', alpha=0.3)
        for y in grid_y[::5]:  # Draw every 5th line for readability
            plt.axhline(y=y, color='gray', linestyle='-', alpha=0.3)
        
        # Create legend
        legend_elements = []
        for c in range(num_classes):
            if c < len(self.class_names):
                class_name = self.class_names[c]
                if class_name in self.lane_colors:
                    color = [x/255.0 for x in self.lane_colors[class_name]]
                else:
                    color = np.random.random(3).tolist()
                
                # Only add to legend if class exists in the image
                if np.any(max_class == c):
                    legend_elements.append(Rectangle((0, 0), 1, 1, color=color, label=f"{c}: {class_name}"))
        
        if legend_elements:
            plt.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.2, 1))
        
        # Save if path is provided
        if save_path:
            plt.savefig(save_path, bbox_inches='tight')
        
        # Show if requested
        if show:
            plt.show()
        
        # Convert to image
        plt.tight_layout()
        fig = plt.gcf()
        fig.canvas.draw()
        img = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
        img = img.reshape(fig.canvas.get_width_height()[::-1] + (3,))
        
        plt.close()
        
        return img
    
    def visualize_instance_ids(self, instance_ids, mask=None, save_path=None, show=False, title='Lane Instance IDs'):
        """Visualize lane instance IDs.
        
        Args:
            instance_ids (np.ndarray or torch.Tensor): Lane instance ID map of shape [H, W]
            mask (np.ndarray or torch.Tensor, optional): Valid mask of shape [H, W]. Defaults to None.
            save_path (str, optional): Path to save the visualization. Defaults to None.
            show (bool, optional): Whether to show the visualization. Defaults to False.
            title (str, optional): Title of the visualization. Defaults to 'Lane Instance IDs'.
            
        Returns:
            np.ndarray: Visualization image
        """
        # Convert to numpy array if tensor
        if isinstance(instance_ids, torch.Tensor):
            instance_ids = instance_ids.detach().cpu().numpy()
        if isinstance(mask, torch.Tensor):
            mask = mask.detach().cpu().numpy()
        
        # Apply mask if provided
        if mask is not None:
            if mask.ndim == 3:
                mask = mask[0]
            instance_ids = instance_ids * mask
        
        # Get unique instance IDs (excluding 0, which is background)
        unique_ids = np.unique(instance_ids)
        unique_ids = unique_ids[unique_ids > 0]
        num_instances = len(unique_ids)
        
        # Create colormap for instances
        cmap = plt.get_cmap('tab20', max(num_instances, 1))
        colors = [cmap(i)[:3] for i in range(num_instances)]
        
        # Create a visualization image
        h, w = instance_ids.shape
        vis_image = np.zeros((h, w, 3), dtype=np.uint8)
        
        # Background color
        vis_image[instance_ids == 0] = [50, 50, 50]  # Dark gray for background
        
        # Assign colors based on instance ID
        for i, instance_id in enumerate(unique_ids):
            color = (np.array(colors[i % len(colors)]) * 255).astype(np.uint8)
            vis_image[instance_ids == instance_id] = color
        
        # Create figure
        plt.figure(figsize=(12, 10))
        
        # Draw grid
        plt.grid(which='both', color='gray', linestyle='-', alpha=0.3)
        
        # Draw instance ID map
        plt.imshow(vis_image.transpose(1, 0, 2), interpolation='nearest',
                  extent=[self.x_min, self.x_max, self.y_min, self.y_max])
        
        plt.title(title)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        
        # Draw grid lines
        grid_x = np.linspace(self.x_min, self.x_max, self.x_size + 1)
        grid_y = np.linspace(self.y_min, self.y_max, self.y_size + 1)
        
        for x in grid_x[::5]:  # Draw every 5th line for readability
            plt.axvline(x=x, color='gray', linestyle='-', alpha=0.3)
        for y in grid_y[::5]:  # Draw every 5th line for readability
            plt.axhline(y=y, color='gray', linestyle='-', alpha=0.3)
        
        # Create legend
        legend_elements = []
        for i, instance_id in enumerate(unique_ids):
            color = colors[i % len(colors)]
            count = np.sum(instance_ids == instance_id)
            legend_elements.append(Rectangle((0, 0), 1, 1, color=color, 
                                            label=f"ID {instance_id}: {count} points"))
        
        if legend_elements:
            plt.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.2, 1))
        
        # Save if path is provided
        if save_path:
            plt.savefig(save_path, bbox_inches='tight')
        
        # Show if requested
        if show:
            plt.show()
        
        # Convert to image
        plt.tight_layout()
        fig = plt.gcf()
        fig.canvas.draw()
        img = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
        img = img.reshape(fig.canvas.get_width_height()[::-1] + (3,))
        
        plt.close()
        
        return img
    
    def visualize_all(self, lane_targets, save_dir=None, show=False, prefix=''):
        """Visualize all lane target components.
        
        Args:
            lane_targets (dict): Lane targets from data pipeline.
            save_dir (str, optional): Directory to save visualizations. Defaults to None.
            show (bool, optional): Whether to show visualizations. Defaults to False.
            prefix (str, optional): Prefix for saved file names. Defaults to ''.
            
        Returns:
            dict: Dictionary of visualization images.
        """
        vis_images = {}
        
        # Create save directory if needed
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        # Process DataContainer if needed
        processed_targets = {}
        for k, v in lane_targets.items():
            if hasattr(v, 'data'):
                processed_targets[k] = v.data[0]
            else:
                processed_targets[k] = v
        
        # Visualize heatmap
        if 'segmentation' in processed_targets:
            heatmap = processed_targets['segmentation']
            save_path = None
            if save_dir:
                save_path = os.path.join(save_dir, f"{prefix}heatmap.png")
            vis_images['heatmap'] = self.visualize_heatmap(
                heatmap, save_path=save_path, show=show, title='Lane Heatmap'
            )
        
        # Visualize lane classes
        if 'gt_cls' in processed_targets and 'mask' in processed_targets:
            cls_map = processed_targets['gt_cls']
            mask = processed_targets['mask']
            save_path = None
            if save_dir:
                save_path = os.path.join(save_dir, f"{prefix}classes.png")
            vis_images['classes'] = self.visualize_lane_classes(
                cls_map, mask=mask, save_path=save_path, show=show, title='Lane Classes'
            )
        
        # Visualize instance IDs
        if 'gt_instance_ids' in processed_targets and 'mask' in processed_targets:
            instance_ids = processed_targets['gt_instance_ids']
            mask = processed_targets['mask']
            save_path = None
            if save_dir:
                save_path = os.path.join(save_dir, f"{prefix}instances.png")
            vis_images['instances'] = self.visualize_instance_ids(
                instance_ids, mask=mask, save_path=save_path, show=show, title='Lane Instances'
            )
        
        return vis_images


def debug_visualize_lane_targets(lane_targets, point_cloud_range, grid_size, 
                                class_names=None, save_dir=None, show=False, prefix=''):
    """Debug visualization for lane targets.
    
    Args:
        lane_targets (dict): Lane targets from data pipeline.
        point_cloud_range (list[float]): Point cloud range [x_min, y_min, z_min, x_max, y_max, z_max]
        grid_size (list[int]): Grid size [x_size, y_size]
        class_names (list[str], optional): Lane class names. Defaults to None.
        save_dir (str, optional): Directory to save visualizations. Defaults to None.
        show (bool, optional): Whether to show visualizations. Defaults to False.
        prefix (str, optional): Prefix for saved file names. Defaults to ''.
    """
    visualizer = BEVLaneVisualizer(point_cloud_range, grid_size)
    
    if class_names:
        visualizer.set_class_names(class_names)
    
    # Process DataContainer if needed
    processed_targets = {}
    for k, v in lane_targets.items():
        if hasattr(v, 'data'):
            processed_targets[k] = v.data[0].cpu().numpy() if isinstance(v.data[0], torch.Tensor) else v.data[0]
        elif isinstance(v, torch.Tensor):
            processed_targets[k] = v.cpu().numpy()
        else:
            processed_targets[k] = v
    
    # Create save directory if needed
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # Debug output
    print(f"[VISUALIZE_DEBUG] Visualizing lane targets:")
    for k, v in processed_targets.items():
        if isinstance(v, np.ndarray):
            print(f"  - {k}: shape={v.shape}, min={v.min()}, max={v.max()}, sum={v.sum()}")
        else:
            print(f"  - {k}: {type(v)}")
    
    # Visualize each component
    visualizer.visualize_all(processed_targets, save_dir=save_dir, show=show, prefix=prefix)
    
    # Return processed targets for further debugging
    return processed_targets