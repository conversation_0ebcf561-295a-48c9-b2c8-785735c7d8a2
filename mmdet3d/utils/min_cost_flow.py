"""
MinCostFlow solver for 3D lane matching.

Adapted from OpenLane evaluation methodology. The implementation is based 
on OR-Tools from Google to solve the minimum cost flow problem for bipartite matching.
"""

import numpy as np

def SolveMinCostFlow(adj_mat, cost_mat):
    """
    Solve minimum cost flow problem for lane matching.
    
    This function uses Google's OR-Tools to solve a min-cost flow problem,
    which finds the optimal matching between ground truth lanes and predicted lanes
    based on a cost matrix.
    
    Args:
        adj_mat: Adjacency matrix (binary) indicating possible matches between 
                 ground truth and prediction lanes.
        cost_mat: Cost matrix indicating the matching costs between ground truth 
                 and prediction lanes.
                 
    Returns:
        List of [gt_idx, pred_idx, cost] triplets representing the matches.
    """
    try:
        from ortools.graph import pywrapgraph
        
        cnt_1, cnt_2 = adj_mat.shape  # gt_count, pred_count
        cnt_nonzero_row = int(np.sum(np.sum(adj_mat, axis=1) > 0))
        cnt_nonzero_col = int(np.sum(np.sum(adj_mat, axis=0) > 0))
        
        # Prepare directed graph for the flow
        start_nodes = np.zeros(cnt_1, dtype=np.int).tolist() +\
                    np.repeat(np.array(range(1, cnt_1+1)), cnt_2).tolist() + \
                    [i for i in range(cnt_1+1, cnt_1 + cnt_2 + 1)]
        end_nodes = [i for i in range(1, cnt_1+1)] + \
                    np.repeat(np.array([i for i in range(cnt_1+1, cnt_1 + cnt_2 + 1)]).reshape([1, -1]), cnt_1, axis=0).flatten().tolist() + \
                    [cnt_1 + cnt_2 + 1 for i in range(cnt_2)]
        capacities = np.ones(cnt_1, dtype=np.int).tolist() + adj_mat.flatten().astype(np.int).tolist() + np.ones(cnt_2, dtype=np.int).tolist()
        costs = (np.zeros(cnt_1, dtype=np.int).tolist() + cost_mat.flatten().astype(np.int).tolist() + np.zeros(cnt_2, dtype=np.int).tolist())
        
        # Define supplies at each node
        supplies = [min(cnt_nonzero_row, cnt_nonzero_col)] + np.zeros(cnt_1 + cnt_2, dtype=np.int).tolist() + [-min(cnt_nonzero_row, cnt_nonzero_col)]
        source = 0
        sink = cnt_1 + cnt_2 + 1
        
        # Initialize min cost flow solver
        min_cost_flow = pywrapgraph.SimpleMinCostFlow()
        
        # Add each arc
        for i in range(len(start_nodes)):
            min_cost_flow.AddArcWithCapacityAndUnitCost(start_nodes[i], end_nodes[i],
                                                    capacities[i], costs[i])
        
        # Add node supplies
        for i in range(len(supplies)):
            min_cost_flow.SetNodeSupply(i, supplies[i])
        
        match_results = []
        # Find the minimum cost flow
        if min_cost_flow.Solve() == min_cost_flow.OPTIMAL:
            for arc in range(min_cost_flow.NumArcs()):
                # Ignore arcs from source or to sink
                if min_cost_flow.Tail(arc) != source and min_cost_flow.Head(arc) != sink:
                    # Arcs with flow value > 0 represent matches
                    if min_cost_flow.Flow(arc) > 0:
                        match_results.append([min_cost_flow.Tail(arc)-1,
                                            min_cost_flow.Head(arc)-cnt_1-1,
                                            min_cost_flow.UnitCost(arc)])
        else:
            print('There was an issue with the min cost flow input.')
        
        return match_results
    
    except ImportError:
        # Fallback to greedy matching if ortools is not available
        print("Warning: ortools not available, falling back to greedy matching")
        matches = []
        # Create a copy of cost matrix to modify
        costs = cost_mat.copy()
        # Set unconnected pairs to infinite cost
        costs[adj_mat == 0] = float('inf')
        
        # While there are valid matches to make
        while np.min(costs) < float('inf'):
            # Find minimum cost match
            i, j = np.unravel_index(np.argmin(costs), costs.shape)
            # Add to matches
            matches.append([i, j, costs[i, j]])
            # Remove these rows/cols from consideration
            costs[i, :] = float('inf')
            costs[:, j] = float('inf')
        
        return matches 