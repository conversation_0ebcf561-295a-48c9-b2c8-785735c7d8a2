#!/usr/bin/env python3
"""
训练性能监控脚本
监控3D车道线检测训练过程中的关键指标
"""

import os
import re
import json
import argparse
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import tensorboard as tb
from datetime import datetime

class TrainingMonitor:
    def __init__(self, log_dir, experiment_name):
        self.log_dir = Path(log_dir)
        self.experiment_name = experiment_name
        self.metrics = {
            'loss_heatmap': [],
            'loss_offset': [],
            'loss_z': [],
            'loss_cls': [],
            'loss_embedding': [],
            'total_loss': [],
            'learning_rate': [],
            'epoch': []
        }
    
    def parse_log_file(self, log_file):
        """解析训练日志文件"""
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        for line in lines:
            # 匹配损失信息
            loss_pattern = r'.*epoch: (\d+).*loss_heatmap: ([\d.]+).*loss_offset: ([\d.]+).*loss_z: ([\d.]+).*loss_cls: ([\d.]+).*loss_embedding: ([\d.]+).*loss: ([\d.]+)'
            match = re.search(loss_pattern, line)
            if match:
                epoch = int(match.group(1))
                loss_heatmap = float(match.group(2))
                loss_offset = float(match.group(3))
                loss_z = float(match.group(4))
                loss_cls = float(match.group(5))
                loss_embedding = float(match.group(6))
                total_loss = float(match.group(7))
                
                self.metrics['epoch'].append(epoch)
                self.metrics['loss_heatmap'].append(loss_heatmap)
                self.metrics['loss_offset'].append(loss_offset)
                self.metrics['loss_z'].append(loss_z)
                self.metrics['loss_cls'].append(loss_cls)
                self.metrics['loss_embedding'].append(loss_embedding)
                self.metrics['total_loss'].append(total_loss)
            
            # 匹配学习率信息
            lr_pattern = r'.*lr: ([\d.e-]+)'
            lr_match = re.search(lr_pattern, line)
            if lr_match:
                lr = float(lr_match.group(1))
                self.metrics['learning_rate'].append(lr)
    
    def plot_training_curves(self, save_path=None):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'Training Progress - {self.experiment_name}', fontsize=16)
        
        # 总损失
        axes[0, 0].plot(self.metrics['epoch'], self.metrics['total_loss'], 'b-', linewidth=2)
        axes[0, 0].set_title('Total Loss')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 热力图损失
        axes[0, 1].plot(self.metrics['epoch'], self.metrics['loss_heatmap'], 'r-', linewidth=2)
        axes[0, 1].set_title('Heatmap Loss')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 偏移损失
        axes[0, 2].plot(self.metrics['epoch'], self.metrics['loss_offset'], 'g-', linewidth=2)
        axes[0, 2].set_title('Offset Loss')
        axes[0, 2].set_xlabel('Epoch')
        axes[0, 2].set_ylabel('Loss')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 高度损失
        axes[1, 0].plot(self.metrics['epoch'], self.metrics['loss_z'], 'm-', linewidth=2)
        axes[1, 0].set_title('Z-Height Loss')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Loss')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 分类损失
        axes[1, 1].plot(self.metrics['epoch'], self.metrics['loss_cls'], 'c-', linewidth=2)
        axes[1, 1].set_title('Classification Loss')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Loss')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 嵌入损失
        if self.metrics['loss_embedding']:
            axes[1, 2].plot(self.metrics['epoch'], self.metrics['loss_embedding'], 'orange', linewidth=2)
            axes[1, 2].set_title('Embedding Loss')
            axes[1, 2].set_xlabel('Epoch')
            axes[1, 2].set_ylabel('Loss')
            axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_report(self):
        """生成训练报告"""
        if not self.metrics['epoch']:
            print("❌ 没有找到训练数据")
            return
        
        print(f"📊 训练监控报告 - {self.experiment_name}")
        print("=" * 60)
        
        # 基本统计
        total_epochs = max(self.metrics['epoch']) if self.metrics['epoch'] else 0
        print(f"🏃 训练轮数: {total_epochs}")
        print(f"📈 总迭代数: {len(self.metrics['epoch'])}")
        
        # 损失分析
        if self.metrics['total_loss']:
            initial_loss = self.metrics['total_loss'][0]
            final_loss = self.metrics['total_loss'][-1]
            loss_reduction = (initial_loss - final_loss) / initial_loss * 100
            
            print(f"\n💡 损失分析:")
            print(f"   初始总损失: {initial_loss:.4f}")
            print(f"   最终总损失: {final_loss:.4f}")
            print(f"   损失降低率: {loss_reduction:.2f}%")
            
            # 各组件损失
            components = ['heatmap', 'offset', 'z', 'cls', 'embedding']
            print(f"\n🔍 各组件最终损失:")
            for comp in components:
                loss_key = f'loss_{comp}'
                if self.metrics[loss_key]:
                    final_comp_loss = self.metrics[loss_key][-1]
                    print(f"   {comp:12s}: {final_comp_loss:.4f}")
        
        # 学习率分析
        if self.metrics['learning_rate']:
            initial_lr = self.metrics['learning_rate'][0]
            final_lr = self.metrics['learning_rate'][-1]
            print(f"\n📚 学习率变化:")
            print(f"   初始学习率: {initial_lr:.2e}")
            print(f"   最终学习率: {final_lr:.2e}")
        
        # 性能建议
        print(f"\n💡 性能建议:")
        if self.metrics['total_loss']:
            recent_losses = self.metrics['total_loss'][-10:]
            loss_variance = np.var(recent_losses) if len(recent_losses) > 1 else 0
            
            if loss_variance < 0.001:
                print("   ✅ 损失已收敛，训练稳定")
            elif self.metrics['total_loss'][-1] > self.metrics['total_loss'][0] * 0.5:
                print("   ⚠️  损失降低不够明显，考虑:")
                print("      - 调整学习率")
                print("      - 检查数据质量")
                print("      - 增加训练轮数")
            else:
                print("   ✅ 训练进展良好")

def main():
    parser = argparse.ArgumentParser(description='监控3D车道线检测训练性能')
    parser.add_argument('--log-dir', type=str, required=True, 
                       help='训练日志目录')
    parser.add_argument('--experiment', type=str, default='3d_lane_detection',
                       help='实验名称')
    parser.add_argument('--save-plots', type=str, 
                       help='保存图表的路径')
    
    args = parser.parse_args()
    
    # 创建监控器
    monitor = TrainingMonitor(args.log_dir, args.experiment)
    
    # 查找日志文件
    log_files = list(Path(args.log_dir).glob('*.log'))
    if not log_files:
        print("❌ 在指定目录中未找到日志文件")
        return
    
    # 解析最新的日志文件
    latest_log = max(log_files, key=os.path.getctime)
    print(f"📖 解析日志文件: {latest_log}")
    
    monitor.parse_log_file(latest_log)
    
    # 生成报告
    monitor.generate_report()
    
    # 绘制曲线
    save_path = args.save_plots or f"training_curves_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    monitor.plot_training_curves(save_path)
    
    print(f"✅ 监控完成，图表已保存至: {save_path}")

if __name__ == "__main__":
    main() 