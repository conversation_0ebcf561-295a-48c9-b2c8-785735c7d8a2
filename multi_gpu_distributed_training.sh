#!/bin/bash

# === Enhanced Multi-GPU Distributed Training Script for BEVFusion Lane Detection ===
# Based on the original train_distributed.sh format with optimizations

# Configuration
GPUS=4
PORT=29501
CONFIG=configs/lane/config_3d_lane_detection_task_based_bevfusion_embadding_new_params_distributed_training.yaml
RUN_DIR=experiments/lane_detection_distributed
VERSION="lane_detection"

# Environment optimization
export CUDA_VISIBLE_DEVICES=0,1,2,3
export OMP_NUM_THREADS=4  # Optimize OpenMP threading
export NCCL_DEBUG=INFO
export NCCL_IB_DISABLE=0  # Enable InfiniBand if available
export NCCL_IB_HCA=mlx5   # Specify HCA for InfiniBand
export NCCL_SOCKET_IFNAME=eth0
export NCCL_MIN_NCHANNELS=4  # Minimum number of channels
export CUDA_LAUNCH_BLOCKING=0  # Disable CUDA launch blocking for better performance
export CUDA_DEVICE_MAX_CONNECTIONS=1  # Limit max connections per device
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512  # Optimize CUDA memory allocation
export TORCH_DISTRIBUTED_DEBUG=DETAIL  # Detailed distributed debugging info

# Function to check GPU availability and memory
check_gpus() {
    if ! command -v nvidia-smi &> /dev/null; then
        echo "Error: NVIDIA driver not found"
        exit 1
    fi

    local required_gpus=$1
    local available_gpus=$(nvidia-smi -L | wc -l)
    if [ $available_gpus -lt $required_gpus ]; then
        echo "Error: Required $required_gpus GPUs but only $available_gpus available"
        exit 1
    fi

    # Check GPU memory (minimum 16GB required for BEVFusion)
    local min_memory=16000
    for i in $(seq 0 $((required_gpus-1))); do
        local memory=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits -i $i)
        if [ $memory -lt $min_memory ]; then
            echo "Error: GPU $i has insufficient memory (${memory}MB < ${min_memory}MB)"
            exit 1
        fi
    done
    
    echo "GPU check passed: $required_gpus GPUs with at least ${min_memory}MB memory each"
}

# Function to setup logging
setup_logging() {
    local version=$1
    LOG_DIR="logs/${version}"
    mkdir -p ${LOG_DIR}
    exec &> >(tee -a "${LOG_DIR}/training_$(date +%Y%m%d_%H%M%S).log")
}

# Error handling function
handle_error() {
    echo "ERROR: $1" >&2
    mkdir -p logs
    echo "Training failed at $(date)" >> logs/training_errors.log
    echo "$1" >> logs/training_errors.log
    exit 1
}

# Create run directory if it doesn't exist
mkdir -p $RUN_DIR

# Run checks and setup
check_gpus ${GPUS} || handle_error "GPU check failed"
setup_logging "${VERSION}"

# Print training information
echo "Starting distributed training with configuration:"
echo "Config path: ${CONFIG}"
echo "Number of GPUs: ${GPUS}"
echo "Version: ${VERSION}"
echo "Output directory: ${RUN_DIR}"
echo "Master port: ${PORT}"
echo "$(date): Training started with config ${CONFIG}" | tee -a logs/training_history.log

# Start distributed training
torchrun \
    --nproc_per_node=${GPUS} \
    --master_port=${PORT} \
    tools/train.py \
    --config ${CONFIG} \
    --run_dir ${RUN_DIR} \
    --launcher pytorch

# Check training exit code
TRAIN_EXIT_CODE=$?

if [ $TRAIN_EXIT_CODE -ne 0 ]; then
    echo "$(date): Training failed with exit code $TRAIN_EXIT_CODE" | tee -a logs/training_errors.log
    exit $TRAIN_EXIT_CODE
else
    echo "$(date): Training completed successfully" | tee -a logs/training_history.log
    echo "Training completed!"
fi