#!/usr/bin/env python3
"""
优化调试日志系统

这个脚本将BEV Lane Heatmap Head中的print()调试语句
替换为logging.debug()，并添加日志级别控制开关，
提高生产环境的性能。

主要功能：
1. 识别所有的print()调试语句
2. 替换为适当的logging调用
3. 添加日志配置和级别控制
4. 保持调试信息的完整性
"""

import os
import re
import sys
from pathlib import Path

def analyze_print_statements():
    """
    分析BEV Lane Heatmap Head中的print语句
    """
    print("=" * 80)
    print("调试日志系统优化分析")
    print("=" * 80)
    
    # 目标文件
    target_file = "/pcpt/pcpt/project/liuyibo/multimodal_bevfusion/mmdet3d/models/heads/lane/bev_lane_heatmap_head.py"
    
    if not os.path.exists(target_file):
        print(f"❌ 文件不存在: {target_file}")
        return None
    
    print(f"📁 分析文件: {target_file}")
    
    # 读取文件内容
    with open(target_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有print语句
    print_patterns = [
        r'print\(f?"\[BEV_HEAD_INIT\].*?"\)',
        r'print\(f?"\[LANE_HEAD_DEBUG\].*?"\)',
        r'print\(f?"\[DIMENSION_ERROR\].*?"\)',
        r'print\(f?"\[LOSS_COMPUTE_DEBUG\].*?"\)',
        r'print\(f?"\[DEBUG_TRANSFORM\].*?"\)',
        r'print\(f?".*?"\)',  # 其他print语句
        r'print\([^)]+\)'     # 所有print语句
    ]
    
    print_statements = []
    
    for pattern in print_patterns:
        matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
        for match in matches:
            line_num = content[:match.start()].count('\n') + 1
            statement = match.group().strip()
            
            # 避免重复
            if not any(stmt['statement'] == statement for stmt in print_statements):
                print_statements.append({
                    'line': line_num,
                    'statement': statement,
                    'category': _categorize_print_statement(statement)
                })
    
    # 按行号排序
    print_statements.sort(key=lambda x: x['line'])
    
    print(f"\n📊 发现 {len(print_statements)} 个print语句:")
    
    # 按类别分组
    categories = {}
    for stmt in print_statements:
        cat = stmt['category']
        if cat not in categories:
            categories[cat] = []
        categories[cat].append(stmt)
    
    for category, statements in categories.items():
        print(f"\n🏷️ {category} ({len(statements)} 个):")
        for stmt in statements[:5]:  # 只显示前5个
            print(f"   第{stmt['line']}行: {stmt['statement'][:80]}...")
        if len(statements) > 5:
            print(f"   ... 还有 {len(statements) - 5} 个")
    
    return print_statements, content

def _categorize_print_statement(statement):
    """
    对print语句进行分类
    """
    if '[BEV_HEAD_INIT]' in statement:
        return '初始化调试'
    elif '[LANE_HEAD_DEBUG]' in statement:
        return '车道头调试'
    elif '[DIMENSION_ERROR]' in statement:
        return '维度错误'
    elif '[LOSS_COMPUTE_DEBUG]' in statement:
        return '损失计算调试'
    elif '[DEBUG_TRANSFORM]' in statement:
        return '数据转换调试'
    else:
        return '其他调试'

def generate_logging_replacement():
    """
    生成logging替换方案
    """
    print("\n" + "=" * 80)
    print("生成Logging替换方案")
    print("=" * 80)
    
    replacement_rules = [
        {
            'pattern': r'print\(f?"\[BEV_HEAD_INIT\](.*?)"\)',
            'replacement': r'logger.info(f"[BEV_HEAD_INIT]\1")',
            'level': 'INFO',
            'description': '初始化信息 - 重要的配置信息，使用INFO级别'
        },
        {
            'pattern': r'print\(f?"\[DIMENSION_ERROR\](.*?)"\)',
            'replacement': r'logger.error(f"[DIMENSION_ERROR]\1")',
            'level': 'ERROR',
            'description': '维度错误 - 严重错误，使用ERROR级别'
        },
        {
            'pattern': r'print\(f?"\[LANE_HEAD_DEBUG\](.*?)"\)',
            'replacement': r'logger.debug(f"[LANE_HEAD_DEBUG]\1")',
            'level': 'DEBUG',
            'description': '车道头调试 - 详细调试信息，使用DEBUG级别'
        },
        {
            'pattern': r'print\(f?"\[LOSS_COMPUTE_DEBUG\](.*?)"\)',
            'replacement': r'logger.debug(f"[LOSS_COMPUTE_DEBUG]\1")',
            'level': 'DEBUG',
            'description': '损失计算调试 - 详细调试信息，使用DEBUG级别'
        },
        {
            'pattern': r'print\(f?"\[DEBUG_TRANSFORM\](.*?)"\)',
            'replacement': r'logger.debug(f"[DEBUG_TRANSFORM]\1")',
            'level': 'DEBUG',
            'description': '数据转换调试 - 详细调试信息，使用DEBUG级别'
        },
        {
            'pattern': r'print\(f?"(.*?)"\)',
            'replacement': r'logger.debug(f"\1")',
            'level': 'DEBUG',
            'description': '其他print语句 - 默认使用DEBUG级别'
        }
    ]
    
    print("📋 替换规则:")
    for i, rule in enumerate(replacement_rules, 1):
        print(f"\n{i}. {rule['description']}")
        print(f"   模式: {rule['pattern']}")
        print(f"   替换: {rule['replacement']}")
        print(f"   级别: {rule['level']}")
    
    return replacement_rules

def create_logging_configuration():
    """
    创建logging配置代码
    """
    print("\n" + "=" * 80)
    print("创建Logging配置")
    print("=" * 80)
    
    logging_config = '''
# === Logging Configuration ===
import logging

# 创建logger
logger = logging.getLogger(__name__)

# 配置日志级别（可通过环境变量控制）
import os
log_level = os.getenv('BEV_LANE_LOG_LEVEL', 'WARNING').upper()
logger.setLevel(getattr(logging, log_level, logging.WARNING))

# 如果还没有handler，添加一个
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# 防止重复日志
logger.propagate = False
'''
    
    print("📝 生成的logging配置:")
    print(logging_config)
    
    return logging_config

def apply_logging_optimization(print_statements, content, replacement_rules, logging_config):
    """
    应用logging优化
    """
    print("\n" + "=" * 80)
    print("应用Logging优化")
    print("=" * 80)
    
    # 在文件开头添加logging配置
    # 找到import语句的结束位置
    import_end = content.find('from mmdet.models.builder import LOSSES')
    if import_end != -1:
        import_end = content.find('\n', import_end) + 1
    else:
        # 如果找不到，在第一个类定义之前插入
        import_end = content.find('@HEADS.register_module()')
    
    if import_end == -1:
        print("❌ 无法找到合适的插入位置")
        return None
    
    # 插入logging配置
    new_content = content[:import_end] + '\n' + logging_config + '\n' + content[import_end:]
    
    # 应用替换规则
    replacements_made = 0
    for rule in replacement_rules:
        pattern = rule['pattern']
        replacement = rule['replacement']
        
        # 计算替换次数
        matches = re.findall(pattern, new_content, re.MULTILINE | re.DOTALL)
        count = len(matches)
        
        if count > 0:
            new_content = re.sub(pattern, replacement, new_content, flags=re.MULTILINE | re.DOTALL)
            replacements_made += count
            print(f"✅ 应用规则 '{rule['description']}': {count} 次替换")
    
    print(f"\n📊 总计进行了 {replacements_made} 次替换")
    
    return new_content

def create_optimized_file(original_content, optimized_content):
    """
    创建优化后的文件
    """
    print("\n" + "=" * 80)
    print("创建优化文件")
    print("=" * 80)
    
    # 原文件路径
    original_file = "/pcpt/pcpt/project/liuyibo/multimodal_bevfusion/mmdet3d/models/heads/lane/bev_lane_heatmap_head.py"
    
    # 备份原文件
    backup_file = original_file + ".backup_before_logging_optimization"
    
    try:
        # 创建备份
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(original_content)
        print(f"✅ 创建备份文件: {backup_file}")
        
        # 写入优化后的内容
        with open(original_file, 'w', encoding='utf-8') as f:
            f.write(optimized_content)
        print(f"✅ 写入优化文件: {original_file}")
        
        # 统计信息
        original_lines = original_content.count('\n')
        optimized_lines = optimized_content.count('\n')
        
        print(f"\n📊 文件统计:")
        print(f"   原文件行数: {original_lines}")
        print(f"   优化后行数: {optimized_lines}")
        print(f"   行数变化: {optimized_lines - original_lines:+d}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")
        return False

def create_usage_guide():
    """
    创建使用指南
    """
    print("\n" + "=" * 80)
    print("使用指南")
    print("=" * 80)
    
    guide = '''
📖 优化后的日志系统使用指南:

1. 日志级别控制:
   - 通过环境变量 BEV_LANE_LOG_LEVEL 控制日志级别
   - 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
   - 默认值: WARNING (生产环境推荐)

2. 使用示例:
   # 开发调试时 - 显示所有调试信息
   export BEV_LANE_LOG_LEVEL=DEBUG
   
   # 生产环境 - 只显示警告和错误
   export BEV_LANE_LOG_LEVEL=WARNING
   
   # 完全静默 - 只显示严重错误
   export BEV_LANE_LOG_LEVEL=ERROR

3. 日志级别说明:
   - DEBUG: 详细的调试信息（开发时使用）
   - INFO: 重要的配置和状态信息
   - WARNING: 警告信息（默认级别）
   - ERROR: 错误信息
   - CRITICAL: 严重错误

4. 性能优化:
   - 生产环境建议使用 WARNING 或更高级别
   - DEBUG 级别会输出大量信息，影响性能
   - 日志格式包含时间戳，便于调试

5. 代码变更:
   - 所有 print() 语句已替换为适当的 logger 调用
   - 维度错误使用 logger.error()
   - 初始化信息使用 logger.info()
   - 调试信息使用 logger.debug()
'''
    
    print(guide)
    
    # 保存使用指南到文件
    guide_file = "/pcpt/pcpt/project/liuyibo/multimodal_bevfusion/LOGGING_OPTIMIZATION_GUIDE.md"
    try:
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write("# BEV Lane Heatmap Head 日志系统优化指南\n\n")
            f.write(guide)
        print(f"\n✅ 使用指南已保存到: {guide_file}")
    except Exception as e:
        print(f"\n❌ 保存使用指南失败: {e}")

def main():
    """
    主函数
    """
    print("🔧 开始优化调试日志系统...")
    
    try:
        # 1. 分析现有的print语句
        result = analyze_print_statements()
        if result is None:
            return False
        
        print_statements, original_content = result
        
        # 2. 生成替换规则
        replacement_rules = generate_logging_replacement()
        
        # 3. 创建logging配置
        logging_config = create_logging_configuration()
        
        # 4. 应用优化
        optimized_content = apply_logging_optimization(
            print_statements, original_content, replacement_rules, logging_config
        )
        
        if optimized_content is None:
            return False
        
        # 5. 创建优化后的文件
        success = create_optimized_file(original_content, optimized_content)
        
        if not success:
            return False
        
        # 6. 创建使用指南
        create_usage_guide()
        
        print("\n✅ 调试日志系统优化完成!")
        print("\n📋 优化总结:")
        print(f"   - 分析了 {len(print_statements)} 个print语句")
        print(f"   - 应用了 {len(replacement_rules)} 个替换规则")
        print("   - 添加了logging配置和级别控制")
        print("   - 创建了备份文件和使用指南")
        print("\n🎯 建议下一步:")
        print("   1. 测试优化后的代码")
        print("   2. 验证不同日志级别的效果")
        print("   3. 在生产环境中设置适当的日志级别")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 优化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)