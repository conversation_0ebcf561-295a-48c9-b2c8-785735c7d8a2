#!/usr/bin/env python3
"""
重构重复代码

这个脚本分析BEV Lane Heatmap Head中的重复代码，
特别是重复的squeeze(1)操作，并将其封装为辅助函数，
减少代码冗余，提高可维护性。

主要功能：
1. 识别重复的squeeze(1)操作
2. 创建_squeeze_spatial()辅助函数
3. 重构其他重复的代码模式
4. 提高代码的可读性和维护性
"""

import os
import re
import sys
from pathlib import Path

def analyze_duplicate_patterns():
    """
    分析BEV Lane Heatmap Head中的重复代码模式
    """
    print("=" * 80)
    print("重复代码模式分析")
    print("=" * 80)
    
    # 目标文件
    target_file = "/pcpt/pcpt/project/liuyibo/multimodal_bevfusion/mmdet3d/models/heads/lane/bev_lane_heatmap_head.py"
    
    if not os.path.exists(target_file):
        print(f"❌ 文件不存在: {target_file}")
        return None
    
    print(f"📁 分析文件: {target_file}")
    
    # 读取文件内容
    with open(target_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 分析squeeze(1)操作
    print("\n" + "=" * 60)
    print("1. SQUEEZE(1)操作分析")
    print("=" * 60)
    
    squeeze_patterns = [
        r'(\w+)_squeezed\s*=\s*(\w+)\.squeeze\(1\)',
        r'(\w+)\s*=\s*(\w+)\.squeeze\(1\)',
    ]
    
    squeeze_operations = []
    
    for pattern in squeeze_patterns:
        matches = re.finditer(pattern, content)
        for match in matches:
            line_num = content[:match.start()].count('\n') + 1
            var_name = match.group(1)
            source_var = match.group(2)
            full_statement = match.group(0)
            
            squeeze_operations.append({
                'line': line_num,
                'var_name': var_name,
                'source_var': source_var,
                'statement': full_statement
            })
    
    print(f"\n📊 发现 {len(squeeze_operations)} 个squeeze(1)操作:")
    for op in squeeze_operations:
        print(f"   第{op['line']}行: {op['statement']}")
    
    # 2. 分析ensure_batch_dim模式
    print("\n" + "=" * 60)
    print("2. ENSURE_BATCH_DIM模式分析")
    print("=" * 60)
    
    batch_dim_pattern = r'(\w+)\s*=\s*ensure_batch_dim\((\w+),\s*(\w+)\)'
    batch_dim_operations = []
    
    matches = re.finditer(batch_dim_pattern, content)
    for match in matches:
        line_num = content[:match.start()].count('\n') + 1
        var_name = match.group(1)
        source_var = match.group(2)
        batch_size = match.group(3)
        
        batch_dim_operations.append({
            'line': line_num,
            'var_name': var_name,
            'source_var': source_var,
            'batch_size': batch_size,
            'statement': match.group(0)
        })
    
    print(f"\n📊 发现 {len(batch_dim_operations)} 个ensure_batch_dim操作:")
    for op in batch_dim_operations:
        print(f"   第{op['line']}行: {op['statement']}")
    
    # 3. 分析重复的维度检查模式
    print("\n" + "=" * 60)
    print("3. 维度检查模式分析")
    print("=" * 60)
    
    dimension_check_patterns = [
        r'if\s+(\w+)\.shape\s*!=\s*(\w+)\.shape:',
        r'logger\.error\(f?"\[DIMENSION_ERROR\].*?"\)',
    ]
    
    dimension_checks = []
    
    for pattern in dimension_check_patterns:
        matches = re.finditer(pattern, content)
        for match in matches:
            line_num = content[:match.start()].count('\n') + 1
            dimension_checks.append({
                'line': line_num,
                'pattern': pattern,
                'statement': match.group(0)
            })
    
    print(f"\n📊 发现 {len(dimension_checks)} 个维度检查操作:")
    for check in dimension_checks[:5]:  # 只显示前5个
        print(f"   第{check['line']}行: {check['statement'][:60]}...")
    
    return {
        'content': content,
        'squeeze_operations': squeeze_operations,
        'batch_dim_operations': batch_dim_operations,
        'dimension_checks': dimension_checks
    }

def design_helper_functions():
    """
    设计辅助函数来减少重复代码
    """
    print("\n" + "=" * 80)
    print("设计辅助函数")
    print("=" * 80)
    
    helper_functions = {
        '_squeeze_spatial': {
            'description': '封装squeeze(1)操作，将[B, 1, H, W]转换为[B, H, W]',
            'code': '''
    def _squeeze_spatial(self, tensor, tensor_name="tensor"):
        """
        安全地执行spatial squeeze操作，将[B, 1, H, W]转换为[B, H, W]
        
        Args:
            tensor (torch.Tensor): 输入张量，期望形状为[B, 1, H, W]
            tensor_name (str): 张量名称，用于调试信息
            
        Returns:
            torch.Tensor: 压缩后的张量，形状为[B, H, W]
        """
        if tensor is None:
            logger.warning(f"[SQUEEZE_SPATIAL] {tensor_name} is None")
            return None
            
        original_shape = tensor.shape
        
        # 检查维度是否符合预期
        if len(original_shape) != 4:
            logger.error(f"[SQUEEZE_SPATIAL] {tensor_name} expected 4D tensor, got {len(original_shape)}D: {original_shape}")
            raise ValueError(f"Expected 4D tensor for {tensor_name}, got {original_shape}")
            
        if original_shape[1] != 1:
            logger.error(f"[SQUEEZE_SPATIAL] {tensor_name} expected dim 1 to be 1, got {original_shape[1]}: {original_shape}")
            raise ValueError(f"Expected dim 1 to be 1 for {tensor_name}, got {original_shape}")
        
        # 执行squeeze操作
        squeezed = tensor.squeeze(1)
        
        logger.debug(f"[SQUEEZE_SPATIAL] {tensor_name}: {original_shape} -> {squeezed.shape}")
        
        return squeezed''',
            'usage_examples': [
                'heatmap_pred_squeezed = self._squeeze_spatial(heatmap_pred, "heatmap_pred")',
                'gt_heatmap_squeezed = self._squeeze_spatial(gt_heatmap, "gt_heatmap")',
                'offset_pred_squeezed = self._squeeze_spatial(offset_pred, "offset_pred")',
            ]
        },
        
        '_ensure_batch_dimension': {
            'description': '统一的批次维度确保函数',
            'code': '''
    def _ensure_batch_dimension(self, tensor, target_batch_size, tensor_name="tensor"):
        """
        确保张量具有正确的批次维度
        
        Args:
            tensor (torch.Tensor): 输入张量
            target_batch_size (int): 目标批次大小
            tensor_name (str): 张量名称，用于调试信息
            
        Returns:
            torch.Tensor: 具有正确批次维度的张量
        """
        if tensor is None:
            logger.warning(f"[ENSURE_BATCH_DIM] {tensor_name} is None")
            return None
            
        original_shape = tensor.shape
        
        # 如果没有批次维度，添加一个
        if len(original_shape) == 3:  # [H, W, C] -> [1, H, W, C]
            tensor = tensor.unsqueeze(0)
            logger.debug(f"[ENSURE_BATCH_DIM] {tensor_name}: Added batch dim {original_shape} -> {tensor.shape}")
        
        current_batch_size = tensor.shape[0]
        
        # 如果批次大小不匹配，扩展到目标大小
        if current_batch_size != target_batch_size:
            if current_batch_size == 1:
                # 扩展单个样本到目标批次大小
                tensor = tensor.expand(target_batch_size, *tensor.shape[1:])
                logger.debug(f"[ENSURE_BATCH_DIM] {tensor_name}: Expanded batch {original_shape} -> {tensor.shape}")
            else:
                logger.error(f"[ENSURE_BATCH_DIM] {tensor_name} batch size mismatch: got {current_batch_size}, expected {target_batch_size}")
                raise ValueError(f"Batch size mismatch for {tensor_name}: got {current_batch_size}, expected {target_batch_size}")
        
        return tensor''',
            'usage_examples': [
                'gt_heatmap = self._ensure_batch_dimension(gt_heatmap, target_batch_size, "gt_heatmap")',
                'gt_offset = self._ensure_batch_dimension(gt_offset, target_batch_size, "gt_offset")',
            ]
        },
        
        '_validate_tensor_dimensions': {
            'description': '统一的张量维度验证函数',
            'code': '''
    def _validate_tensor_dimensions(self, pred_tensor, gt_tensor, tensor_type="tensor"):
        """
        验证预测张量和真值张量的维度是否匹配
        
        Args:
            pred_tensor (torch.Tensor): 预测张量
            gt_tensor (torch.Tensor): 真值张量
            tensor_type (str): 张量类型，用于错误信息
            
        Returns:
            bool: 维度是否匹配
            
        Raises:
            ValueError: 当维度不匹配时
        """
        if pred_tensor is None or gt_tensor is None:
            logger.error(f"[VALIDATE_DIMS] {tensor_type} contains None tensor")
            raise ValueError(f"None tensor found in {tensor_type} validation")
        
        pred_shape = pred_tensor.shape
        gt_shape = gt_tensor.shape
        
        if pred_shape != gt_shape:
            logger.error(f"[VALIDATE_DIMS] {tensor_type} dimension mismatch!")
            logger.error(f"[VALIDATE_DIMS]   Prediction: {pred_shape}")
            logger.error(f"[VALIDATE_DIMS]   Ground truth: {gt_shape}")
            logger.error(f"[VALIDATE_DIMS]   Difference: H={pred_shape[-2]} vs {gt_shape[-2]}, W={pred_shape[-1]} vs {gt_shape[-1]}")
            
            raise ValueError(f"Dimension mismatch for {tensor_type}: pred {pred_shape} vs gt {gt_shape}")
        
        logger.debug(f"[VALIDATE_DIMS] {tensor_type} dimensions match: {pred_shape}")
        return True''',
            'usage_examples': [
                'self._validate_tensor_dimensions(heatmap_pred_squeezed, gt_heatmap_squeezed, "heatmap")',
                'self._validate_tensor_dimensions(offset_pred_squeezed, gt_offset_squeezed, "offset")',
            ]
        }
    }
    
    print("📋 设计的辅助函数:")
    for func_name, func_info in helper_functions.items():
        print(f"\n🔧 {func_name}:")
        print(f"   描述: {func_info['description']}")
        print(f"   使用示例:")
        for example in func_info['usage_examples']:
            print(f"     - {example}")
    
    return helper_functions

def generate_refactored_code(analysis_result, helper_functions):
    """
    生成重构后的代码
    """
    print("\n" + "=" * 80)
    print("生成重构代码")
    print("=" * 80)
    
    content = analysis_result['content']
    squeeze_operations = analysis_result['squeeze_operations']
    
    # 1. 在类中添加辅助函数
    # 找到类定义的位置
    class_start = content.find('class BEVLaneHeatmapHead(BaseModule):')
    if class_start == -1:
        print("❌ 无法找到类定义")
        return None
    
    # 找到__init__方法的结束位置
    init_start = content.find('def __init__(', class_start)
    if init_start == -1:
        print("❌ 无法找到__init__方法")
        return None
    
    # 找到下一个方法定义的位置
    next_method = content.find('\n    def ', init_start + 1)
    if next_method == -1:
        # 如果没有找到下一个方法，在类的末尾添加
        next_method = content.find('\n\n', init_start + 1000)  # 大概位置
    
    # 插入辅助函数
    helper_code = '\n\n    # === 辅助函数 ===\n'
    for func_name, func_info in helper_functions.items():
        helper_code += func_info['code'] + '\n\n'
    
    new_content = content[:next_method] + helper_code + content[next_method:]
    
    # 2. 替换squeeze操作
    print("\n📝 替换squeeze操作:")
    replacements_made = 0
    
    # 定义替换规则
    squeeze_replacements = [
        {
            'pattern': r'(\w+_squeezed)\s*=\s*(\w+)\.squeeze\(1\)',
            'replacement': lambda m: f'{m.group(1)} = self._squeeze_spatial({m.group(2)}, "{m.group(2)}")',
            'description': '替换带_squeezed后缀的squeeze操作'
        },
        {
            'pattern': r'(heatmap_pred_squeezed)\s*=\s*(heatmap_pred)\.squeeze\(1\)',
            'replacement': r'\1 = self._squeeze_spatial(\2, "\2")',
            'description': '替换热图预测的squeeze操作'
        }
    ]
    
    for replacement in squeeze_replacements:
        pattern = replacement['pattern']
        repl = replacement['replacement']
        
        if callable(repl):
            # 使用函数替换
            matches = list(re.finditer(pattern, new_content))
            for match in reversed(matches):  # 从后往前替换，避免位置偏移
                new_text = repl(match)
                new_content = new_content[:match.start()] + new_text + new_content[match.end():]
                replacements_made += 1
        else:
            # 使用字符串替换
            count = len(re.findall(pattern, new_content))
            new_content = re.sub(pattern, repl, new_content)
            replacements_made += count
        
        print(f"   ✅ {replacement['description']}: 替换了相关操作")
    
    # 3. 替换ensure_batch_dim操作
    print("\n📝 替换ensure_batch_dim操作:")
    
    batch_dim_pattern = r'(\w+)\s*=\s*ensure_batch_dim\((\w+),\s*(\w+)\)'
    batch_dim_replacement = r'\1 = self._ensure_batch_dimension(\2, \3, "\2")'
    
    batch_count = len(re.findall(batch_dim_pattern, new_content))
    new_content = re.sub(batch_dim_pattern, batch_dim_replacement, new_content)
    replacements_made += batch_count
    
    print(f"   ✅ 替换ensure_batch_dim操作: {batch_count} 次")
    
    # 4. 添加维度验证调用
    print("\n📝 添加维度验证:")
    
    # 在维度检查之前添加验证调用
    dimension_check_pattern = r'if\s+(\w+)\.shape\s*!=\s*(\w+)\.shape:'
    
    def add_validation(match):
        tensor1 = match.group(1)
        tensor2 = match.group(2)
        # 推断张量类型
        if 'heatmap' in tensor1.lower():
            tensor_type = 'heatmap'
        elif 'offset' in tensor1.lower():
            tensor_type = 'offset'
        elif 'height' in tensor1.lower() or 'z' in tensor1.lower():
            tensor_type = 'height'
        else:
            tensor_type = 'tensor'
        
        return f'self._validate_tensor_dimensions({tensor1}, {tensor2}, "{tensor_type}")'
    
    validation_matches = list(re.finditer(dimension_check_pattern, new_content))
    for match in reversed(validation_matches):
        validation_call = add_validation(match)
        # 替换整个if语句
        new_content = new_content[:match.start()] + validation_call + new_content[match.end():]
        replacements_made += 1
    
    print(f"   ✅ 添加维度验证: {len(validation_matches)} 次")
    
    print(f"\n📊 总计进行了 {replacements_made} 次重构")
    
    return new_content

def create_refactored_file(original_content, refactored_content):
    """
    创建重构后的文件
    """
    print("\n" + "=" * 80)
    print("创建重构文件")
    print("=" * 80)
    
    # 原文件路径
    original_file = "/pcpt/pcpt/project/liuyibo/multimodal_bevfusion/mmdet3d/models/heads/lane/bev_lane_heatmap_head.py"
    
    # 备份原文件
    backup_file = original_file + ".backup_before_refactoring"
    
    try:
        # 创建备份
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(original_content)
        print(f"✅ 创建备份文件: {backup_file}")
        
        # 写入重构后的内容
        with open(original_file, 'w', encoding='utf-8') as f:
            f.write(refactored_content)
        print(f"✅ 写入重构文件: {original_file}")
        
        # 统计信息
        original_lines = original_content.count('\n')
        refactored_lines = refactored_content.count('\n')
        
        print(f"\n📊 文件统计:")
        print(f"   原文件行数: {original_lines}")
        print(f"   重构后行数: {refactored_lines}")
        print(f"   行数变化: {refactored_lines - original_lines:+d}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")
        return False

def create_refactoring_summary():
    """
    创建重构总结
    """
    print("\n" + "=" * 80)
    print("重构总结")
    print("=" * 80)
    
    summary = '''
📖 代码重构总结:

1. 新增辅助函数:
   - _squeeze_spatial(): 安全的spatial squeeze操作
   - _ensure_batch_dimension(): 统一的批次维度确保
   - _validate_tensor_dimensions(): 统一的张量维度验证

2. 重构效果:
   - 减少了重复的squeeze(1)操作代码
   - 统一了批次维度处理逻辑
   - 改进了维度验证和错误处理
   - 提高了代码的可读性和维护性

3. 代码质量提升:
   - 更好的错误信息和调试支持
   - 统一的命名约定和参数处理
   - 更清晰的函数职责分离
   - 更容易进行单元测试

4. 性能优化:
   - 减少了重复的维度检查代码
   - 统一的错误处理避免了重复的异常创建
   - 更高效的调试信息输出

5. 维护性改进:
   - 集中的辅助函数便于修改和扩展
   - 统一的接口减少了代码耦合
   - 更好的文档和类型提示
'''
    
    print(summary)
    
    # 保存重构总结到文件
    summary_file = "/pcpt/pcpt/project/liuyibo/multimodal_bevfusion/REFACTORING_SUMMARY.md"
    try:
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("# BEV Lane Heatmap Head 代码重构总结\n\n")
            f.write(summary)
        print(f"\n✅ 重构总结已保存到: {summary_file}")
    except Exception as e:
        print(f"\n❌ 保存重构总结失败: {e}")

def main():
    """
    主函数
    """
    print("🔧 开始重构重复代码...")
    
    try:
        # 1. 分析重复代码模式
        analysis_result = analyze_duplicate_patterns()
        if analysis_result is None:
            return False
        
        # 2. 设计辅助函数
        helper_functions = design_helper_functions()
        
        # 3. 生成重构后的代码
        refactored_content = generate_refactored_code(analysis_result, helper_functions)
        
        if refactored_content is None:
            return False
        
        # 4. 创建重构后的文件
        success = create_refactored_file(analysis_result['content'], refactored_content)
        
        if not success:
            return False
        
        # 5. 创建重构总结
        create_refactoring_summary()
        
        print("\n✅ 代码重构完成!")
        print("\n📋 重构总结:")
        print(f"   - 分析了 {len(analysis_result['squeeze_operations'])} 个squeeze操作")
        print(f"   - 分析了 {len(analysis_result['batch_dim_operations'])} 个batch_dim操作")
        print(f"   - 添加了 {len(helper_functions)} 个辅助函数")
        print("   - 提高了代码的可读性和维护性")
        print("\n🎯 建议下一步:")
        print("   1. 测试重构后的代码")
        print("   2. 验证功能的正确性")
        print("   3. 检查性能是否有改善")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 重构过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)