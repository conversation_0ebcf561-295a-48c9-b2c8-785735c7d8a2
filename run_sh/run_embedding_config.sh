#!/bin/bash
# Training script for BEVFusion 3D Lane Detection with Embedding Feature
# This script uses the new embedding configuration with background category and optimized radius processing

# Kill existing training processes (uncomment if needed)
# ps -ef|grep python |grep -v grep|awk '{print $2}'|xargs kill -9

# Set environment variables
export CUDA_VISIBLE_DEVICES=0,1
export PYTHONPATH="${PWD}:$PYTHONPATH"

# Training command with distributed training support
bash tools/dist_train.sh \
    configs/lane/bevfusion_lane_detection_embadding_config.yaml \
    2 \
    --work-dir work_dirs/bevfusion_embedding_experiment \
    --seed 42 \
    --deterministic

# Alternative single GPU training (uncomment if needed)
# python tools/train.py \
#     configs/lane/bevfusion_lane_detection_embadding_config.yaml \
#     --work-dir work_dirs/bevfusion_embedding_experiment \
#     --seed 42 \
#     --deterministic

echo "Training completed. Check work_dirs/bevfusion_embedding_experiment for results."