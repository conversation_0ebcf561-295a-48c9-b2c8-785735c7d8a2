function SEND_MSG(){
if [ $? -ne 0 ]; then
    result=$(curl -s http://quan.suning.com/getSysTime.do)
    datetime=${result:13:19}
    msg="<font color='warning'>$1 Failed.</font> \nContainer: jjy_4GPU \n<font color='comment'>$datetime</font>"
else
    result=$(curl -s http://quan.suning.com/getSysTime.do)
    datetime=${result:13:19}
    msg="$1 .  \nContainer: jjy_4GPU \n<font color='comment'>$datetime</font>"
fi
a=$(curl -s 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bea27727-0a86-4b37-8efe-952842068233' \
   -H 'Content-Type: application/json' \
   -d '
   {
        "msgtype": "markdown",
        "markdown": {
            "content": "'"$msg"'"
         }
   }')
}

function CLEAN_GPU(){
  echo "Cleaning Nvidia Occupied."
  ps -ef|grep python |grep -v grep|awk '{print $2}'|xargs kill -9
  sleep 1
  nvidia-smi | grep MiB
  sleep 1
}

CLEAN_GPU
MSG1="V4 bev SENetFuser e3s0"
# ps -ef|grep python |grep -v grep|awk '{print $2}'|xargs kill -9
SEND_MSG "Start Run ${MSG1}"
bash tools/dist_train.sh\
    configs/mogo/falcon_v4_77baseline_SENetFuser_epoch30.yaml\
    4 1 0 10.0.12.40
# bash tools/dist_train.sh configs/mogo/falcon_v4_77baseline_SENetFuser_epoch30.yaml 4 1 0 10.0.12.40
SEND_MSG "TAD Run ${MSG1} "
sleep 30

SEND_MSG "Occupying GPU \n${MSG1}"
python /rbs/jiangjunyuan/tools/gpu2.py --size 20000 --gpus 1 --interval 0.01

