#!/usr/bin/env python3
"""Test script to validate the 7-camera lane detection configuration."""

import os
import sys
import torch
from mmcv import Config
from mmdet3d.models import build_model
from mmdet3d.datasets import build_dataset

def test_7_camera_config():
    """Test loading and instantiating the model with 7-camera setup."""
    
    # Load configuration
    config_path = 'configs/lane/config_3d_lane_detection_task_based_bevfusion.yaml'
    print(f"Loading configuration from: {config_path}")
    
    try:
        cfg = Config.fromfile(config_path)
        print("✓ Configuration loaded successfully")
    except Exception as e:
        print(f"✗ Failed to load configuration: {e}")
        return False
    
    # Print key configuration details
    print("\nConfiguration Summary:")
    print(f"  - Model type: {cfg.model.type}")
    print(f"  - Camera list: {cfg.cam_list}")
    print(f"  - Camera count: {cfg.camera_num}")
    print(f"  - Expected cameras: {len(cfg.cam_list)}")
    print(f"  - Image size: {cfg.image_size}")
    print(f"  - Point cloud range: {cfg.point_cloud_range}")
    
    # Check camera augmentation settings
    print(f"  - Resize train: {len(cfg.augment2d.resize_train)} settings")
    print(f"  - Resize test: {len(cfg.augment2d.resize_test)} settings")
    print(f"  - Bot crop train: {len(cfg.augment2d.bot_pct_lim_train)} settings")
    print(f"  - Bot crop test: {len(cfg.augment2d.bot_pct_lim_test)} settings")
    
    # Validate camera settings consistency
    cam_count = len(cfg.cam_list)
    if cam_count != cfg.camera_num:
        print(f"✗ Camera count mismatch: cam_list has {cam_count}, camera_num is {cfg.camera_num}")
        return False
    
    if len(cfg.augment2d.resize_train) != cam_count:
        print(f"✗ Resize train settings mismatch: expected {cam_count}, got {len(cfg.augment2d.resize_train)}")
        return False
        
    if len(cfg.augment2d.resize_test) != cam_count:
        print(f"✗ Resize test settings mismatch: expected {cam_count}, got {len(cfg.augment2d.resize_test)}")
        return False
        
    if len(cfg.augment2d.bot_pct_lim_train) != cam_count:
        print(f"✗ Bot crop train settings mismatch: expected {cam_count}, got {len(cfg.augment2d.bot_pct_lim_train)}")
        return False
        
    if len(cfg.augment2d.bot_pct_lim_test) != cam_count:
        print(f"✗ Bot crop test settings mismatch: expected {cam_count}, got {len(cfg.augment2d.bot_pct_lim_test)}")
        return False
    
    print("✓ Camera settings consistency validated")
    
    # Test model instantiation
    print("\nTesting model instantiation...")
    try:
        model = build_model(cfg.model, train_cfg=None, test_cfg=None)
        print("✓ Model instantiated successfully")
        
        # Count model parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"  - Total parameters: {total_params:,}")
        print(f"  - Trainable parameters: {trainable_params:,}")
        
    except Exception as e:
        print(f"✗ Failed to instantiate model: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test dataset instantiation (optional, may fail if data path doesn't exist)
    print("\nTesting dataset instantiation...")
    try:
        train_dataset = build_dataset(cfg.data.train)
        print(f"✓ Train dataset instantiated successfully")
        print(f"  - Dataset type: {type(train_dataset).__name__}")
        print(f"  - Camera list: {train_dataset.cam_list}")
        print(f"  - Dataset root: {train_dataset.dataset_root}")
        
    except Exception as e:
        print(f"ⓘ Dataset instantiation failed (expected if data path doesn't exist): {e}")
    
    # Test forward pass with dummy data
    print("\nTesting model forward pass with dummy data...")
    try:
        model.eval()
        model.cuda()
        
        batch_size = 1
        num_cameras = cfg.camera_num
        img_height, img_width = cfg.image_size
        
        # Create dummy inputs
        dummy_img = torch.randn(batch_size, num_cameras, 3, img_height, img_width).cuda()
        dummy_points = [torch.randn(1000, 4).cuda() for _ in range(batch_size)]
        
        # Create dummy transformation matrices
        dummy_transform = torch.eye(4).unsqueeze(0).repeat(batch_size, num_cameras, 1, 1).cuda()
        dummy_lidar2ego = torch.eye(4).unsqueeze(0).repeat(batch_size, 1, 1).cuda()
        dummy_intrinsics = torch.eye(4).unsqueeze(0).repeat(batch_size, num_cameras, 1, 1).cuda()
        
        # Create dummy metadata
        metas = [{'sample_idx': i} for i in range(batch_size)]
        
        with torch.no_grad():
            outputs = model(
                img=dummy_img,
                points=dummy_points,
                camera2ego=dummy_transform,
                lidar2ego=dummy_lidar2ego,
                lidar2camera=dummy_transform,
                lidar2image=dummy_transform,
                camera_intrinsics=dummy_intrinsics,
                camera2lidar=dummy_transform,
                img_aug_matrix=dummy_transform,
                lidar_aug_matrix=dummy_lidar2ego,
                metas=metas,
            )
        
        print("✓ Forward pass successful")
        print(f"  - Output type: {type(outputs)}")
        if isinstance(outputs, list):
            print(f"  - Output length: {len(outputs)}")
        
    except Exception as e:
        print(f"✗ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "="*50)
    print("✓ All tests passed! 7-camera configuration is working correctly.")
    print("="*50)
    return True

if __name__ == "__main__":
    test_7_camera_config() 