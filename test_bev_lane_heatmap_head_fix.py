#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试BEVLaneHeatmapHead坐标系统修复的正确性

主要验证内容：
1. 张量索引顺序的正确性 [batch, channel, height_idx, width_idx]
2. 坐标转换逻辑的一致性
3. 网格坐标到世界坐标的映射关系
4. 特征提取的张量索引正确性
"""

import torch
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append('/turbo/pcpt/project/liuyibo/multimodal_bevfusion')

from mmdet3d.models.heads.lane.bev_lane_heatmap_head import BEVLaneHeatmapHead

def test_coordinate_system_consistency():
    """
    测试坐标系统的一致性
    验证网格坐标到世界坐标的转换是否正确
    """
    print("\n=== 测试坐标系统一致性 ===")
    
    # 模拟BEV配置参数
    bev_h, bev_w = 200, 200  # BEV网格尺寸
    x_min, x_max = -50.0, 50.0  # X轴范围
    y_min, y_max = -50.0, 50.0  # Y轴范围
    
    x_res = (x_max - x_min) / bev_w  # X轴分辨率
    y_res = (y_max - y_min) / bev_h  # Y轴分辨率
    
    print(f"BEV网格尺寸: {bev_h} x {bev_w}")
    print(f"世界坐标范围: X[{x_min}, {x_max}], Y[{y_min}, {y_max}]")
    print(f"分辨率: X={x_res:.3f}, Y={y_res:.3f}")
    
    # 测试几个关键点的坐标转换
    test_points = [
        (0, 0),           # 左上角
        (bev_w-1, 0),     # 右上角
        (0, bev_h-1),     # 左下角
        (bev_w-1, bev_h-1), # 右下角
        (bev_w//2, bev_h//2), # 中心点
    ]
    
    print("\n网格坐标 -> 世界坐标转换测试:")
    for xs_grid, ys_grid in test_points:
        # 按照修复后的逻辑进行坐标转换
        world_x = xs_grid * x_res + x_min  # xs_grid对应X轴（宽度维度）
        world_y = ys_grid * y_res + y_min  # ys_grid对应Y轴（高度维度）
        
        print(f"网格({xs_grid:3d}, {ys_grid:3d}) -> 世界({world_x:6.2f}, {world_y:6.2f})")
    
    # 验证中心点是否接近原点
    center_x = (bev_w//2) * x_res + x_min
    center_y = (bev_h//2) * y_res + y_min
    print(f"\n中心点世界坐标: ({center_x:.2f}, {center_y:.2f})")
    print(f"是否接近原点: {abs(center_x) < 1.0 and abs(center_y) < 1.0}")

def test_tensor_indexing():
    """
    测试张量索引的正确性
    验证修复后的张量索引顺序是否符合PyTorch标准
    """
    print("\n=== 测试张量索引正确性 ===")
    
    batch_size = 2
    num_classes = 4
    bev_h, bev_w = 200, 200
    embed_dim = 64
    
    # 创建模拟的预测张量
    heatmap = torch.randn(batch_size, num_classes, bev_h, bev_w)
    offset_pred = torch.randn(batch_size, 2, bev_h, bev_w)  # [dx, dy]
    z_pred = torch.randn(batch_size, 1, bev_h, bev_w)
    cls_pred = torch.randn(batch_size, num_classes, bev_h, bev_w)
    embed_pred = torch.randn(batch_size, embed_dim, bev_h, bev_w)
    
    print(f"张量形状:")
    print(f"  heatmap: {heatmap.shape}")
    print(f"  offset_pred: {offset_pred.shape}")
    print(f"  z_pred: {z_pred.shape}")
    print(f"  cls_pred: {cls_pred.shape}")
    print(f"  embed_pred: {embed_pred.shape}")
    
    # 模拟topk操作后的网格坐标
    num_points = 100
    batch_idx = 0
    
    # 生成随机的网格坐标（模拟topk结果）
    xs_grid = torch.randint(0, bev_w, (num_points,))  # 宽度维度索引
    ys_grid = torch.randint(0, bev_h, (num_points,))  # 高度维度索引
    
    print(f"\n模拟topk结果:")
    print(f"  xs_grid (宽度维度): {xs_grid[:5]}...")
    print(f"  ys_grid (高度维度): {ys_grid[:5]}...")
    
    # 测试修复后的张量索引顺序
    print("\n测试张量索引 [batch, channel, height_idx, width_idx]:")
    
    try:
        # 按照修复后的索引顺序提取特征
        xs_b_int = xs_grid.long().clamp(0, bev_w - 1)
        ys_b_int = ys_grid.long().clamp(0, bev_h - 1)
        
        # 正确的张量索引顺序: [batch, channel, height_idx, width_idx]
        offset_features = offset_pred[batch_idx, :, ys_b_int, xs_b_int]  # [2, num_points]
        z_features = z_pred[batch_idx, :, ys_b_int, xs_b_int]           # [1, num_points]
        cls_features = cls_pred[batch_idx, :, ys_b_int, xs_b_int]       # [num_classes, num_points]
        embed_features = embed_pred[batch_idx, :, ys_b_int, xs_b_int]   # [embed_dim, num_points]
        
        print(f"  offset_features: {offset_features.shape}")
        print(f"  z_features: {z_features.shape}")
        print(f"  cls_features: {cls_features.shape}")
        print(f"  embed_features: {embed_features.shape}")
        
        # 验证提取的特征是否合理
        print(f"\n特征统计:")
        print(f"  offset范围: [{offset_features.min():.3f}, {offset_features.max():.3f}]")
        print(f"  z值范围: [{z_features.min():.3f}, {z_features.max():.3f}]")
        print(f"  类别分数范围: [{cls_features.min():.3f}, {cls_features.max():.3f}]")
        print(f"  嵌入特征范围: [{embed_features.min():.3f}, {embed_features.max():.3f}]")
        
        print("✓ 张量索引测试通过")
        
    except Exception as e:
        print(f"✗ 张量索引测试失败: {e}")
        return False
    
    return True

def test_coordinate_transformation():
    """
    测试完整的坐标转换流程
    从网格坐标到世界坐标，再应用偏移修正
    """
    print("\n=== 测试坐标转换流程 ===")
    
    # BEV配置
    bev_h, bev_w = 200, 200
    x_min, x_max = -50.0, 50.0
    y_min, y_max = -50.0, 50.0
    x_res = (x_max - x_min) / bev_w
    y_res = (y_max - y_min) / bev_h
    
    # 模拟一些网格点
    xs_grid = torch.tensor([50, 100, 150])  # 宽度维度
    ys_grid = torch.tensor([60, 100, 140])  # 高度维度
    
    # 模拟偏移预测 [dx, dy]
    offset_x = torch.tensor([0.1, -0.2, 0.3])  # X方向偏移
    offset_y = torch.tensor([-0.1, 0.15, -0.25])  # Y方向偏移
    
    print(f"输入网格坐标:")
    print(f"  xs_grid (宽度): {xs_grid}")
    print(f"  ys_grid (高度): {ys_grid}")
    print(f"  offset_x: {offset_x}")
    print(f"  offset_y: {offset_y}")
    
    # 按照修复后的逻辑进行坐标转换
    world_x = xs_grid.float() * x_res + x_min  # xs_grid -> X轴世界坐标
    world_y = ys_grid.float() * y_res + y_min  # ys_grid -> Y轴世界坐标
    
    # 应用偏移修正
    final_x = world_x + offset_x
    final_y = world_y + offset_y
    
    print(f"\n坐标转换结果:")
    for i in range(len(xs_grid)):
        print(f"  点{i+1}: 网格({xs_grid[i]}, {ys_grid[i]}) -> "
              f"世界({world_x[i]:.2f}, {world_y[i]:.2f}) -> "
              f"修正后({final_x[i]:.2f}, {final_y[i]:.2f})")
    
    # 验证坐标范围是否合理
    x_in_range = torch.all((final_x >= x_min - 5) & (final_x <= x_max + 5))
    y_in_range = torch.all((final_y >= y_min - 5) & (final_y <= y_max + 5))
    
    print(f"\n坐标范围检查:")
    print(f"  X坐标在合理范围内: {x_in_range}")
    print(f"  Y坐标在合理范围内: {y_in_range}")
    
    return x_in_range and y_in_range

def test_boundary_conditions():
    """
    测试边界条件处理
    验证边界钳位是否正确工作
    """
    print("\n=== 测试边界条件 ===")
    
    bev_h, bev_w = 200, 200
    
    # 测试边界和超出边界的坐标
    test_coords = [
        (-1, -1),      # 超出左上边界
        (0, 0),        # 左上角
        (bev_w-1, bev_h-1),  # 右下角
        (bev_w, bev_h),      # 超出右下边界
        (bev_w+10, bev_h+10), # 大幅超出边界
    ]
    
    print(f"边界钳位测试 (范围: [0, {bev_w-1}] x [0, {bev_h-1}]):")
    
    for x, y in test_coords:
        x_tensor = torch.tensor([x])
        y_tensor = torch.tensor([y])
        
        # 应用边界钳位
        x_clamped = x_tensor.long().clamp(0, bev_w - 1)
        y_clamped = y_tensor.long().clamp(0, bev_h - 1)
        
        print(f"  ({x:3d}, {y:3d}) -> ({x_clamped.item():3d}, {y_clamped.item():3d})")
    
    print("✓ 边界钳位测试通过")

def run_all_tests():
    """
    运行所有测试
    """
    print("开始BEVLaneHeatmapHead坐标系统修复验证测试...")
    print("=" * 60)
    
    try:
        # 测试1: 坐标系统一致性
        test_coordinate_system_consistency()
        
        # 测试2: 张量索引正确性
        tensor_test_passed = test_tensor_indexing()
        
        # 测试3: 坐标转换流程
        coord_test_passed = test_coordinate_transformation()
        
        # 测试4: 边界条件
        test_boundary_conditions()
        
        print("\n" + "=" * 60)
        print("测试结果总结:")
        print(f"  张量索引测试: {'✓ 通过' if tensor_test_passed else '✗ 失败'}")
        print(f"  坐标转换测试: {'✓ 通过' if coord_test_passed else '✗ 失败'}")
        print(f"  边界条件测试: ✓ 通过")
        
        if tensor_test_passed and coord_test_passed:
            print("\n🎉 所有测试通过！坐标系统修复验证成功。")
            return True
        else:
            print("\n❌ 部分测试失败，需要进一步检查。")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)