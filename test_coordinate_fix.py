#!/usr/bin/env python3
"""
测试坐标系统修复效果的脚本

这个脚本将验证BEVLaneHeatmapHead中的坐标系统修复是否正常工作。
"""

import torch
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append('/turbo/pcpt/project/liuyibo/multimodal_bevfusion')

def test_coordinate_fix():
    """测试坐标系统修复效果"""
    print("=== 坐标系统修复测试 ===")
    
    try:
        from mmdet3d.models.heads.lane.bev_lane_heatmap_head import BEVLaneHeatmapHead
        
        # 创建测试配置
        grid_conf = {
            'xbound': [-25.6, 25.6, 0.8],  # [x_min, x_max, x_res]
            'ybound': [-9.6, 9.6, 0.8],    # [y_min, y_max, y_res]
            'zbound': [-5, 3, 8],          # [z_min, z_max, z_bins]
        }
        
        # 创建BEVLaneHeatmapHead实例
        head = BEVLaneHeatmapHead(
            in_channels=256,
            feat_channels=128,
            num_classes=1,
            grid_conf=grid_conf
        )
        
        print(f"网格配置: nx={head.nx}, ny={head.ny}")
        print(f"期望heatmap形状: [batch, channels, {head.ny}, {head.nx}]")
        
        # 测试1: 正常形状的heatmap
        print("\n--- 测试1: 正常形状heatmap ---")
        normal_heatmap = torch.randn(1, 1, head.ny, head.nx)  # [1, 1, 23, 64]
        print(f"输入heatmap形状: {normal_heatmap.shape}")
        
        scores, inds, classes, ys, xs = head._topk(normal_heatmap, k=10)
        print(f"输出坐标范围: ys=[{ys.min():.1f}, {ys.max():.1f}], xs=[{xs.min():.1f}, {xs.max():.1f}]")
        print(f"期望坐标范围: ys=[0, {head.ny-1}], xs=[0, {head.nx-1}]")
        
        # 测试2: 颠倒形状的heatmap (模拟问题情况)
        print("\n--- 测试2: 颠倒形状heatmap ---")
        swapped_heatmap = torch.randn(1, 1, head.nx, head.ny)  # [1, 1, 64, 24]
        print(f"输入heatmap形状: {swapped_heatmap.shape}")
        
        scores, inds, classes, ys, xs = head._topk(swapped_heatmap, k=10)
        print(f"输出坐标范围: ys=[{ys.min():.1f}, {ys.max():.1f}], xs=[{xs.min():.1f}, {xs.max():.1f}]")
        print(f"期望坐标范围: ys=[0, {head.ny-1}], xs=[0, {head.nx-1}]")
        
        # 验证坐标是否在有效范围内
        ys_valid = (ys >= 0) & (ys < head.ny)
        xs_valid = (xs >= 0) & (xs < head.nx)
        
        print(f"\n坐标有效性检查:")
        print(f"  ys坐标有效率: {ys_valid.float().mean():.2%}")
        print(f"  xs坐标有效率: {xs_valid.float().mean():.2%}")
        
        if ys_valid.all() and xs_valid.all():
            print("✅ 坐标修复成功！所有坐标都在有效范围内。")
        else:
            print("❌ 坐标修复可能存在问题，部分坐标超出有效范围。")
            
        # 测试3: 完整的get_lanes流程
        print("\n--- 测试3: 完整get_lanes流程 ---")
        
        # 创建模拟的模型输出 (根据get_lanes方法的参数格式)
        heatmap = torch.randn(1, 1, head.nx, head.ny)  # 注意：这里是颠倒的形状
        offset = torch.randn(1, 2, head.nx, head.ny)
        z_pred = torch.randn(1, 1, head.nx, head.ny)
        cls_pred = torch.randn(1, 1, head.nx, head.ny)
        
        print(f"heatmap形状: {heatmap.shape}")
        print(f"offset形状: {offset.shape}")
        print(f"z_pred形状: {z_pred.shape}")
        print(f"cls_pred形状: {cls_pred.shape}")
        
        # 根据是否使用embedding决定preds格式
        if head.use_embedding:
            embed_pred = torch.randn(1, head.embed_dims, head.nx, head.ny)
            preds = (heatmap, offset, z_pred, cls_pred, embed_pred)
        else:
            preds = (heatmap, offset, z_pred, cls_pred)
        
        # 调用get_lanes方法
        results = head.get_lanes(
            preds=preds,
            img_metas=[{'img_shape': (900, 1600, 3)}]
        )
        
        if results and len(results) > 0 and 'points_3d' in results[0]:
            points_3d = results[0]['points_3d']
            print(f"\n输出points_3d形状: {points_3d.shape}")
            
            if len(points_3d) > 0:
                x_coords = points_3d[:, 0]
                y_coords = points_3d[:, 1]
                z_coords = points_3d[:, 2]
                
                print(f"X坐标范围: [{x_coords.min():.1f}, {x_coords.max():.1f}] (期望: [0.0, 51.2])")
                print(f"Y坐标范围: [{y_coords.min():.1f}, {y_coords.max():.1f}] (期望: [-9.6, 9.6])")
                print(f"Z坐标范围: [{z_coords.min():.1f}, {z_coords.max():.1f}]")
                
                # 检查坐标是否在期望范围内
                x_in_range = (x_coords >= 0) & (x_coords <= 51.2)
                y_in_range = (y_coords >= -9.6) & (y_coords <= 9.6)
                
                print(f"\n坐标范围检查:")
                print(f"  X坐标在期望范围内: {x_in_range.float().mean():.2%}")
                print(f"  Y坐标在期望范围内: {y_in_range.float().mean():.2%}")
                
                if x_in_range.float().mean() > 0.8 and y_in_range.float().mean() > 0.8:
                    print("✅ 坐标系统修复效果良好！")
                else:
                    print("⚠️  坐标系统可能仍需进一步调整。")
            else:
                print("⚠️  未检测到车道线点。")
        else:
            print("❌ get_lanes方法执行失败或未返回有效结果。")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_coordinate_fix()