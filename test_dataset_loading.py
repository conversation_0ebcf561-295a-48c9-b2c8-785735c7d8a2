#!/usr/bin/env python3

import sys
import os
sys.path.append('/pcpt/pcpt/project/liuyibo/multimodal_bevfusion')

from mmdet3d.datasets import Custom3DLaneDataset
import yaml

def test_dataset_loading():
    """测试数据集加载是否正常工作"""
    
    # 直接使用已知的数据集路径
    dataset_root = '/pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames'
    ann_file = '/pcpt/pcpt/data/lane_dataset/lane_3D_lane_dataset_1000frames/train_annotations_skip30_without_depth_maps.pkl'
    cam_list = ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
    
    print("=== 数据集配置信息 ===")
    print(f"数据集根目录: {dataset_root}")
    print(f"标注文件: {ann_file}")
    print(f"相机列表: {cam_list}")
    print()
    
    # 创建数据集实例
    try:
        dataset = Custom3DLaneDataset(
            dataset_root=dataset_root,
            ann_file=ann_file,
            cam_list=cam_list,
            pipeline=[],  # 暂时不使用pipeline
            test_mode=False
        )
        
        print(f"=== 数据集加载成功 ===")
        print(f"数据集大小: {len(dataset)}")
        print()
        
        # 测试第一个样本
        if len(dataset) > 0:
            print("=== 测试第一个样本 ===")
            
            # 获取原始数据信息
            info = dataset.data_infos[0]
            print(f"样本信息键: {list(info.keys())}")
            
            if 'cam_paths' in info:
                print(f"cam_paths类型: {type(info['cam_paths'])}")
                print(f"cam_paths键: {list(info['cam_paths'].keys()) if isinstance(info['cam_paths'], dict) else 'Not a dict'}")
                
                # 检查每个相机路径
                for cam_name, cam_path in info['cam_paths'].items():
                    exists = os.path.exists(cam_path)
                    print(f"  {cam_name}: {cam_path} -> 存在: {exists}")
            
            if 'lidar_path' in info:
                lidar_exists = os.path.exists(info['lidar_path'])
                print(f"lidar_path: {info['lidar_path']} -> 存在: {lidar_exists}")
            
            # 测试_process_camera_data方法
            print("\n=== 测试相机数据处理 ===")
            input_dict = {
                'image_paths': [],
                'camera_intrinsics': [],
                'lidar2camera': []
            }
            
            try:
                dataset._process_camera_data(input_dict, info)
                print(f"处理后的image_paths数量: {len(input_dict['image_paths'])}")
                print(f"有效图像路径数量: {sum(1 for p in input_dict['image_paths'] if p is not None)}")
                
                for i, path in enumerate(input_dict['image_paths']):
                    if path is not None:
                        exists = os.path.exists(path)
                        print(f"  路径 {i}: {path} -> 存在: {exists}")
                    else:
                        print(f"  路径 {i}: None")
                        
            except Exception as e:
                print(f"相机数据处理失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试完整的数据获取流程
            print("\n=== 测试完整数据获取 ===")
            try:
                data_info = dataset.get_data_info(0)
                if 'image_paths' in data_info:
                    print(f"完整流程获取的image_paths数量: {len(data_info['image_paths'])}")
                    for i, path in enumerate(data_info['image_paths']):
                        if path is not None:
                            exists = os.path.exists(path)
                            print(f"  完整路径 {i}: {path} -> 存在: {exists}")
                        else:
                            print(f"  完整路径 {i}: None")
                else:
                    print("完整流程中没有image_paths字段")
            except Exception as e:
                print(f"完整数据获取失败: {e}")
                import traceback
                traceback.print_exc()
        
    except Exception as e:
        print(f"数据集加载失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_dataset_loading()