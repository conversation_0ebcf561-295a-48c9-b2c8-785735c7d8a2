#!/usr/bin/env python3
"""
测试脚本：验证lane_processing.py和bev_lane_heatmap_head.py之间的字段映射修复

主要测试内容：
1. 验证lane_processing.py输出正确的字段名（gt_z而不是gt_height）
2. 验证bev_lane_heatmap_head.py能正确映射gt_z到gt_height
3. 验证数值验证和调试信息正常工作
4. 验证网格配置参数的正确性
"""

import sys
import os
import torch
import numpy as np
from mmcv.parallel import DataContainer as DC

# 添加项目路径
sys.path.insert(0, '/pcpt/pcpt/project/liuyibo/multimodal_bevfusion')

def test_lane_processing_output_fields():
    """测试lane_processing.py输出的字段名是否正确"""
    print("\n=== 测试1: lane_processing.py字段输出 ===")
    
    try:
        from mmdet3d.datasets.pipelines.lane_processing import GenerateBEVLaneHeatmapTargets
        
        # 创建处理器实例
        grid_conf = {
            'xbound': [-81.6, 97.6, 0.4],
            'ybound': [-48.0, 48.0, 0.4]
        }
        
        processor = GenerateBEVLaneHeatmapTargets(
            point_cloud_range=[-81.6, -48.0, -3.0, 97.6, 48.0, 5.0],
            grid_conf=grid_conf,
            lane_classes=['lane'],
            target_config={
                'heatmap_radius': 3,
                'cls_radius': 3,
                'reg_radius': 3,
                'vis_threshold': 0.5,
                'generate_instance_ids': True,
                'max_lanes': 40,
                'num_points': 120
            }
        )
        
        # 创建模拟的车道数据
        lane_3d = {
            'xyz': np.array([
                [10.0, -2.0, 0.0],
                [20.0, -2.0, 0.0], 
                [30.0, -2.0, 0.0],
                [40.0, -2.0, 0.0]
            ]),
            'visibility': np.array([1.0, 1.0, 1.0, 1.0])
        }
        
        results = {
            'lane_3d': [lane_3d],
            'lane_labels': [1],  # 车道类别
        }
        
        # 处理数据
        processed_results = processor(results)
        
        # 检查输出字段
        if 'lane_targets' in processed_results:
            lane_targets = processed_results['lane_targets']
            print(f"lane_processing.py输出的字段: {list(lane_targets.keys())}")
            
            # 验证关键字段存在
            expected_fields = ['gt_heatmap', 'gt_offset', 'gt_z', 'gt_mask', 'gt_cls']
            missing_fields = []
            for field in expected_fields:
                if field not in lane_targets:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ 缺失字段: {missing_fields}")
                return False
            else:
                print("✅ 所有期望字段都存在")
                
            # 验证gt_z字段（而不是gt_height）
            if 'gt_z' in lane_targets and 'gt_height' not in lane_targets:
                print("✅ 正确输出gt_z字段（而不是gt_height）")
                return True
            else:
                print(f"❌ 字段映射错误: gt_z存在={('gt_z' in lane_targets)}, gt_height存在={('gt_height' in lane_targets)}")
                return False
        else:
            print("❌ 没有找到lane_targets输出")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_head_field_mapping():
    """测试bev_lane_heatmap_head.py的字段映射功能"""
    print("\n=== 测试2: bev_lane_heatmap_head.py字段映射 ===")
    
    try:
        from mmdet3d.models.heads.lane.bev_lane_heatmap_head import BEVLaneHeatmapHead
        
        # 创建头部实例
        grid_conf = {
            'xbound': [-81.6, 97.6, 0.4],
            'ybound': [-48.0, 48.0, 0.4]
        }
        
        head = BEVLaneHeatmapHead(
            in_channels=256,
            num_classes=4,
            grid_conf=grid_conf,
            use_embedding=False
        )
        
        # 创建模拟的lane_targets（模拟lane_processing.py的输出）
        H, W = 447, 240  # 根据grid_conf计算的网格大小: [X=447, Y=240]
        
        # 模拟DataContainer包装的数据
        mock_lane_targets = {
            'gt_heatmap': [torch.randn(1, H, W)],
            'gt_offset': [torch.randn(2, H, W)],  # offset有2个通道
            'gt_z': [torch.randn(1, H, W)],  # 注意：这里是gt_z，不是gt_height
            'gt_mask': [torch.ones(1, H, W)],
            'gt_cls': [torch.randn(4, H, W)]
        }
        
        # 测试字段转换
        transformed = head._transform_lane_targets(mock_lane_targets)
        
        if transformed is None:
            print("❌ 字段转换返回None")
            return False
            
        print(f"转换后的字段: {list(transformed.keys())}")
        
        # 验证gt_z被正确映射到gt_height
        if 'gt_height' in transformed:
            print("✅ gt_z成功映射到gt_height")
            
            # 验证其他字段也存在
            expected_output_fields = ['gt_heatmap', 'gt_offset', 'gt_height', 'gt_mask', 'gt_cls']
            missing_output_fields = []
            for field in expected_output_fields:
                if field not in transformed:
                    missing_output_fields.append(field)
            
            if missing_output_fields:
                print(f"❌ 转换后缺失字段: {missing_output_fields}")
                return False
            else:
                print("✅ 所有期望的输出字段都存在")
                return True
        else:
            print("❌ gt_z没有被映射到gt_height")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_grid_configuration_validation():
    """测试网格配置参数的正确性"""
    print("\n=== 测试3: 网格配置参数验证 ===")
    
    try:
        from mmdet3d.datasets.pipelines.lane_processing import GenerateBEVLaneHeatmapTargets
        
        # 测试配置
        grid_conf = {
            'xbound': [-81.6, 97.6, 0.4],  # x范围和分辨率
            'ybound': [-48.0, 48.0, 0.4]   # y范围和分辨率
        }
        
        processor = GenerateBEVLaneHeatmapTargets(
            point_cloud_range=[-81.6, -48.0, -3.0, 97.6, 48.0, 5.0],
            grid_conf=grid_conf,
            lane_classes=['lane'],
            target_config={
                'heatmap_radius': 3,
                'cls_radius': 3,
                'reg_radius': 3,
                'vis_threshold': 0.5,
                'max_lanes': 40,
                'num_points': 120
            }
        )
        
        # 验证网格大小计算
        expected_x_size = int((97.6 - (-81.6)) / 0.4)  # 448
        expected_y_size = int((48.0 - (-48.0)) / 0.4)   # 240
        
        actual_grid_size = processor.grid_size
        print(f"期望网格大小: [X={expected_x_size}, Y={expected_y_size}]")
        print(f"实际网格大小: {actual_grid_size}")
        
        if actual_grid_size == [expected_x_size, expected_y_size]:
            print("✅ 网格大小计算正确")
            
            # 验证边界参数
            print(f"X边界: [{processor.x_min}, {processor.x_max}], 分辨率: {processor.x_res}")
            print(f"Y边界: [{processor.y_min}, {processor.y_max}], 分辨率: {processor.y_res}")
            
            return True
        else:
            print(f"❌ 网格大小计算错误")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_pipeline():
    """端到端测试：从lane_processing到head的完整数据流"""
    print("\n=== 测试4: 端到端数据流测试 ===")
    
    try:
        from mmdet3d.datasets.pipelines.lane_processing import GenerateBEVLaneHeatmapTargets
        from mmdet3d.models.heads.lane.bev_lane_heatmap_head import BEVLaneHeatmapHead
        
        # 统一的网格配置
        grid_conf = {
            'xbound': [-81.6, 97.6, 0.4],
            'ybound': [-48.0, 48.0, 0.4]
        }
        
        # 创建处理器和头部
        processor = GenerateBEVLaneHeatmapTargets(
            point_cloud_range=[-81.6, -48.0, -3.0, 97.6, 48.0, 5.0],
            grid_conf=grid_conf,
            lane_classes=['lane'],
            target_config={
                'heatmap_radius': 3,
                'cls_radius': 3,
                'reg_radius': 3,
                'vis_threshold': 0.5,
                'max_lanes': 40,
                'num_points': 120
            }
        )
        
        head = BEVLaneHeatmapHead(
            in_channels=256,
            num_classes=4,
            grid_conf=grid_conf,
            use_embedding=False
        )
        
        # 创建模拟车道数据
        lane_3d = {
            'xyz': np.array([
                [10.0, -2.0, 0.0],
                [20.0, -2.0, 0.0], 
                [30.0, -2.0, 0.0],
                [40.0, -2.0, 0.0]
            ]),
            'visibility': np.array([1.0, 1.0, 1.0, 1.0])
        }
        
        results = {
            'lane_3d': [lane_3d],
            'lane_labels': [1],
        }
        
        # 步骤1: 处理数据
        processed_results = processor(results)
        lane_targets = processed_results['lane_targets']
        
        print(f"处理器输出字段: {list(lane_targets.keys())}")
        
        # 步骤2: 转换字段
        print(f"\n调用_transform_lane_targets前，lane_targets内容:")
        for key, value in lane_targets.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}, dtype: {value.dtype}")
            else:
                print(f"  {key}: {type(value)}")
        
        transformed_targets = head._transform_lane_targets(lane_targets)
        
        print(f"\n_transform_lane_targets返回结果: {type(transformed_targets)}")
        if transformed_targets is None:
            print("❌ 字段转换失败 - 返回None")
            return False
        elif not transformed_targets:
            print("❌ 字段转换失败 - 返回空字典")
            return False
            
        print(f"头部转换后字段: {list(transformed_targets.keys())}")
        
        # 验证完整的数据流
        success_checks = [
            'gt_z' in lane_targets,  # 处理器输出gt_z
            'gt_height' in transformed_targets,  # 头部转换为gt_height
            'gt_heatmap' in transformed_targets,
            'gt_mask' in transformed_targets,
            'gt_offset' in transformed_targets,
            'gt_cls' in transformed_targets
        ]
        
        if all(success_checks):
            print("✅ 端到端数据流测试成功")
            return True
        else:
            print(f"❌ 端到端测试失败，检查结果: {success_checks}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("开始字段映射修复验证测试...")
    
    test_results = [
        test_lane_processing_output_fields(),
        test_head_field_mapping(), 
        test_grid_configuration_validation(),
        test_end_to_end_pipeline()
    ]
    
    print("\n=== 测试结果汇总 ===")
    test_names = [
        "lane_processing.py字段输出",
        "bev_lane_heatmap_head.py字段映射",
        "网格配置参数验证",
        "端到端数据流测试"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"测试{i+1}: {name} - {status}")
    
    all_passed = all(test_results)
    print(f"\n总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if all_passed:
        print("\n🎉 字段映射修复验证成功！代码已准备好用于训练。")
    else:
        print("\n⚠️  仍有问题需要解决，请检查失败的测试。")
    
    return all_passed

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)