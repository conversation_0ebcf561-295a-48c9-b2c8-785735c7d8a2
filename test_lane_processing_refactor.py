#!/usr/bin/env python3
"""
Test script to validate the refactored GenerateBEVLaneHeatmapTargets module.

This script tests:
1. Correct grid resolution alignment (0.4m vs 0.1m)
2. Batch-safe processing (no state pollution)
3. Interpolation functionality
4. Target generation consistency
"""

import numpy as np
import torch
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.abspath('.'))

from mmdet3d.datasets.pipelines.lane_processing import GenerateBEVLaneHeatmapTargets


def test_grid_resolution_alignment():
    """Test that the target generator uses correct BEV resolution."""
    print("=== Testing Grid Resolution Alignment ===")
    
    # Configuration matching the head
    point_cloud_range = [-81.6, -48, -1, 97.6, 48, 3.0]
    grid_conf = {
        'xbound': [-81.6, 97.6, 0.4],
        'ybound': [-48.0, 48.0, 0.4]
    }
    lane_classes = ['background', 'white-solid', 'yellow-solid']
    
    # Initialize target generator
    target_gen = GenerateBEVLaneHeatmapTargets(
        point_cloud_range=point_cloud_range,
        grid_conf=grid_conf,
        lane_classes=lane_classes,
        target_config={'num_points': 120, 'vis_threshold': 0.5}
    )
    
    # Verify grid dimensions
    expected_x_size = int((97.6 - (-81.6)) / 0.4)  # 448
    expected_y_size = int((48.0 - (-48.0)) / 0.4)  # 240
    
    print(f"Expected grid size: [{expected_x_size}, {expected_y_size}]")
    print(f"Actual grid size: {target_gen.grid_size}")
    print(f"Resolution: x_res={target_gen.x_res}m, y_res={target_gen.y_res}m")
    
    assert target_gen.grid_size == [expected_x_size, expected_y_size], \
        f"Grid size mismatch: expected [{expected_x_size}, {expected_y_size}], got {target_gen.grid_size}"
    assert target_gen.x_res == 0.4 and target_gen.y_res == 0.4, \
        f"Resolution mismatch: expected 0.4m, got x_res={target_gen.x_res}, y_res={target_gen.y_res}"
    
    print("✓ Grid resolution alignment test passed!")
    return target_gen


def test_batch_safe_processing(target_gen):
    """Test that processing is batch-safe (no state pollution)."""
    print("\n=== Testing Batch-Safe Processing ===")
    
    # Create two different lane samples
    sample1 = {
        'gt_lanes_3d': [{
            'points': [[0, -10, 0], [10, -10, 0], [20, -10, 0]],
            'visibility': [1.0, 1.0, 1.0]
        }],
        'gt_lane_labels': [1]
    }
    
    sample2 = {
        'gt_lanes_3d': [{
            'points': [[0, 10, 0], [10, 10, 0], [20, 10, 0]],
            'visibility': [1.0, 1.0, 1.0]
        }],
        'gt_lane_labels': [2]
    }
    
    # Store original x_positions
    original_x_positions = target_gen.x_positions.copy()
    
    # Process first sample
    result1 = target_gen(sample1)
    x_positions_after_1 = target_gen.x_positions.copy()
    
    # Process second sample
    result2 = target_gen(sample2)
    x_positions_after_2 = target_gen.x_positions.copy()
    
    # Verify x_positions remain unchanged (batch-safe)
    assert np.array_equal(original_x_positions, x_positions_after_1), \
        "x_positions modified after first sample processing!"
    assert np.array_equal(original_x_positions, x_positions_after_2), \
        "x_positions modified after second sample processing!"
    assert np.array_equal(x_positions_after_1, x_positions_after_2), \
        "x_positions inconsistent between samples!"
    
    print("✓ Batch-safe processing test passed!")
    print(f"  x_positions shape: {target_gen.x_positions.shape}")
    print(f"  x_positions range: [{target_gen.x_positions[0]:.1f}, {target_gen.x_positions[-1]:.1f}]")
    
    return result1, result2


def test_interpolation_functionality(target_gen):
    """Test that interpolation works correctly."""
    print("\n=== Testing Interpolation Functionality ===")
    
    # Create a sample with sparse lane points
    sample = {
        'gt_lanes_3d': [{
            'points': [[-50, -5, 0], [0, -5, 0.5], [50, -5, 1.0]],  # Sparse points
            'visibility': [1.0, 1.0, 1.0]
        }],
        'gt_lane_labels': [1]
    }
    
    # Process sample
    result = target_gen(sample)
    
    # Check that targets were generated
    assert 'lane_targets' in result, "lane_targets not found in result!"
    
    targets = result['lane_targets']
    required_keys = ['heatmap', 'offset', 'z_map', 'mask', 'cls_map']
    
    for key in required_keys:
        assert key in targets, f"Required target '{key}' not found!"
        assert isinstance(targets[key], torch.Tensor), f"Target '{key}' is not a tensor!"
    
    # Check target shapes
    expected_shapes = {
        'heatmap': (1, 1, target_gen.grid_size[0], target_gen.grid_size[1]),
        'offset': (1, 2, target_gen.grid_size[0], target_gen.grid_size[1]),  # 2 channels for x,y offset
        'z_map': (1, 1, target_gen.grid_size[0], target_gen.grid_size[1]),
        'mask': (1, 1, target_gen.grid_size[0], target_gen.grid_size[1])
    }
    
    for key, expected_shape in expected_shapes.items():
        actual_shape = targets[key].shape
        assert actual_shape == expected_shape, \
            f"Target '{key}' shape mismatch: expected {expected_shape}, got {actual_shape}"
    
    # Check that some targets were generated (non-zero mask)
    mask_sum = targets['mask'].sum().item()
    assert mask_sum > 0, f"No valid targets generated (mask sum = {mask_sum})"
    
    print("✓ Interpolation functionality test passed!")
    print(f"  Generated {mask_sum} valid target points")
    print(f"  Heatmap max value: {targets['heatmap'].max().item():.3f}")
    print(f"  Target shapes: {targets['heatmap'].shape}")
    
    return result


def test_backward_compatibility():
    """Test backward compatibility with voxel_size parameter."""
    print("\n=== Testing Backward Compatibility ===")
    
    point_cloud_range = [-81.6, -48, -1, 97.6, 48, 3.0]
    voxel_size = [0.1, 0.1, 0.2]  # Old parameter
    lane_classes = ['background', 'white-solid']
    
    # Should still work but use 0.4m resolution internally
    target_gen = GenerateBEVLaneHeatmapTargets(
        point_cloud_range=point_cloud_range,
        voxel_size=voxel_size,  # Old parameter
        lane_classes=lane_classes
    )
    
    # Should use 0.4m resolution despite voxel_size=0.1m
    assert target_gen.x_res == 0.4 and target_gen.y_res == 0.4, \
        f"Backward compatibility failed: expected 0.4m resolution, got {target_gen.x_res}m"
    
    print("✓ Backward compatibility test passed!")
    print(f"  Used 0.4m resolution despite voxel_size=[0.1, 0.1, 0.2]")


def main():
    """Run all tests."""
    print("Testing Refactored GenerateBEVLaneHeatmapTargets")
    print("=" * 50)
    
    try:
        # Test 1: Grid resolution alignment
        target_gen = test_grid_resolution_alignment()
        
        # Test 2: Batch-safe processing
        result1, result2 = test_batch_safe_processing(target_gen)
        
        # Test 3: Interpolation functionality
        result3 = test_interpolation_functionality(target_gen)
        
        # Test 4: Backward compatibility
        test_backward_compatibility()
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed! Refactoring successful.")
        print("\nKey improvements:")
        print("  ✓ Fixed 4x resolution mismatch (0.1m → 0.4m)")
        print("  ✓ Eliminated batch processing state pollution")
        print("  ✓ Restored robust interpolation functionality")
        print("  ✓ Maintained backward compatibility")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())