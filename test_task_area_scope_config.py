#!/usr/bin/env python3
"""
Test script for validating the new task_area_scope configuration changes.
This script verifies that the BEV cropping works correctly with the new forward-focused range.
"""

import torch
import numpy as np
import sys
import os

# Add project path
sys.path.append('/turbo/pcpt/project/liuyibo/multimodal_bevfusion')

def test_task_area_scope_config():
    """Test the new task area scope configuration"""
    print("=== Testing Task Area Scope Configuration ===")
    
    try:
        from mmdet3d.models.heads.lane.bev_lane_heatmap_head import BEVLaneHeatmapHead
        
        # Configuration matching your YAML changes
        config = {
            'in_channels': 256,
            'feat_channels': 64,
            'num_classes': 13,  # Including background
            'grid_conf': {
                'xbound': [-81.6, 97.6, 0.4],
                'ybound': [-48.0, 48.0, 0.4]
            },
            'task_area_scope': {
                'x_range': [0.0, 60.0],    # NEW: Forward-focused
                'y_range': [-15.0, 15.0],  # Narrower lateral range
                'z_range': [-1.0, 3.0],
                'enable_crop': True,       # NEW: Enable cropping
                'crop_method': 'slice'     # NEW: Slice method
            },
            'row_points': 120,
            'z_range': [-1.0, 3.0],
            'max_lanes': 40,
            'use_embedding': True,
            'embedding_dim': 16
        }
        
        # Create head instance
        head = BEVLaneHeatmapHead(**config)
        print(f"✓ Head initialized successfully")
        
        # Verify grid dimensions
        print(f"\nGrid Configuration:")
        print(f"  Global grid: {head.global_nx} x {head.global_ny}")
        print(f"  Task grid: {head.nx} x {head.ny}")
        print(f"  Crop enabled: {head.enable_crop}")
        
        if head.enable_crop:
            print(f"  Crop indices: x[{head.crop_x_start}:{head.crop_x_end}], y[{head.crop_y_start}:{head.crop_y_end}]")
            print(f"  Task area: x[{head.x_min:.1f}, {head.x_max:.1f}], y[{head.y_min:.1f}, {head.y_max:.1f}]")
        
        # Test forward pass with mock data
        batch_size = 2
        global_h, global_w = head.global_ny, head.global_nx
        
        # Create mock BEV features (global size)
        mock_features = torch.randn(batch_size, 256, global_h, global_w)
        print(f"\nTesting forward pass:")
        print(f"  Input features shape: {mock_features.shape}")
        
        # Forward pass
        with torch.no_grad():
            outputs = head(mock_features)
        
        print(f"  Output shapes:")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"    {key}: {value.shape}")
        
        # Verify output dimensions match task area
        expected_h, expected_w = head.ny, head.nx
        heatmap_shape = outputs['heatmap'].shape[-2:]
        
        if heatmap_shape == (expected_h, expected_w):
            print(f"✓ Output dimensions correct: {heatmap_shape}")
        else:
            print(f"✗ Output dimensions mismatch: got {heatmap_shape}, expected ({expected_h}, {expected_w})")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinate_consistency():
    """Test that coordinates are consistent between global and task areas"""
    print("\n=== Testing Coordinate Consistency ===")
    
    # Test points in the task area
    test_points = [
        [30.0, 0.0],    # Center forward
        [10.0, -10.0],  # Left side
        [50.0, 10.0],   # Right side forward
        [0.0, 0.0],     # Origin (should be at task area boundary)
    ]
    
    for x, y in test_points:
        # Check if point is within task area
        in_task_area = (0.0 <= x <= 60.0) and (-15.0 <= y <= 15.0)
        print(f"  Point ({x:4.1f}, {y:4.1f}): {'✓' if in_task_area else '✗'} in task area")

if __name__ == "__main__":
    print("Testing new task_area_scope configuration...")
    print("=" * 60)
    
    success = test_task_area_scope_config()
    test_coordinate_consistency()
    
    if success:
        print("\n✓ All tests passed! Configuration is ready for training.")
    else:
        print("\n✗ Tests failed. Please check the configuration.")