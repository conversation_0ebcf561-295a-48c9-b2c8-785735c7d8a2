#!/usr/bin/env python

import argparse
import os
import mmcv
import torch
import numpy as np
import time
import cv2
from pathlib import Path
from mmcv import Config, DictAction
from mmcv.parallel import MMDataParallel, MMDistributedDataParallel
from mmcv.runner import get_dist_info, init_dist, load_checkpoint, wrap_fp16_model

from mmdet3d.datasets import build_dataloader, build_dataset
from mmdet3d.models import build_model
from mmdet3d.apis import single_gpu_test
from mmdet.apis import multi_gpu_test, set_random_seed
from mmdet.datasets import replace_ImageToTensor

# Import the new visualization module
from visualizer_tool.visualize_3d_lane_results import visualize_lane_detection_results


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='3D Lane Detection Inference')
    parser.add_argument('--config', help='test config file path', required=True)
    parser.add_argument('--checkpoint', help='checkpoint file', required=True)
    parser.add_argument('--input', help='input path, could override the dataset_root in config')
    parser.add_argument(
        '--output-dir', 
        help='output directory for results', 
        default='results/3d_lane_detection'
    )
    parser.add_argument(
        '--show', 
        action='store_true', 
        help='show visualization results'
    )
    parser.add_argument(
        '--show-dir', 
        help='directory where visualization results will be saved'
    )
    parser.add_argument(
        '--show-score-thr',
        type=float,
        default=0.3,
        help='score threshold for visualization'
    )
    parser.add_argument(
        '--gpu-collect',
        action='store_true',
        help='whether to use gpu to collect results.'
    )
    parser.add_argument(
        '--tmpdir',
        help='tmp directory used for collecting results from multiple workers, '
        'available when gpu-collect is not specified'
    )
    parser.add_argument(
        '--launcher',
        choices=['none', 'pytorch', 'slurm', 'mpi'],
        default='none',
        help='job launcher'
    )
    parser.add_argument(
        '--fuse-conv-bn',
        action='store_true',
        help='Whether to fuse conv and bn, this will slightly increase '
        'the inference speed'
    )
    parser.add_argument(
        '--cfg-options',
        nargs='+',
        action=DictAction,
        help='override some settings in the used config, the key-value pair '
        'in xxx=yyy format will be merged into config file. If the value to '
        'be overwritten is a list, it should be like key="[a,b]" or key=a,b '
        'It also allows nested list/tuple values, e.g. key="[(a,b),(c,d)]" '
        'Note that the quotation marks are necessary and that no white space '
        'is allowed.'
    )
    parser.add_argument(
        '--fps',
        action='store_true',
        help='measure and report fps'
    )
    parser.add_argument(
        '--seed',
        type=int,
        default=0,
        help='random seed'
    )
    
    args = parser.parse_args()
    return args


def main():
    args = parse_args()

    # Set cudnn benchmark and random seed
    torch.backends.cudnn.benchmark = True
    set_random_seed(args.seed)
    
    # Initialize distributed environment if needed
    if args.launcher == 'none':
        distributed = False
    else:
        distributed = True
        init_dist(args.launcher)
        # Re-set gpu_ids with distributed training mode
        _, world_size = get_dist_info()
        gpu_ids = range(world_size)
    
    # Load config
    cfg = Config.fromfile(args.config)
    
    # Override config options if specified
    if args.cfg_options is not None:
        cfg.merge_from_dict(args.cfg_options)
    
    # Update input path if specified
    if args.input:
        cfg.dataset_root = args.input
        if hasattr(cfg, 'data'):
            for mode in ['train', 'val', 'test']:
                if hasattr(cfg.data, mode):
                    cfg.data[mode].dataset_root = args.input
                    # Update annotation file path
                    ann_file = os.path.join(args.input, 'test_info.pkl')
                    if os.path.exists(ann_file):
                        cfg.data[mode].ann_file = ann_file
    
    # Make sure test_mode is set to True
    if hasattr(cfg, 'data'):
        if hasattr(cfg.data, 'test'):
            cfg.data.test.test_mode = True
        if hasattr(cfg.data, 'val'):
            cfg.data.val.test_mode = True
    
    # Build the dataset
    dataset = build_dataset(cfg.data.test)
    
    # Build the dataloader
    data_loader = build_dataloader(
        dataset,
        samples_per_gpu=1,  # Use batch size 1 for inference
        workers_per_gpu=cfg.data.workers_per_gpu,
        dist=distributed,
        shuffle=False,
    )
    
    # Build the model and load checkpoint
    model = build_model(cfg.model)
    fp16_cfg = cfg.get('fp16', None)
    if fp16_cfg is not None:
        wrap_fp16_model(model)
    
    # Load checkpoint
    checkpoint = load_checkpoint(model, args.checkpoint, map_location='cpu')
    
    # Fuse conv and bn if specified
    if args.fuse_conv_bn:
        model = fuse_conv_bn(model)
    
    # Set model to evaluation mode
    model.eval()
    
    # Prepare for test - use single GPU or distributed testing
    if not distributed:
        model = MMDataParallel(model, device_ids=[0])
        
        # Measure FPS if requested
        if args.fps:
            warmup = 5
            times = []
            total_samples = len(data_loader)
            
            # Warmup
            for i, data in enumerate(data_loader):
                if i >= warmup:
                    break
                with torch.no_grad():
                    model(return_loss=False, rescale=True, **data)
            
            # Actual timing
            prog_bar = mmcv.ProgressBar(total_samples)
            for i, data in enumerate(data_loader):
                torch.cuda.synchronize()
                start_time = time.time()
                
                with torch.no_grad():
                    result = model(return_loss=False, rescale=True, **data)
                
                torch.cuda.synchronize()
                elapsed_time = time.time() - start_time
                times.append(elapsed_time)
                
                prog_bar.update()
            
            # Report FPS
            if len(times) > 0:
                fps = 1.0 / np.mean(times)
                print(f"\nAverage FPS: {fps:.2f} ({len(times)} samples, {np.mean(times)*1000:.2f}ms per frame)")
            
            # Run inference again to get all results
            results = single_gpu_test(model, data_loader)
        else:
            # Regular inference
            results = single_gpu_test(model, data_loader)
    else:
        # Distributed testing
        model = MMDistributedDataParallel(
            model.cuda(),
            device_ids=[torch.cuda.current_device()],
            broadcast_buffers=False,
        )
        results = multi_gpu_test(model, data_loader, args.tmpdir, args.gpu_collect)
    
    # Only perform result formatting and visualization on rank 0
    rank, _ = get_dist_info()
    if rank == 0:
        # Create output directory
        os.makedirs(args.output_dir, exist_ok=True)
        
        # Format results to the standard format
        result_files, tmp_dir = dataset.format_results(
            results, jsonfile_prefix=os.path.join(args.output_dir, 'results'))
        
        print(f"Results saved to {args.output_dir}")
        
        # Visualize results if requested
        if args.show or args.show_dir:
            # Use show_dir if specified, otherwise use output_dir
            vis_dir = args.show_dir if args.show_dir else os.path.join(args.output_dir, 'visualization')
            os.makedirs(vis_dir, exist_ok=True)
            
            prog_bar = mmcv.ProgressBar(len(results))
            for idx, result in enumerate(results):
                # Get raw data for visualization
                data_info = dataset.data_infos[idx]
                frame_id = data_info.get('frame_id', str(idx).zfill(10))
                segment_id = data_info.get('segment_id', '')
                
                # Create a subfolder for each segment if segment_id is available
                if segment_id:
                    segment_dir = os.path.join(vis_dir, segment_id)
                    os.makedirs(segment_dir, exist_ok=True)
                    prefix = os.path.join(segment_dir, frame_id)
                else:
                    prefix = os.path.join(vis_dir, frame_id)
                
                # Get camera image and transforms for camera visualization
                img = None
                camera_intrinsics = None
                lidar2camera = None
                
                # Find front camera image and intrinsics
                if hasattr(dataset, 'get_data_info'):
                    data_info = dataset.get_data_info(idx)
                    
                    # Get image paths
                    if 'image_paths' in data_info and data_info['image_paths']:
                        front_cam_idx = 1  # Assuming front camera is index 1
                        if len(data_info['image_paths']) > front_cam_idx:
                            front_img_path = data_info['image_paths'][front_cam_idx]
                            if front_img_path and os.path.exists(front_img_path):
                                img = cv2.imread(front_img_path)
                                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                    
                    # Get camera intrinsics and lidar2camera transform
                    if 'camera_intrinsics' in data_info and 'lidar2camera' in data_info:
                        if len(data_info['camera_intrinsics']) > front_cam_idx:
                            camera_intrinsics = data_info['camera_intrinsics'][front_cam_idx]
                            lidar2camera = data_info['lidar2camera'][front_cam_idx]
                
                # Visualize results using the new visualization module
                visualize_lane_detection_results(
                    result,
                    img=img,
                    camera_intrinsics=camera_intrinsics,
                    lidar2camera=lidar2camera,
                    point_cloud_range=dataset.point_cloud_range,
                    output_dir=os.path.dirname(prefix),
                    prefix=os.path.basename(prefix),
                    lane_classes=dataset.LANE_CLASSES,
                    show_score_thr=args.show_score_thr,
                )
                
                prog_bar.update()
        
        # Clean up temporary directory if created
        if tmp_dir is not None:
            tmp_dir.cleanup()
    
    print("Inference completed successfully!")


if __name__ == '__main__':
    main() 