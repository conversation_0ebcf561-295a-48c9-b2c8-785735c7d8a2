#!/bin/bash

# Process 3D lane detection dataset

# Default parameters
DATASET_ROOT="/path/to/custom_3d_lane_dataset"
OUTPUT_DIR="data/lane_data"
LANE_TYPES_FILE="configs/lane/lane_types_mapping.yaml"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --dataset-root)
      DATASET_ROOT="$2"
      shift 2
      ;;
    --output-dir)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --lane-types-file)
      LANE_TYPES_FILE="$2"
      shift 2
      ;;
    --skip-invalid)
      SKIP_INVALID="--skip-invalid"
      shift
      ;;
    --filter-min-points)
      FILTER_MIN_POINTS="--filter-min-points $2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo ""
      echo "Options:"
      echo "  --dataset-root      Root directory of the dataset"
      echo "  --output-dir        Output directory for pickle files"
      echo "  --lane-types-file   Path to lane types mapping YAML file"
      echo "  --skip-invalid      Skip frames with missing data or invalid annotations"
      echo "  --filter-min-points Minimum number of points required for a valid lane"
      echo "  --help              Display this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Run the data processing script
python tools/process_lane_dataset_pkl.py \
  --dataset-root "$DATASET_ROOT" \
  --output-dir "$OUTPUT_DIR" \
  --lane-types-file "$LANE_TYPES_FILE" \
  --splits train val test \
  --cam-list 120_front 120_left 120_right 120_back right_back left_back \
  --point-cloud-range -81.6 -48 -1 97.6 48 3.0 \
  $SKIP_INVALID $FILTER_MIN_POINTS

echo "Dataset processing complete. Output files:"
echo "  - $OUTPUT_DIR/train_annotations.pkl"
echo "  - $OUTPUT_DIR/val_annotations.pkl"
echo "  - $OUTPUT_DIR/test_info.pkl"