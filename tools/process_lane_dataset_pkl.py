#!/usr/bin/env python
"""
Process a 3D lane detection dataset and generate pickle files for training, validation, and testing.

This script processes a custom 3D lane detection dataset with the following structure:
```
custom_3d_lane_dataset/
├── train/
│   ├── segment_001/
│   │   ├── images/
│   │   │   ├── 60_front
│   │   │   ├── 120_front
|   |   |   |       |——timestamp_01.jpg
│   │   │   ├── 120_left
│   │   │   ├── 120_right
│   │   │   ├── 120_back
│   │   │   ├── right_back
│   │   │   └── left_back
│   │   ├── lidar/
│   │   │     ├── timestamp_01.pcd
│   │   ├── depth_maps/   (optional)
│   │   │       |——timestamp_01.png
│   │   ├── annotations/
│   │   │   ├── timestamp_01.json
│   │   ├── pose.txt
│   │   └── calibration.json
│   └── ...
├── val/
│   └── ...
└── test/
    └── ...
```

The script generates three pickle files:
- train_annotations.pkl (or train_annotations_without_depth_maps.pkl): Contains data for training
- val_annotations.pkl (or val_annotations_without_depth_maps.pkl): Contains data for validation
- test_info.pkl (or test_info_without_depth_maps.pkl): Contains data for testing (may not include ground truth annotations)
"""

import argparse
import json
import os
import os.path as osp
import pickle
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import tqdm
import yaml

# Fix to use the proper import path for utility functions
import sys
sys.path.append(str(Path(__file__).parent))
from utils.lane_data_utils import (check_file_exists, find_valid_camera_paths, 
                                validate_lane_points, validate_calibration,
                                normalize_lane_data, fix_lane_coordinates)


def parse_args():
    parser = argparse.ArgumentParser(description='Process 3D lane detection dataset')
    parser.add_argument('--dataset-root', type=str, required=True,
                        help='Root directory of the 3D lane detection dataset')
    parser.add_argument('--output-dir', type=str, required=True,
                        help='Output directory for pickle files')
    parser.add_argument('--splits', type=str, nargs='+', default=['train', 'val', 'test'],
                        help='Dataset splits to process')
    parser.add_argument('--cam-list', type=str, nargs='+', 
                        default=['60_front', '120_front', '120_left', '120_right', 
                                 '120_back', 'right_back', 'left_back'],
                        help='List of cameras to include')
    parser.add_argument('--lane-types-file', type=str, default=None,
                        help='Path to lane types mapping yaml file')
    parser.add_argument('--point-cloud-range', type=float, nargs=6, 
                        default=[-81.6, -48, -1, 97.6, 48, 3.0],
                        help='Point cloud range [x_min, y_min, z_min, x_max, y_max, z_max] (maintained for compatibility, not used for filtering)')
    parser.add_argument('--skip-invalid', action='store_true',
                        help='Skip frames with missing data or invalid annotations')
    parser.add_argument('--segment-filter', type=str, nargs='*', default=None,
                        help='Optional list of segment IDs to include. If not provided, all segments are processed.')
    parser.add_argument('--filter-min-points', type=int, default=0,
                        help='Filter out lanes with fewer than this many points')
    parser.add_argument('--require-depth-maps', action='store_true',
                        help='Require depth maps to be present for all frames (adds "without_depth_maps" to filename if not set)')
    parser.add_argument('--skip-frame', type=int, default=30,
                        help='Process every Nth frame (default: 1, process all frames)')
    return parser.parse_args()


def load_lane_type_mapping(file_path: str) -> Dict:
    """Load lane type mapping from YAML file.
    
    Args:
        file_path: Path to the YAML file containing lane type mapping
        
    Returns:
        Dictionary with lane type mapping information
    """
    if not file_path or not osp.exists(file_path):
        print(f"Warning: Lane type mapping file not found: {file_path}")
        print("Using default lane type mapping")
        return {
            'lane_type_mapping': [
                {'type_id': 0, 'type_name': 'white-solid', 'original_sqlite_value': 1},
                {'type_id': 1, 'type_name': 'white-dash', 'original_sqlite_value': 2},
            ]
        }
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lane_types = yaml.safe_load(f)
    
    return lane_types


def build_lane_type_mappings(lane_types: Dict) -> Tuple[Dict, Dict, Dict]:
    """Build lane type mappings from loaded lane type data.
    
    Args:
        lane_types: Loaded lane type mapping data
        
    Returns:
        Tuple of (name_to_id, id_to_name, orig_value_to_id) dictionaries
    """
    name_to_id = {}
    id_to_name = {}
    orig_value_to_id = {}
    
    for entry in lane_types.get('lane_type_mapping', []):
        type_id = entry.get('type_id')
        type_name = entry.get('type_name')
        original_value = entry.get('original_sqlite_value')
        
        if type_id is not None and type_name:
            name_to_id[type_name] = type_id
            id_to_name[type_id] = type_name
            
        if type_id is not None and original_value is not None:
            orig_value_to_id[original_value] = type_id
    
    return name_to_id, id_to_name, orig_value_to_id


def find_segments(dataset_root: str, split: str, segment_filter: Optional[List[str]] = None) -> List[str]:
    """Find all segment directories in the specified split.
    
    Args:
        dataset_root: Root directory of the dataset
        split: Dataset split (train, val, test)
        segment_filter: Optional list of segment IDs to include
        
    Returns:
        List of segment paths
    """
    split_dir = osp.join(dataset_root, split)
    if not osp.exists(split_dir):
        print(f"Warning: Split directory not found: {split_dir}")
        return []
    
    segment_dirs = []
    for item in os.listdir(split_dir):
        item_path = osp.join(split_dir, item)
        if osp.isdir(item_path) and (segment_filter is None or item in segment_filter):
            segment_dirs.append(item_path)
    
    return segment_dirs


def load_calibration(calib_path: str) -> Dict:
    """Load calibration data from JSON file.
    
    Args:
        calib_path: Path to calibration.json file
        
    Returns:
        Dictionary with calibration data
    """
    if not check_file_exists(calib_path):
        return {}
    
    with open(calib_path, 'r') as f:
        return json.load(f)


def validate_all_camera_calibrations(calib_data: Dict, cam_list: List[str]) -> Tuple[bool, List[str]]:
    """Validate calibration data for all cameras in cam_list.
    
    Args:
        calib_data: Dictionary containing calibration data
        cam_list: List of camera names to validate
        
    Returns:
        Tuple of (is_valid, missing_cameras)
    """
    if not calib_data:
        return False, cam_list
    
    missing_cameras = []
    
    for cam in cam_list:
        # Try both underscore and dash formats
        cam_variants = [cam, cam.replace('_', '-'), cam.replace('-', '_')]
        found = False
        
        for cam_variant in cam_variants:
            if cam_variant in calib_data:
                cam_data = calib_data[cam_variant]
                # Check for essential calibration parameters
                required_keys = ['intrinsic', 'extrinsic', 'width', 'height']
                if all(key in cam_data for key in required_keys):
                    found = True
                    break
        
        if not found:
            missing_cameras.append(cam)
    
    return len(missing_cameras) == 0, missing_cameras


def process_annotation_file(
    ann_path: str, 
    name_to_id: Dict[str, int],
    filter_min_points: int = 0
) -> Dict:
    """Process a single annotation file.
    
    Args:
        ann_path: Path to annotation JSON file
        name_to_id: Lane type name to ID mapping
        filter_min_points: Filter out lanes with fewer than this many points
        
    Returns:
        Dictionary with processed annotation data
    """
    if not check_file_exists(ann_path):
        return {}
    
    with open(ann_path, 'r') as f:
        ann_data = json.load(f)
    
    timestamp = ann_data.get('timestamp', Path(ann_path).stem)
    sensors = ann_data.get('sensors', {})
    
    # Process lane lines
    lane_lines = []
    for lane in ann_data.get('lane_lines', []):
        # Normalize lane data
        lane = normalize_lane_data(lane, name_to_id)
        if not lane:
            continue
            
        # Fix common issues with lane coordinates
        lane = fix_lane_coordinates(lane)
        
        # Get XYZ and UV points
        xyz_points = lane.get('xyz', [])
        uv_points = lane.get('uv', [])
        
        # Validate lane points
        is_valid, filtered_xyz, filtered_uv = validate_lane_points(
            xyz_points, uv_points, min_points=filter_min_points
        )
        
        if not is_valid:
            continue
            
        # Create processed lane data with validated points
        processed_lane = {
            'type_name': lane.get('type_name', ''),
            'type_id': lane.get('type_id', 0),
            'xyz': filtered_xyz,
            'attribute': lane.get('attribute', 0)
        }
        
        # Add optional fields if present
        if filtered_uv is not None:
            processed_lane['uv'] = filtered_uv
        if 'visibility' in lane:
            processed_lane['visibility'] = lane['visibility']
        if 'track_id' in lane:
            processed_lane['track_id'] = lane['track_id']
            
        lane_lines.append(processed_lane)
    
    return {
        'timestamp': timestamp,
        'sensors': sensors,
        'lane_lines': lane_lines
    }


def process_segment(
    segment_path: str, 
    cam_list: List[str],
    lane_type_mappings: Tuple[Dict, Dict, Dict],
    split: str,
    dataset_root: str,
    filter_min_points: int = 0,
    skip_invalid: bool = True,
    require_depth_maps: bool = False,
    skip_frame: int = 1
) -> Tuple[List[Dict], bool]:
    """Process a segment directory and extract frame information.
    
    Args:
        segment_path: Path to segment directory
        cam_list: List of camera names to include
        lane_type_mappings: Tuple of lane type mapping dictionaries
        split: Dataset split (train, val, test)
        dataset_root: Root directory of the dataset
        filter_min_points: Filter out lanes with fewer than this many points
        skip_invalid: Skip frames with missing data or invalid annotations
        require_depth_maps: Whether depth maps are required for all frames
        skip_frame: Process every Nth frame (default: 1, process all frames)
        
    Returns:
        Tuple of (List of frame information dictionaries, has_depth_maps)
    """
    segment_id = osp.basename(segment_path)
    calib_path = osp.join(segment_path, 'calibration.json')
    calib_data = load_calibration(calib_path)
    
    # Validate calibration data for all cameras
    calib_valid, missing_cameras = validate_all_camera_calibrations(calib_data, cam_list)
    if not calib_valid and skip_invalid:
        print(f"Warning: Invalid or missing calibration data for segment {segment_id}")
        print(f"Missing calibrations for cameras: {missing_cameras}")
        return [], False
    elif not calib_valid:
        print(f"Warning: Missing calibrations for cameras in segment {segment_id}: {missing_cameras}")
    
    # Get all annotation files in the segment
    ann_dir = osp.join(segment_path, 'annotations')
    if not check_file_exists(ann_dir, verbose=True):
        return [], False
    
    ann_files = sorted([f for f in os.listdir(ann_dir) if f.endswith('.json')])
    
    # Apply skip_frame parameter - select every Nth frame
    if skip_frame > 1:
        ann_files = ann_files[::skip_frame]
    
    name_to_id, id_to_name, orig_value_to_id = lane_type_mappings
    
    frames_info = []
    segment_has_depth_maps = False
    
    # Check if depth_maps directory exists for this segment
    depth_maps_dir = osp.join(segment_path, 'depth_maps')
    depth_maps_dir_exists = osp.exists(depth_maps_dir) and osp.isdir(depth_maps_dir)
    
    for ann_file in ann_files:
        ann_path = osp.join(ann_dir, ann_file)
        frame_id = osp.splitext(ann_file)[0]
        
        # Process annotation data
        ann_data = process_annotation_file(
            ann_path, name_to_id, filter_min_points
        )
        
        if not ann_data and skip_invalid:
            continue
            
        # Check if lidar file exists
        lidar_path = osp.join(segment_path, 'lidar', f"{frame_id}.pcd")
        if not check_file_exists(lidar_path, verbose=False) and skip_invalid:
            continue
            
        # Find camera paths using utility function
        cam_paths = find_valid_camera_paths(segment_path, frame_id, cam_list, dataset_root)
        
        # Skip if no camera images found (if required)
        if not cam_paths and skip_invalid:
            continue
        
        # Create frame info with camera paths clearly recorded
        frame_info = {
            'timestamp': ann_data.get('timestamp', frame_id),
            'segment_id': segment_id,
            'frame_id': frame_id,
            'cam_paths': cam_paths,  # Dictionary mapping camera name to relative path
            'lidar_path': osp.relpath(lidar_path, dataset_root),
            'calib_path': osp.relpath(calib_path, dataset_root)
        }
        
        # Check for dense depth maps (optional, only for front cameras)
        frame_has_depth_map = False
        if split in ['train', 'val'] and depth_maps_dir_exists:
            # Check for depth maps for front cameras
            front_cameras = ['60_front', '120_front']
            for front_cam in front_cameras:
                if front_cam in cam_paths or front_cam.replace('_', '-') in cam_paths:
                    depth_map_path = osp.join(depth_maps_dir, f"{frame_id}.png")
                    if check_file_exists(depth_map_path, verbose=False):
                        frame_info['depth_map_path'] = osp.relpath(depth_map_path, dataset_root)
                        frame_has_depth_map = True
                        segment_has_depth_maps = True
                        break
        
        # Skip frame if depth maps are required but not found
        if require_depth_maps and split in ['train', 'val'] and not frame_has_depth_map:
            if skip_invalid:
                continue
        
        # Add annotations if available and not test set
        if ann_data.get('lane_lines') and split != 'test':
            frame_info['annos'] = ann_data.get('lane_lines', [])
            
        frames_info.append(frame_info)
    
    return frames_info, segment_has_depth_maps


def process_dataset(args):
    """Process the entire dataset and generate pickle files.
    
    Args:
        args: Command line arguments
    """
    # Load lane type mapping
    lane_types = load_lane_type_mapping(args.lane_types_file)
    lane_type_mappings = build_lane_type_mappings(lane_types)
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Process each split
    for split in args.splits:
        print(f"Processing {split} split...")
        
        segments = find_segments(args.dataset_root, split, args.segment_filter)
        print(f"Found {len(segments)} segments in {split} split")
        
        all_frames = []
        dataset_has_depth_maps = False
        
        for segment_path in tqdm.tqdm(segments):
            frames_info, segment_has_depth_maps = process_segment(
                segment_path,
                args.cam_list,
                lane_type_mappings,
                split,
                args.dataset_root,
                args.filter_min_points,
                args.skip_invalid,
                args.require_depth_maps,
                args.skip_frame
            )
            all_frames.extend(frames_info)
            if segment_has_depth_maps:
                dataset_has_depth_maps = True
        
        print(f"Processed {len(all_frames)} frames in {split} split")
        
        # Generate output filename based on depth map availability
        base_name = f"{split}_{'annotations' if split != 'test' else 'info'}"
        if args.skip_frame > 1:
            base_name += f"_skip{args.skip_frame}"
            
        if not args.require_depth_maps and not dataset_has_depth_maps and split in ['train', 'val']:
            output_file = f"{base_name}_without_depth_maps.pkl"
        else:
            output_file = f"{base_name}.pkl"
        
        output_path = osp.join(args.output_dir, output_file)
        
        with open(output_path, 'wb') as f:
            pickle.dump(all_frames, f)
        
        print(f"Saved {output_path}")
        if split in ['train', 'val']:
            print(f"Depth maps {'included' if dataset_has_depth_maps else 'not included'} in dataset")
        
        # Validate output by checking a few samples
        print(f"Validating output for {split} split...")
        with open(output_path, 'rb') as f:
            loaded_data = pickle.load(f)
        
        # Validate the loaded frames data
        frames = loaded_data
        print(f"  Loaded {len(frames)} frames")
        
        # Validate a few random frames
        if frames:
            sample_frames = frames[:min(3, len(frames))]
            for i, frame in enumerate(sample_frames):
                print(f"  Sample frame {i+1}: {frame.get('segment_id', 'unknown')}/{frame.get('frame_id', 'unknown')}")
                print(f"    Cameras: {list(frame.get('cam_paths', {}).keys())}")
                if 'depth_map_path' in frame:
                    print(f"    Has depth map: {frame['depth_map_path']}")
                if 'annos' in frame:
                    print(f"    Annotations: {len(frame['annos'])} lanes")
    
    print("Finished processing dataset")


def main():
    args = parse_args()
    process_dataset(args)


if __name__ == '__main__':
    main()