import pickle
import argparse
from collections import defaultdict

def analyze_lane_annotations(pkl_path):
    """
    分析车道线标注pkl文件的统计信息
    
    Args:
        pkl_path: 训练集pkl文件路径
    """
    # 加载pkl文件
    with open(pkl_path, 'rb') as f:
        frames = pickle.load(f)
    
    # 初始化统计变量
    total_frames = 0
    total_lanes = 0
    lanes_per_frame = []
    type_mapping = {}
    type_counter = defaultdict(int)
    
    # 遍历所有帧
    for frame in frames:
        total_frames += 1
        
        # 统计当前帧的车道线
        if 'annos' in frame:
            frame_lanes = len(frame['annos'])
            lanes_per_frame.append(frame_lanes)
            total_lanes += frame_lanes
            
            # 遍历当前帧的所有车道线
            for lane in frame['annos']:
                type_name = lane.get('type_name', 'unknown')
                type_id = lane.get('type_id', -1)
                
                # 记录类型映射关系
                if type_name not in type_mapping:
                    type_mapping[type_name] = type_id
                
                # 统计类型出现次数
                type_counter[(type_name, type_id)] += 1
    
    # 输出统计结果
    print(f"总帧数: {total_frames}")
    print(f"总车道线数量: {total_lanes}")
    print(f"平均每帧车道线数量: {total_lanes/total_frames:.2f}")
    print("\n车道线类型分布:")
    for (type_name, type_id), count in sorted(type_counter.items(), key=lambda x: x[1], reverse=True):
        print(f"  {type_name} (ID: {type_id}): {count}条")
    
    print("\n车道线类型映射关系:")
    for type_name, type_id in type_mapping.items():
        print(f"  {type_name} -> {type_id}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='分析车道线标注pkl文件')
    parser.add_argument('pkl_file', type=str, help='训练集pkl文件路径')
    args = parser.parse_args()
    
    analyze_lane_annotations(args.pkl_file) 