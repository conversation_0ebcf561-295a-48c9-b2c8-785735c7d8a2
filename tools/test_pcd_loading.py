#!/usr/bin/env python
"""
Test script for PCD file loading functionality.
This script creates sample PCD files in both ASCII and binary formats and tests the loading.
"""

import os
import struct
import tempfile
import sys
from pathlib import Path

import numpy as np

# Add the project path to import mmdet3d modules
sys.path.append(str(Path(__file__).parent.parent))

def create_sample_ascii_pcd(file_path, points_data):
    """Create a sample ASCII format PCD file.
    
    Args:
        file_path (str): Path to save the PCD file
        points_data (list): List of point data [x, y, z, intensity]
    """
    with open(file_path, 'w') as f:
        # Write PCD header
        f.write("# .PCD v0.7 - Point Cloud Data file format\n")
        f.write("VERSION 0.7\n")
        f.write("FIELDS x y z intensity\n")
        f.write("SIZE 4 4 4 4\n")
        f.write("TYPE F F F F\n")
        f.write("COUNT 1 1 1 1\n")
        f.write(f"WIDTH {len(points_data)}\n")
        f.write("HEIGHT 1\n")
        f.write("VIEWPOINT 0 0 0 1 0 0 0\n")
        f.write(f"POINTS {len(points_data)}\n")
        f.write("DATA ascii\n")
        
        # Write point data
        for point in points_data:
            f.write(f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {point[3]:.6f}\n")


def create_sample_binary_pcd(file_path, points_data):
    """Create a sample binary format PCD file.
    
    Args:
        file_path (str): Path to save the PCD file
        points_data (list): List of point data [x, y, z, intensity]
    """
    with open(file_path, 'wb') as f:
        # Write PCD header (in text format)
        header = "# .PCD v0.7 - Point Cloud Data file format\n"
        header += "VERSION 0.7\n"
        header += "FIELDS x y z intensity\n"
        header += "SIZE 4 4 4 4\n"
        header += "TYPE F F F F\n"
        header += "COUNT 1 1 1 1\n"
        header += f"WIDTH {len(points_data)}\n"
        header += "HEIGHT 1\n"
        header += "VIEWPOINT 0 0 0 1 0 0 0\n"
        header += f"POINTS {len(points_data)}\n"
        header += "DATA binary\n"
        
        f.write(header.encode('utf-8'))
        
        # Write binary point data
        for point in points_data:
            # Pack as 4 floats (x, y, z, intensity)
            binary_data = struct.pack('<ffff', point[0], point[1], point[2], point[3])
            f.write(binary_data)


def test_pcd_loading():
    """Test PCD loading functionality for both ASCII and binary formats."""
    
    # Sample point cloud data
    test_points = [
        [0.0, 0.0, 0.0, 10.0],
        [1.0, 0.0, 0.0, 15.0],
        [0.0, 1.0, 0.0, 20.0],
        [0.0, 0.0, 1.0, 25.0],
        [1.0, 1.0, 1.0, 30.0]
    ]
    
    print("Testing PCD file loading functionality...")
    print(f"Test data: {len(test_points)} points")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test files
        ascii_pcd_path = os.path.join(temp_dir, 'test_ascii.pcd')
        binary_pcd_path = os.path.join(temp_dir, 'test_binary.pcd')
        
        print(f"\nCreating test PCD files in: {temp_dir}")
        
        # Create ASCII PCD file
        create_sample_ascii_pcd(ascii_pcd_path, test_points)
        print(f"✓ Created ASCII PCD: {ascii_pcd_path}")
        
        # Create binary PCD file
        create_sample_binary_pcd(binary_pcd_path, test_points)
        print(f"✓ Created binary PCD: {binary_pcd_path}")
        
        # Test loading with the LoadPointsFromFile class
        try:
            from mmdet3d.datasets.pipelines.loading import LoadPointsFromFile
            
            # Create loader instance
            loader = LoadPointsFromFile(
                coord_type='LIDAR',
                load_dim=4,
                use_dim=[0, 1, 2, 3]
            )
            
            # Test ASCII loading
            print(f"\nTesting ASCII PCD loading...")
            ascii_points, ascii_labels = loader._load_points(ascii_pcd_path)
            print(f"✓ ASCII PCD loaded successfully: shape {ascii_points.shape}")
            print(f"  First point: {ascii_points[0]}")
            print(f"  Last point: {ascii_points[-1]}")
            
            # Test binary loading
            print(f"\nTesting binary PCD loading...")
            binary_points, binary_labels = loader._load_points(binary_pcd_path)
            print(f"✓ Binary PCD loaded successfully: shape {binary_points.shape}")
            print(f"  First point: {binary_points[0]}")
            print(f"  Last point: {binary_points[-1]}")
            
            # Verify data consistency
            print(f"\nVerifying data consistency...")
            if ascii_points.shape == binary_points.shape:
                print(f"✓ Shapes match: {ascii_points.shape}")
            else:
                print(f"✗ Shape mismatch: ASCII {ascii_points.shape} vs Binary {binary_points.shape}")
                
            # Check if points are approximately equal
            if np.allclose(ascii_points, binary_points, rtol=1e-5):
                print(f"✓ Point data matches between ASCII and binary formats")
            else:
                print(f"✗ Point data differs between formats")
                print(f"  ASCII max value: {ascii_points.max()}")
                print(f"  Binary max value: {binary_points.max()}")
            
            print(f"\n✓ All tests passed successfully!")
            
        except ImportError as e:
            print(f"✗ Failed to import LoadPointsFromFile: {e}")
            print("Make sure you're running this from the project root directory")
        except Exception as e:
            print(f"✗ Error during testing: {e}")
            import traceback
            traceback.print_exc()


def main():
    """Main function to run the tests."""
    test_pcd_loading()


if __name__ == '__main__':
    main() 