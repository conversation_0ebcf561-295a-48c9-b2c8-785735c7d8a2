#!/usr/bin/env python
"""
Test script for validating the lane dataset processing functionality.
This script can be used to verify that the process_lane_dataset_pkl.py works correctly.
"""

import argparse
import json
import os
import os.path as osp
import pickle
import tempfile
from pathlib import Path
from typing import Dict, List

def create_test_dataset(base_dir: str) -> str:
    """Create a minimal test dataset structure for validation.
    
    Args:
        base_dir: Base directory to create test dataset
        
    Returns:
        Path to created test dataset
    """
    dataset_root = osp.join(base_dir, 'test_3d_lane_dataset')
    
    # Create dataset structure
    for split in ['train', 'val']:
        split_dir = osp.join(dataset_root, split)
        
        # Create a test segment
        segment_dir = osp.join(split_dir, 'segment_001')
        os.makedirs(segment_dir, exist_ok=True)
        
        # Create image directories
        image_base = osp.join(segment_dir, 'images')
        cameras = ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
        
        for cam in cameras:
            cam_dir = osp.join(image_base, cam)
            os.makedirs(cam_dir, exist_ok=True)
            
            # Create a dummy image
            img_path = osp.join(cam_dir, 'timestamp_01.jpg')
            with open(img_path, 'w') as f:
                f.write('dummy_image_content')
        
        # Create lidar directory
        lidar_dir = osp.join(segment_dir, 'lidar')
        os.makedirs(lidar_dir, exist_ok=True)
        lidar_path = osp.join(lidar_dir, 'timestamp_01.pcd')
        with open(lidar_path, 'w') as f:
            f.write('dummy_lidar_content')
        
        # Create annotations directory
        ann_dir = osp.join(segment_dir, 'annotations')
        os.makedirs(ann_dir, exist_ok=True)
        
        # Create annotation file
        ann_data = {
            'timestamp': 'timestamp_01',
            'sensors': {},
            'lane_lines': [
                {
                    'type_name': 'white-solid',
                    'type_id': 0,
                    'xyz': [[0.0, 0.0, 0.0], [1.0, 0.0, 0.0], [2.0, 0.0, 0.0]],
                    'uv': [[100, 200], [110, 210], [120, 220]],
                    'attribute': 0,
                    'track_id': '1'
                }
            ]
        }
        
        ann_path = osp.join(ann_dir, 'timestamp_01.json')
        with open(ann_path, 'w') as f:
            json.dump(ann_data, f, indent=2)
        
        # Create calibration file
        calib_data = {
            '60_front': {
                'intrinsic': [[1000.0, 0.0, 640.0], [0.0, 1000.0, 360.0], [0.0, 0.0, 1.0]],
                'extrinsic': [[1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0]],
                'width': 1280,
                'height': 720
            },
            '120_front': {
                'intrinsic': [[1000.0, 0.0, 640.0], [0.0, 1000.0, 360.0], [0.0, 0.0, 1.0]],
                'extrinsic': [[1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0]],
                'width': 1280,
                'height': 720
            },
            '120_left': {
                'intrinsic': [[1000.0, 0.0, 640.0], [0.0, 1000.0, 360.0], [0.0, 0.0, 1.0]],
                'extrinsic': [[1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0]],
                'width': 1280,
                'height': 720
            },
            '120_right': {
                'intrinsic': [[1000.0, 0.0, 640.0], [0.0, 1000.0, 360.0], [0.0, 0.0, 1.0]],
                'extrinsic': [[1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0]],
                'width': 1280,
                'height': 720
            },
            '120_back': {
                'intrinsic': [[1000.0, 0.0, 640.0], [0.0, 1000.0, 360.0], [0.0, 0.0, 1.0]],
                'extrinsic': [[1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0]],
                'width': 1280,
                'height': 720
            },
            'right_back': {
                'intrinsic': [[1000.0, 0.0, 640.0], [0.0, 1000.0, 360.0], [0.0, 0.0, 1.0]],
                'extrinsic': [[1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0]],
                'width': 1280,
                'height': 720
            },
            'left_back': {
                'intrinsic': [[1000.0, 0.0, 640.0], [0.0, 1000.0, 360.0], [0.0, 0.0, 1.0]],
                'extrinsic': [[1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0]],
                'width': 1280,
                'height': 720
            },
            'hesai_at128_front_extrinsic': {
                'extrinsic': [[1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0], [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0]]
            }
        }
        
        calib_path = osp.join(segment_dir, 'calibration.json')
        with open(calib_path, 'w') as f:
            json.dump(calib_data, f, indent=2)
    
    return dataset_root


def test_process_dataset():
    """Test the dataset processing functionality."""
    print("Creating test dataset...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test dataset
        test_dataset_root = create_test_dataset(temp_dir)
        output_dir = osp.join(temp_dir, 'output')
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"Test dataset created at: {test_dataset_root}")
        print(f"Output directory: {output_dir}")
        
        # Import and run the processing function
        import sys
        sys.path.append(str(Path(__file__).parent))
        
        from process_lane_dataset_pkl import process_dataset
        
        # Create mock args
        class Args:
            def __init__(self):
                self.dataset_root = test_dataset_root
                self.output_dir = output_dir
                self.splits = ['train', 'val']
                self.cam_list = ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
                self.lane_types_file = None
                self.point_cloud_range = [-81.6, -48, -1, 97.6, 48, 3.0]
                self.skip_invalid = True
                self.segment_filter = None
                self.filter_min_points = 0
                self.require_depth_maps = False
        
        args = Args()
        
        print("Processing dataset...")
        process_dataset(args)
        
        # Validate outputs
        print("\nValidating outputs...")
        expected_files = [
            'train_annotations_without_depth_maps.pkl',
            'val_annotations_without_depth_maps.pkl'
        ]
        
        for expected_file in expected_files:
            file_path = osp.join(output_dir, expected_file)
            if osp.exists(file_path):
                print(f"✓ {expected_file} created successfully")
                
                # Load and validate content
                with open(file_path, 'rb') as f:
                    frames = pickle.load(f)
                
                print(f"  - Frames: {len(frames)}")
                
                if frames:
                    sample_frame = frames[0]
                    print(f"  - Sample frame cameras: {list(sample_frame.get('cam_paths', {}).keys())}")
                    print(f"  - Sample frame has annotations: {'annos' in sample_frame}")
            else:
                print(f"✗ {expected_file} not found")
        
        print("\nTest completed successfully!")


def parse_args():
    parser = argparse.ArgumentParser(description='Test lane dataset processing')
    parser.add_argument('--test-processing', action='store_true',
                        help='Run processing test with synthetic data')
    return parser.parse_args()


def main():
    args = parse_args()
    
    if args.test_processing:
        test_process_dataset()
    else:
        print("Use --test-processing to run the processing test")


if __name__ == '__main__':
    main() 