"""
Utility functions for processing 3D lane detection dataset.
"""

import json
import numpy as np
import os
import os.path as osp
from typing import Dict, List, Tuple, Optional, Union


def check_file_exists(file_path: str, verbose: bool = True) -> bool:
    """Check if file exists and warn if not found.
    
    Args:
        file_path: Path to file
        verbose: Whether to print a warning message if file not found
        
    Returns:
        <PERSON><PERSON><PERSON> indicating if file exists
    """
    exists = osp.exists(file_path)
    if not exists and verbose:
        print(f"Warning: File not found: {file_path}")
    return exists


def find_valid_camera_paths(
    segment_path: str, 
    frame_id: str, 
    cam_list: List[str],
    dataset_root: str
) -> Dict[str, str]:
    """Find valid camera image paths for a given frame.
    
    Args:
        segment_path: Path to segment directory
        frame_id: Frame ID or timestamp
        cam_list: List of camera names to search for
        dataset_root: Root directory of the dataset
        
    Returns:
        Dictionary mapping camera names to relative image paths
    """
    cam_paths = {}
    
    # Different naming conventions for images
    frame_names = [
        f"{frame_id}.jpg",
        f"{frame_id}.png",
        f"{frame_id}.jpeg"
    ]
    
    for cam in cam_list:
        found = False
        # Try both underscore and dash formats, and also handle special cases
        cam_variants = [cam, cam.replace('_', '-'), cam.replace('-', '_')]
        
        # Add additional variants for front cameras
        if 'front' in cam.lower():
            # Handle cases like 60-front, 120-front, etc.
            cam_variants.extend([
                cam.replace('_front', '-front'),
                cam.replace('-front', '_front')
            ])
        
        for cam_variant in cam_variants:
            # Try different file extensions
            for frame_name in frame_names:
                img_path = osp.join(segment_path, 'images', cam_variant, frame_name)
                if check_file_exists(img_path, verbose=False):
                    # Standardize camera name to underscore format in keys
                    cam_key = cam_variant.replace('-', '_')
                    cam_paths[cam_key] = osp.relpath(img_path, dataset_root)
                    found = True
                    break
            
            if found:
                break
    
    return cam_paths


def validate_lane_points(
    xyz_points: List[List[float]], 
    uv_points: Optional[List[List[float]]] = None,
    point_cloud_range: Optional[List[float]] = None,
    min_points: int = 2,
    min_length: float = 1.0
) -> Tuple[bool, List[List[float]], Optional[List[List[float]]]]:
    """Validate and clean lane points.
    
    Args:
        xyz_points: List of 3D points [x, y, z]
        uv_points: Optional list of 2D image points [u, v]
        point_cloud_range: Optional range filter [x_min, y_min, z_min, x_max, y_max, z_max]
        min_points: Minimum number of points required for a valid lane
        min_length: Minimum length of lane in meters
        
    Returns:
        Tuple of (is_valid, filtered_xyz, filtered_uv)
    """
    if not xyz_points or len(xyz_points) < min_points:
        return False, [], None
    
    # Convert to numpy for easier processing
    xyz_array = np.array(xyz_points, dtype=np.float32)
    
    # Filter by point cloud range if specified
    if point_cloud_range:
        x_min, y_min, z_min, x_max, y_max, z_max = point_cloud_range
        mask = ((xyz_array[:, 0] >= x_min) & (xyz_array[:, 0] <= x_max) &
                (xyz_array[:, 1] >= y_min) & (xyz_array[:, 1] <= y_max) &
                (xyz_array[:, 2] >= z_min) & (xyz_array[:, 2] <= z_max))
        
        if not np.any(mask):
            return False, [], None
            
        xyz_array = xyz_array[mask]
        
        # Filter UV points with the same mask if provided
        if uv_points:
            uv_array = np.array(uv_points, dtype=np.float32)
            if len(uv_array) >= len(mask):
                uv_array = uv_array[mask]
            else:
                # Handle case where UV points are fewer than XYZ points
                uv_array = uv_array[:len(xyz_array)]
    else:
        # If no range filtering, just ensure UV points match XYZ points in length
        if uv_points:
            uv_array = np.array(uv_points, dtype=np.float32)
            uv_array = uv_array[:len(xyz_array)]
    
    # Check if we have enough points after filtering
    if len(xyz_array) < min_points:
        return False, [], None
    
    # Check minimum lane length
    if min_length > 0:
        # Calculate Euclidean distances between consecutive points
        diffs = np.diff(xyz_array[:, :2], axis=0)  # Only consider x, y for length
        point_distances = np.sqrt(np.sum(diffs**2, axis=1))
        lane_length = np.sum(point_distances)
        
        if lane_length < min_length:
            return False, [], None
    
    # Remove duplicate points
    unique_points = []
    unique_indices = []
    
    for i, point in enumerate(xyz_array):
        is_duplicate = False
        for j, unique_point in enumerate(unique_points):
            # Check if points are very close (less than 1cm apart)
            if np.linalg.norm(point - unique_point) < 0.01:
                is_duplicate = True
                break
        
        if not is_duplicate:
            unique_points.append(point)
            unique_indices.append(i)
    
    if len(unique_points) < min_points:
        return False, [], None
    
    # Convert back to list for JSON serialization
    filtered_xyz = [point.tolist() for point in unique_points]
    
    # Filter UV points to match unique XYZ points
    filtered_uv = None
    if uv_points:
        filtered_uv = [uv_array[i].tolist() for i in unique_indices if i < len(uv_array)]
    
    return True, filtered_xyz, filtered_uv


def validate_calibration(calib_data: Dict, cam_list: Optional[List[str]] = None) -> bool:
    """Validate calibration data.
    
    Args:
        calib_data: Dictionary containing calibration data
        cam_list: Optional list of camera names to validate. If None, validates essential cameras only.
        
    Returns:
        Boolean indicating if calibration data is valid
    """
    # Check if calibration data is empty
    if not calib_data:
        return False
    
    # Essential cameras that must be present
    essential_cameras = ['120_front', 'hesai_at128_front_extrinsic']
    
    # Check for essential calibrations
    for cam_key in essential_cameras:
        cam_variants = [cam_key, cam_key.replace('_', '-'), cam_key.replace('-', '_')]
        found = False
        
        for variant in cam_variants:
            if variant in calib_data:
                found = True
                break
                
        if not found:
            print(f"Warning: Missing essential calibration for {cam_key}")
            return False
    
    # Check if front camera has valid intrinsic and extrinsic parameters
    front_cam_variants = ['120_front', '120-front']
    front_cam_data = None
    
    for variant in front_cam_variants:
        if variant in calib_data:
            front_cam_data = calib_data[variant]
            break
    
    if not front_cam_data:
        return False
    
    required_keys = ['intrinsic', 'extrinsic', 'width', 'height']
    for key in required_keys:
        if key not in front_cam_data:
            print(f"Warning: Missing {key} in front camera calibration")
            return False
    
    # If cam_list is provided, validate all cameras in the list
    if cam_list:
        for cam in cam_list:
            cam_variants = [cam, cam.replace('_', '-'), cam.replace('-', '_')]
            found = False
            
            for cam_variant in cam_variants:
                if cam_variant in calib_data:
                    cam_data = calib_data[cam_variant]
                    # Check for essential calibration parameters
                    if all(key in cam_data for key in required_keys):
                        found = True
                        break
            
            if not found:
                print(f"Warning: Missing calibration for camera {cam}")
                # Don't return False here, just warn - some cameras might be optional
    
    return True


def normalize_lane_data(lane_data: Dict, name_to_id: Dict) -> Dict:
    """Normalize and validate lane data.
    
    Args:
        lane_data: Dictionary containing lane data
        name_to_id: Mapping from lane type names to IDs
        
    Returns:
        Normalized lane data dictionary
    """
    normalized = {}
    
    # Ensure type information is consistent
    if 'type_id' in lane_data:
        normalized['type_id'] = lane_data['type_id']
    elif 'type_name' in lane_data and lane_data['type_name'] in name_to_id:
        normalized['type_id'] = name_to_id[lane_data['type_name']]
    else:
        # Default to first lane type if not specified
        normalized['type_id'] = list(name_to_id.values())[0] if name_to_id else 0
    
    # Ensure type_name is present
    if 'type_name' in lane_data:
        normalized['type_name'] = lane_data['type_name']
    
    # Ensure required fields are present
    if 'xyz' in lane_data:
        normalized['xyz'] = lane_data['xyz']
    else:
        # Lane must have 3D points
        return {}
    
    # Optional fields
    for key in ['uv', 'visibility', 'attribute', 'track_id']:
        if key in lane_data:
            normalized[key] = lane_data[key]
    
    # Default values for optional fields
    if 'attribute' not in normalized:
        normalized['attribute'] = 0
    
    if 'track_id' not in normalized and 'id' in lane_data:
        normalized['track_id'] = str(lane_data['id'])
    
    return normalized


def fix_lane_coordinates(lane_data: Dict) -> Dict:
    """Fix common issues with lane coordinates.
    
    Args:
        lane_data: Dictionary containing lane data
        
    Returns:
        Fixed lane data dictionary
    """
    fixed_data = lane_data.copy()
    
    # Ensure xyz is a list of lists
    if 'xyz' in fixed_data:
        xyz = fixed_data['xyz']
        if isinstance(xyz, list):
            # Fix individual points if needed
            for i, point in enumerate(xyz):
                if not isinstance(point, list):
                    # Convert single values to list
                    if isinstance(point, (int, float)):
                        if i > 0 and i < len(xyz) - 1:
                            # Interpolate from neighbors
                            prev_point = xyz[i-1]
                            next_point = xyz[i+1]
                            xyz[i] = [(p + n) / 2 for p, n in zip(prev_point, next_point)]
                        else:
                            # Can't interpolate, remove point
                            xyz[i] = None
                elif len(point) < 3:
                    # Add missing z coordinate if needed
                    if len(point) == 2:
                        point.append(0.0)  # Default z to ground plane
            
            # Remove None values and ensure all points are valid
            fixed_data['xyz'] = [p for p in xyz if p is not None and len(p) >= 3]
    
    # Similar checks for UV coordinates
    if 'uv' in fixed_data:
        uv = fixed_data['uv']
        if isinstance(uv, list):
            # Fix individual UV points
            for i, point in enumerate(uv):
                if not isinstance(point, list):
                    uv[i] = None
                elif len(point) < 2:
                    uv[i] = None
            
            # Remove None values
            fixed_data['uv'] = [p for p in uv if p is not None and len(p) >= 2]
            
            # Ensure UV points don't exceed XYZ points in length
            if 'xyz' in fixed_data and len(fixed_data['uv']) > len(fixed_data['xyz']):
                fixed_data['uv'] = fixed_data['uv'][:len(fixed_data['xyz'])]
    
    return fixed_data