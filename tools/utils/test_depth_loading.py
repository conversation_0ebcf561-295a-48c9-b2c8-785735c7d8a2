import os
import sys
import numpy as np
from mmcv import Config
import mmcv
import matplotlib.pyplot as plt

# Add project path to PYTHONPATH
project_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_dir not in sys.path:
    sys.path.append(project_dir)

from mmdet3d.datasets.pipelines import LoadDenseDepthMapFromFile

def visualize_depth(depth_map, name='depth'):
    """Visualize depth map."""
    plt.figure(figsize=(10, 6))
    plt.imshow(depth_map, cmap='plasma')
    plt.colorbar(label='Depth')
    plt.title(f'Depth Map - {name}')
    plt.savefig(f'depth_vis_{name}.png')
    plt.close()

def test_depth_loading(dataset_dir, frame_id):
    """Test depth map loading functionality."""
    # Construct paths for test
    segment_path = os.path.join(dataset_dir, 'train', 'segment_001')
    img_path = os.path.join(segment_path, 'images', '120_front', f'{frame_id}.jpg')
    depth_path = os.path.join(segment_path, 'depth_maps', f'{frame_id}.png')
    
    # Check if files exist
    print(f"Image path exists: {os.path.exists(img_path)}")
    print(f"Depth path exists: {os.path.exists(depth_path)}")
    
    # Create mock results dict
    results = {
        'image_paths': [img_path],
    }
    
    # Initialize and call the loader
    depth_loader = LoadDenseDepthMapFromFile(
        to_float32=True,
        color_type='unchanged',
        backend='cv2',
        depth_scale=256.0,
        default_depth=0.0,
        front_cam_name='120_front'
    )
    
    # Call the loader
    output_results = depth_loader(results)
    
    # Check results
    if 'depth_map' in output_results:
        print(f"Successfully loaded depth map!")
        print(f"Depth map shape: {output_results['depth_map'][0].shape}")
        print(f"Depth map dtype: {output_results['depth_map'][0].dtype}")
        print(f"Depth map min: {output_results['depth_map'][0].min()}")
        print(f"Depth map max: {output_results['depth_map'][0].max()}")
        
        # Visualize depth map
        visualize_depth(output_results['depth_map'][0], name=frame_id)
    else:
        print("Failed to load depth map!")

if __name__ == '__main__':
    # Set the dataset directory and frame ID
    dataset_dir = 'D:/Work_project/BevFusion_multimodel/multimodal_bevfusion/custom_3d_lane_dataset'
    frame_id = '000000'  # Change this to match an actual frame in your dataset
    
    # Run the test
    test_depth_loading(dataset_dir, frame_id)