#!/usr/bin/env python
# Copyright (c) OpenMMLab. All rights reserved.

import argparse
import os
import os.path as osp
import time

import mmcv
import torch
import numpy as np
import cv2
from mmcv import Config
from mmcv.runner import load_checkpoint, wrap_fp16_model

from mmdet3d.datasets import build_dataloader, build_dataset
from mmdet3d.models import build_model
from mmdet3d.utils import get_root_logger


def parse_args():
    parser = argparse.ArgumentParser(
        description='Test lane detection with lane grouping')
    parser.add_argument('config', help='test config file path')
    parser.add_argument('checkpoint', help='checkpoint file')
    parser.add_argument(
        '--work-dir',
        help='the dir to save logs and models',
        default=None)
    parser.add_argument(
        '--show-dir',
        help='directory where visualizations will be saved')
    parser.add_argument(
        '--compare',
        action='store_true',
        help='compare results with and without lane grouping')
    parser.add_argument('--local_rank', type=int, default=0)
    args = parser.parse_args()
    if 'LOCAL_RANK' not in os.environ:
        os.environ['LOCAL_RANK'] = str(args.local_rank)
    return args


def compute_distance(lane1, lane2):
    """Compute minimum distance between two lanes."""
    min_dist = float('inf')
    for pt1 in lane1:
        for pt2 in lane2:
            dist = np.sqrt((pt1[0] - pt2[0])**2 + (pt1[1] - pt2[1])**2)
            min_dist = min(min_dist, dist)
    return min_dist


def group_lanes(lanes, min_distance):
    """Group lanes based on minimum distance."""
    if not lanes:
        return []
    
    grouped_lanes = []
    used_indices = set()
    
    for i in range(len(lanes)):
        if i in used_indices:
            continue
        
        current_group = [lanes[i]]
        used_indices.add(i)
        
        for j in range(len(lanes)):
            if j in used_indices or i == j:
                continue
            
            # Check if lane j should be grouped with current group
            should_group = False
            for lane in current_group:
                dist = compute_distance(lane['points'], lanes[j]['points'])
                if dist < min_distance:
                    should_group = True
                    break
            
            if should_group:
                current_group.append(lanes[j])
                used_indices.add(j)
        
        # Add the highest confidence lane from this group
        best_lane = max(current_group, key=lambda x: x['score'])
        grouped_lanes.append(best_lane)
    
    return grouped_lanes


def visualize_lanes(model, data_loader, show_dir, compare=False):
    """Visualize lane detections with and without grouping."""
    model.eval()
    dataset = data_loader.dataset
    prog_bar = mmcv.ProgressBar(len(dataset))
    
    # Create colormap for visualization
    colors = [(0, 0, 0), (1, 0, 0), (0, 1, 0), (0, 0, 1), (1, 1, 0),
              (1, 0, 1), (0, 1, 1), (0.5, 0, 0), (0, 0.5, 0),
              (0, 0, 0.5), (0.5, 0.5, 0), (0.5, 0, 0.5), (0, 0.5, 0.5)]
    
    os.makedirs(show_dir, exist_ok=True)
    
    for i, data in enumerate(data_loader):
        with torch.no_grad():
            result = model(return_loss=False, rescale=True, **data)
        
        # Get BEV image for visualization
        if 'img_metas' in data:
            img_metas = data['img_metas'][0].data[0][0]
            xbound = img_metas['grid_conf']['xbound']
            ybound = img_metas['grid_conf']['ybound']
            
            # Create BEV canvas
            bev_height = int((ybound[1] - ybound[0]) / ybound[2])
            bev_width = int((xbound[1] - xbound[0]) / xbound[2])
            
            if compare:
                # Create two canvases side by side for comparison
                bev_img_original = np.zeros((bev_height, bev_width, 3), dtype=np.uint8)
                bev_img_grouped = np.zeros((bev_height, bev_width, 3), dtype=np.uint8)
                
                # First, visualize without grouping
                if 'lane' in result:
                    lane_result = result['lane']
                    for lane_idx, lane in enumerate(lane_result):
                        lane_class = int(lane['class_id'])
                        if lane_class >= len(colors):
                            lane_class = lane_class % len(colors)
                        color = tuple(int(c * 255) for c in colors[lane_class])
                        
                        pts = lane['points']
                        for pt_idx in range(len(pts) - 1):
                            x1 = int((pts[pt_idx][0] - xbound[0]) / xbound[2])
                            y1 = int((pts[pt_idx][1] - ybound[0]) / ybound[2])
                            x2 = int((pts[pt_idx + 1][0] - xbound[0]) / xbound[2])
                            y2 = int((pts[pt_idx + 1][1] - ybound[0]) / ybound[2])
                            
                            # Convert to image coordinate (origin at top-left)
                            y1 = bev_height - y1 - 1
                            y2 = bev_height - y2 - 1
                            
                            # Draw line on BEV image
                            cv2.line(bev_img_original, (x1, y1), (x2, y2), color, 2)
                
                # Then, apply manual grouping and visualize
                if 'lane' in result:
                    lane_result = result['lane']
                    # Get min_distance from the model's head configuration or use default
                    lane_group_min_distance = 1.0  # Default value
                    if hasattr(model, 'bbox_head') and hasattr(model.bbox_head, 'lane_group_min_distance'):
                        lane_group_min_distance = model.bbox_head.lane_group_min_distance
                    
                    # Apply grouping manually
                    grouped_lanes = group_lanes(lane_result, lane_group_min_distance)
                    
                    for lane_idx, lane in enumerate(grouped_lanes):
                        lane_class = int(lane['class_id'])
                        if lane_class >= len(colors):
                            lane_class = lane_class % len(colors)
                        color = tuple(int(c * 255) for c in colors[lane_class])
                        
                        pts = lane['points']
                        for pt_idx in range(len(pts) - 1):
                            x1 = int((pts[pt_idx][0] - xbound[0]) / xbound[2])
                            y1 = int((pts[pt_idx][1] - ybound[0]) / ybound[2])
                            x2 = int((pts[pt_idx + 1][0] - xbound[0]) / xbound[2])
                            y2 = int((pts[pt_idx + 1][1] - ybound[0]) / ybound[2])
                            
                            # Convert to image coordinate (origin at top-left)
                            y1 = bev_height - y1 - 1
                            y2 = bev_height - y2 - 1
                            
                            # Draw line on BEV image
                            cv2.line(bev_img_grouped, (x1, y1), (x2, y2), color, 2)
                
                # Add text labels to images
                cv2.putText(bev_img_original, "Original", (10, 30), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                cv2.putText(bev_img_grouped, "Grouped", (10, 30), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                # Combine images side by side
                combined_img = np.hstack((bev_img_original, bev_img_grouped))
                
                # Save visualization
                mmcv.imwrite(combined_img, osp.join(show_dir, f'lane_compare_{i:06d}.png'))
            else:
                # Just visualize the lanes with grouping done by the model
                bev_img = np.zeros((bev_height, bev_width, 3), dtype=np.uint8)
                
                if 'lane' in result:
                    lane_result = result['lane']
                    for lane_idx, lane in enumerate(lane_result):
                        lane_class = int(lane['class_id'])
                        if lane_class >= len(colors):
                            lane_class = lane_class % len(colors)
                        color = tuple(int(c * 255) for c in colors[lane_class])
                        
                        pts = lane['points']
                        for pt_idx in range(len(pts) - 1):
                            x1 = int((pts[pt_idx][0] - xbound[0]) / xbound[2])
                            y1 = int((pts[pt_idx][1] - ybound[0]) / ybound[2])
                            x2 = int((pts[pt_idx + 1][0] - xbound[0]) / xbound[2])
                            y2 = int((pts[pt_idx + 1][1] - ybound[0]) / ybound[2])
                            
                            # Convert to image coordinate (origin at top-left)
                            y1 = bev_height - y1 - 1
                            y2 = bev_height - y2 - 1
                            
                            # Draw line on BEV image
                            cv2.line(bev_img, (x1, y1), (x2, y2), color, 2)
                
                # Save visualization
                mmcv.imwrite(bev_img, osp.join(show_dir, f'lane_vis_{i:06d}.png'))
        
        batch_size = 1
        for _ in range(batch_size):
            prog_bar.update()


def main():
    args = parse_args()

    cfg = Config.fromfile(args.config)
    
    # set cudnn_benchmark
    if cfg.get('cudnn_benchmark', False):
        torch.backends.cudnn.benchmark = True
    
    # work_dir is determined in this priority: CLI > segment in file > filename
    if args.work_dir is not None:
        cfg.work_dir = args.work_dir
    elif cfg.get('work_dir', None) is None:
        cfg.work_dir = osp.join('./work_dirs',
                                osp.splitext(osp.basename(args.config))[0])
    
    cfg.gpu_ids = [0]
    
    # Create work_dir
    mmcv.mkdir_or_exist(osp.abspath(cfg.work_dir))
    # Dump config
    cfg.dump(osp.join(cfg.work_dir, osp.basename(args.config)))
    # Init logger
    timestamp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
    log_file = osp.join(cfg.work_dir, f'{timestamp}.log')
    logger = get_root_logger(log_file=log_file, log_level=cfg.log_level)
    
    # Enable lane grouping in model config if not already set
    if 'model' in cfg and 'bbox_head' in cfg.model:
        if 'group_lanes' not in cfg.model.bbox_head:
            logger.info("Setting group_lanes=True in config for testing")
            cfg.model.bbox_head.group_lanes = True
        if 'lane_group_min_distance' not in cfg.model.bbox_head:
            logger.info("Setting lane_group_min_distance=1.0 in config for testing")
            cfg.model.bbox_head.lane_group_min_distance = 1.0
    
    # Build the model and load checkpoint
    model = build_model(cfg.model)
    
    fp16_cfg = cfg.get('fp16', None)
    if fp16_cfg is not None:
        wrap_fp16_model(model)
    
    checkpoint = load_checkpoint(model, args.checkpoint, map_location='cpu')
    
    # Build the dataloader
    dataset = build_dataset(cfg.data.val)
    data_loader = build_dataloader(
        dataset,
        samples_per_gpu=1,
        workers_per_gpu=cfg.data.workers_per_gpu,
        dist=False,
        shuffle=False)
    
    # Set show_dir
    if args.show_dir is None:
        args.show_dir = osp.join(cfg.work_dir, 'lane_grouping_visualization')
    
    # Run visualization
    visualize_lanes(model, data_loader, args.show_dir, args.compare)
    
    logger.info(f'Visualizations saved to {args.show_dir}')


if __name__ == '__main__':
    main() 