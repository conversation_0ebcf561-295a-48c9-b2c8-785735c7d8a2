#!/usr/bin/env python
# Copyright (c) OpenMMLab. All rights reserved.

import argparse
import os
import os.path as osp
import time

import mmcv
import torch
from mmcv import Config
from mmcv.runner import build_optimizer
from mmcv.runner import load_checkpoint, wrap_fp16_model

from mmdet3d.datasets import build_dataloader, build_dataset
from mmdet3d.models import build_model
from mmdet3d.utils import collect_env, get_root_logger
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import cv2


def parse_args():
    parser = argparse.ArgumentParser(
        description='Validate lane detection model')
    parser.add_argument('config', help='test config file path')
    parser.add_argument('checkpoint', help='checkpoint file')
    parser.add_argument(
        '--work-dir',
        help='the dir to save logs and models',
        default=None)
    parser.add_argument(
        '--show-dir',
        help='directory where visualizations will be saved')
    parser.add_argument(
        '--eval-options',
        nargs='+',
        action=DictAction,
        help='custom options for evaluation')
    parser.add_argument(
        '--launcher',
        choices=['none', 'pytorch', 'slurm', 'mpi'],
        default='none',
        help='job launcher')
    parser.add_argument('--local_rank', type=int, default=0)
    args = parser.parse_args()
    if 'LOCAL_RANK' not in os.environ:
        os.environ['LOCAL_RANK'] = str(args.local_rank)
    return args


class DictAction(argparse.Action):
    """
    argparse action to split an argument into KEY=VALUE form
    on the first = and append to a dictionary. List options can
    be passed as comma separated values, i.e 'KEY=V1,V2,V3', or with explicit
    brackets, i.e. 'KEY=[V1,V2,V3]'. It also support nested brackets to build
    list/dict values. e.g. 'KEY=[(V1,V2),(V3,V4)]'
    """

    @staticmethod
    def _parse_int_float_bool(val):
        try:
            return int(val)
        except ValueError:
            try:
                return float(val)
            except ValueError:
                if val.lower() in ['true', 'false']:
                    return True if val.lower() == 'true' else False
                return val

    @staticmethod
    def _parse_iterable(val):
        """Parse iterable values in the string.

        All elements inside '()' or '[]' are treated as iterable values.

        Args:
            val (str): Value string.

        Returns:
            list | tuple: The parsed iterable value.
        """

        def find_next_comma(string):
            """Find the position of next comma in the string.

            If no ',' is found in the string, return the string length. All
            chars inside '()' and '[]' are treated as one element and the
            contained ',' is ignored.
            """
            assert (string.count('(') == string.count(')')) and (
                    string.count('[') == string.count(']')), \
                f'Imbalanced brackets exist in {string}'
            end = len(string)
            for idx, char in enumerate(string):
                pre = string[:idx]
                # The string before this ',' is balanced
                if ((char == ',') and (pre.count('(') == pre.count(')'))
                        and (pre.count('[') == pre.count(']'))):
                    end = idx
                    break
            return end

        # Strip ' and " characters and replace whitespace.
        val = val.strip('\'\"').replace(' ', '')
        is_tuple = False
        if val.startswith('(') and val.endswith(')'):
            is_tuple = True
            val = val[1:-1]
        elif val.startswith('[') and val.endswith(']'):
            val = val[1:-1]
        elif ',' not in val:
            # val is a single value
            return DictAction._parse_int_float_bool(val)

        values = []
        while len(val) > 0:
            comma_idx = find_next_comma(val)
            element = DictAction._parse_int_float_bool(val[:comma_idx])
            values.append(element)
            val = val[comma_idx + 1:]

        if is_tuple:
            return tuple(values)
        return values

    def __call__(self, parser, namespace, values, option_string=None):
        options = {}
        for kv in values:
            key, val = kv.split('=', maxsplit=1)
            if '[' in val or '(' in val:
                val = self._parse_iterable(val)
            else:
                val = self._parse_int_float_bool(val)
            options[key] = val
        setattr(namespace, self.dest, options)


def visualize_lanes(model, data_loader, show_dir):
    """Visualize lane detections."""
    model.eval()
    dataset = data_loader.dataset
    prog_bar = mmcv.ProgressBar(len(dataset))
    
    # Create colormap for visualization
    colors = [(0, 0, 0), (1, 0, 0), (0, 1, 0), (0, 0, 1), (1, 1, 0),
              (1, 0, 1), (0, 1, 1), (0.5, 0, 0), (0, 0.5, 0),
              (0, 0, 0.5), (0.5, 0.5, 0), (0.5, 0, 0.5), (0, 0.5, 0.5)]
    
    os.makedirs(show_dir, exist_ok=True)
    
    for i, data in enumerate(data_loader):
        with torch.no_grad():
            result = model(return_loss=False, rescale=True, **data)
        
        # Get BEV image for visualization
        if 'img_metas' in data:
            img_metas = data['img_metas'][0].data[0][0]
            xbound = img_metas['grid_conf']['xbound']
            ybound = img_metas['grid_conf']['ybound']
            
            # Create BEV canvas
            bev_height = int((ybound[1] - ybound[0]) / ybound[2])
            bev_width = int((xbound[1] - xbound[0]) / xbound[2])
            bev_img = np.zeros((bev_height, bev_width, 3), dtype=np.uint8)
            
            # Draw detected lanes
            if 'lane' in result:
                lane_result = result['lane']
                for lane_idx, lane in enumerate(lane_result):
                    lane_class = int(lane['class_id'])
                    if lane_class >= len(colors):
                        lane_class = lane_class % len(colors)
                    color = tuple(int(c * 255) for c in colors[lane_class])
                    
                    pts = lane['points']
                    for pt_idx in range(len(pts) - 1):
                        x1 = int((pts[pt_idx][0] - xbound[0]) / xbound[2])
                        y1 = int((pts[pt_idx][1] - ybound[0]) / ybound[2])
                        x2 = int((pts[pt_idx + 1][0] - xbound[0]) / xbound[2])
                        y2 = int((pts[pt_idx + 1][1] - ybound[0]) / ybound[2])
                        
                        # Convert to image coordinate (origin at top-left)
                        y1 = bev_height - y1 - 1
                        y2 = bev_height - y2 - 1
                        
                        # Draw line on BEV image
                        cv2.line(bev_img, (x1, y1), (x2, y2), color, 2)
            
            # Save visualization
            mmcv.imwrite(bev_img, osp.join(show_dir, f'lane_vis_{i:06d}.png'))
        
        batch_size = 1
        for _ in range(batch_size):
            prog_bar.update()


def main():
    args = parse_args()

    cfg = Config.fromfile(args.config)
    
    # set cudnn_benchmark
    if cfg.get('cudnn_benchmark', False):
        torch.backends.cudnn.benchmark = True
    
    # work_dir is determined in this priority: CLI > segment in file > filename
    if args.work_dir is not None:
        # update configs according to CLI args if args.work_dir is not None
        cfg.work_dir = args.work_dir
    elif cfg.get('work_dir', None) is None:
        # use config filename as default work_dir if cfg.work_dir is None
        cfg.work_dir = osp.join('./work_dirs',
                                osp.splitext(osp.basename(args.config))[0])
    
    cfg.gpu_ids = [0]
    
    # init distributed env first, since logger depends on the dist info.
    distributed = False
    
    # create work_dir
    mmcv.mkdir_or_exist(osp.abspath(cfg.work_dir))
    # dump config
    cfg.dump(osp.join(cfg.work_dir, osp.basename(args.config)))
    # init the logger before other steps
    timestamp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
    log_file = osp.join(cfg.work_dir, f'{timestamp}.log')
    logger = get_root_logger(log_file=log_file, log_level=cfg.log_level)

    # init the model and load checkpoint
    model = build_model(cfg.model)
    
    fp16_cfg = cfg.get('fp16', None)
    if fp16_cfg is not None:
        wrap_fp16_model(model)
    
    checkpoint = load_checkpoint(model, args.checkpoint, map_location='cpu')
    
    # build the dataloader
    dataset = build_dataset(cfg.data.val)
    data_loader = build_dataloader(
        dataset,
        samples_per_gpu=1,
        workers_per_gpu=cfg.data.workers_per_gpu,
        dist=distributed,
        shuffle=False)
    
    # Set show_dir
    if args.show_dir is None:
        args.show_dir = osp.join(cfg.work_dir, 'lane_visualization')
    
    # Run visualization
    visualize_lanes(model, data_loader, args.show_dir)
    
    logger.info(f'Visualizations saved to {args.show_dir}')


if __name__ == '__main__':
    main() 