#!/usr/bin/env python
"""
Visualize 3D lane detection dataset.

This script visualizes the 3D lane detection dataset to verify the processed data.
It can display:
1. 3D lanes in bird's eye view (BEV)
2. Projections of lanes on camera images
3. Basic statistics about the dataset
"""

import argparse
import os
import os.path as osp
import pickle
from typing import Dict, List

import cv2
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle


def parse_args():
    parser = argparse.ArgumentParser(description='Visualize 3D lane detection dataset')
    parser.add_argument('--pkl-file', type=str, required=True,
                        help='Path to the pickle file containing dataset information')
    parser.add_argument('--dataset-root', type=str, required=True,
                        help='Root directory of the dataset')
    parser.add_argument('--output-dir', type=str, default='visualization',
                        help='Output directory for visualization results')
    parser.add_argument('--num-samples', type=int, default=10,
                        help='Number of samples to visualize')
    parser.add_argument('--vis-mode', type=str, choices=['bev', 'camera', 'both'], default='both',
                        help='Visualization mode')
    parser.add_argument('--point-cloud-range', type=float, nargs=6, 
                        default=[-81.6, -48, -1, 97.6, 48, 3.0],
                        help='Point cloud range for BEV visualization')
    return parser.parse_args()


def load_dataset(pkl_file: str) -> List[Dict]:
    """Load dataset from pickle file.
    
    Args:
        pkl_file: Path to the pickle file
        
    Returns:
        List of frame information dictionaries
    """
    if not osp.exists(pkl_file):
        raise FileNotFoundError(f"Pickle file not found: {pkl_file}")
    
    with open(pkl_file, 'rb') as f:
        return pickle.load(f)


def visualize_bev(
    frame_info: Dict, 
    dataset_root: str, 
    point_cloud_range: List[float],
    output_path: str = None
) -> np.ndarray:
    """Visualize lanes in bird's eye view.
    
    Args:
        frame_info: Frame information dictionary
        dataset_root: Root directory of the dataset
        point_cloud_range: Point cloud range [x_min, y_min, z_min, x_max, y_max, z_max]
        output_path: Optional path to save the visualization
        
    Returns:
        BEV visualization image
    """
    # Extract point cloud range
    x_min, y_min, _, x_max, y_max, _ = point_cloud_range
    
    # Create figure
    plt.figure(figsize=(10, 6))
    plt.xlim(x_min, x_max)
    plt.ylim(y_min, y_max)
    plt.xlabel('X (m)')
    plt.ylabel('Y (m)')
    plt.title(f"BEV - Segment: {frame_info['segment_id']}, Frame: {frame_info['frame_id']}")
    
    # Plot ego vehicle position (assuming it's at origin)
    plt.plot(0, 0, 'ko', markersize=8)
    
    # Add rectangular outline for ego vehicle
    ego_rect = Rectangle((-1, -1), 2, 4, linewidth=1, edgecolor='black', facecolor='none')
    plt.gca().add_patch(ego_rect)
    
    # Define lane type colors
    lane_colors = {
        0: 'white',
        1: 'yellow',
        2: 'blue',
        3: 'red',
        4: 'green',
        5: 'purple',
        6: 'orange',
        7: 'cyan',
        8: 'magenta',
        9: 'lime',
        10: 'pink',
        11: 'teal',
        12: 'lavender',
        13: 'brown',
    }
    
    # Plot lane lines
    lane_legends = set()
    
    if 'annos' in frame_info:
        for lane in frame_info['annos']:
            if 'xyz' in lane and lane['xyz']:
                lane_type = lane.get('type_id', 0)
                lane_name = lane.get('type_name', f'Type {lane_type}')
                
                # Get lane color
                color = lane_colors.get(lane_type, 'gray')
                
                # Extract x, y coordinates
                points = np.array(lane['xyz'])
                plt.plot(points[:, 0], points[:, 1], '-', color=color, linewidth=2, 
                         label=lane_name if lane_name not in lane_legends else "")
                
                # Add lane type to legends set
                lane_legends.add(lane_name)
    
    # Add legend if there are lanes
    if lane_legends:
        plt.legend(loc='upper right')
    
    # Add grid
    plt.grid(True)
    
    # Save or show the figure
    if output_path:
        plt.savefig(output_path, dpi=200)
        plt.close()
    else:
        plt.tight_layout()
        plt.show()
    
    # Convert matplotlib figure to numpy array for return
    fig = plt.gcf()
    fig.canvas.draw()
    img = np.array(fig.canvas.renderer.buffer_rgba())
    plt.close()
    
    return img


def visualize_camera_view(
    frame_info: Dict, 
    dataset_root: str, 
    camera_name: str = '120_front',
    output_path: str = None
) -> np.ndarray:
    """Visualize lanes projected on camera image.
    
    Args:
        frame_info: Frame information dictionary
        dataset_root: Root directory of the dataset
        camera_name: Camera name for visualization
        output_path: Optional path to save the visualization
        
    Returns:
        Camera view visualization image
    """
    # Find camera image path
    cam_key = camera_name.replace('-', '_')
    if cam_key not in frame_info['cam_paths']:
        print(f"Warning: {camera_name} not found in frame")
        return None
    
    img_path = osp.join(dataset_root, frame_info['cam_paths'][cam_key])
    if not osp.exists(img_path):
        print(f"Warning: Image file not found: {img_path}")
        return None
    
    # Load image
    img = cv2.imread(img_path)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # Define lane type colors (BGR format for OpenCV)
    lane_colors = {
        0: (255, 255, 255),  # white
        1: (0, 255, 255),    # yellow
        2: (255, 0, 0),      # blue
        3: (0, 0, 255),      # red
        4: (0, 255, 0),      # green
        5: (128, 0, 128),    # purple
        6: (0, 165, 255),    # orange
        7: (255, 255, 0),    # cyan
        8: (255, 0, 255),    # magenta
        9: (0, 255, 128),    # lime
        10: (203, 192, 255), # pink
        11: (128, 128, 0),   # teal
        12: (216, 191, 216), # lavender
        13: (42, 42, 165),   # brown
    }
    
    # Plot lane lines (UV projections if available)
    if 'annos' in frame_info:
        for lane in frame_info['annos']:
            if 'uv' in lane and lane['uv']:
                lane_type = lane.get('type_id', 0)
                
                # Get lane color
                color = lane_colors.get(lane_type, (200, 200, 200))
                
                # Extract u, v coordinates
                points = np.array(lane['uv']).astype(np.int32)
                
                # Filter points outside image boundaries
                valid_points = []
                h, w = img.shape[:2]
                
                for pt in points:
                    u, v = pt
                    if 0 <= u < w and 0 <= v < h:
                        valid_points.append([u, v])
                
                # Draw polyline if enough valid points
                if len(valid_points) >= 2:
                    cv2.polylines(img, [np.array(valid_points)], False, color, 2)
    
    # Add frame info as text
    cv2.putText(img, f"Segment: {frame_info['segment_id']}, Frame: {frame_info['frame_id']}", 
                (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # Save the visualization if output path is provided
    if output_path:
        cv2.imwrite(output_path, cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
    
    return img


def calculate_dataset_statistics(dataset: List[Dict]) -> Dict:
    """Calculate basic statistics about the dataset.
    
    Args:
        dataset: List of frame information dictionaries
        
    Returns:
        Dictionary with statistics
    """
    stats = {
        'num_frames': len(dataset),
        'num_segments': len(set(frame['segment_id'] for frame in dataset)),
        'num_lanes': 0,
        'lane_types': {},
        'avg_points_per_lane': 0,
        'cameras': {},
    }
    
    total_points = 0
    
    for frame in dataset:
        # Count cameras
        if 'cam_paths' in frame:
            for cam in frame['cam_paths'].keys():
                stats['cameras'][cam] = stats['cameras'].get(cam, 0) + 1
        
        # Count lanes and points
        if 'annos' in frame:
            stats['num_lanes'] += len(frame['annos'])
            
            for lane in frame['annos']:
                # Count lane types
                lane_type = lane.get('type_id', -1)
                lane_name = lane.get('type_name', f'Unknown Type {lane_type}')
                type_key = f"{lane_type}: {lane_name}"
                stats['lane_types'][type_key] = stats['lane_types'].get(type_key, 0) + 1
                
                # Count points
                if 'xyz' in lane:
                    total_points += len(lane['xyz'])
    
    # Calculate average points per lane
    if stats['num_lanes'] > 0:
        stats['avg_points_per_lane'] = total_points / stats['num_lanes']
    
    return stats


def main():
    args = parse_args()
    
    # Load dataset
    dataset = load_dataset(args.pkl_file)
    print(f"Loaded {len(dataset)} frames from {args.pkl_file}")
    
    # Calculate statistics
    stats = calculate_dataset_statistics(dataset)
    print("\nDataset Statistics:")
    print(f"Number of frames: {stats['num_frames']}")
    print(f"Number of segments: {stats['num_segments']}")
    print(f"Number of lanes: {stats['num_lanes']}")
    print(f"Average points per lane: {stats['avg_points_per_lane']:.2f}")
    
    print("\nLane Types:")
    for type_name, count in sorted(stats['lane_types'].items()):
        print(f"  {type_name}: {count}")
    
    print("\nCameras:")
    for cam, count in sorted(stats['cameras'].items()):
        print(f"  {cam}: {count}")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Visualize samples
    num_samples = min(args.num_samples, len(dataset))
    sample_indices = np.linspace(0, len(dataset) - 1, num_samples, dtype=int)
    
    for i, idx in enumerate(sample_indices):
        frame_info = dataset[idx]
        print(f"\nVisualizing sample {i+1}/{num_samples} (index {idx}):")
        print(f"  Segment: {frame_info['segment_id']}")
        print(f"  Frame: {frame_info['frame_id']}")
        
        if args.vis_mode in ['bev', 'both']:
            # BEV visualization
            bev_output_path = osp.join(args.output_dir, f"sample_{i+1}_bev.png")
            visualize_bev(frame_info, args.dataset_root, args.point_cloud_range, bev_output_path)
            print(f"  BEV visualization saved to {bev_output_path}")
        
        if args.vis_mode in ['camera', 'both']:
            # Camera visualization
            if 'cam_paths' in frame_info and '120_front' in frame_info['cam_paths']:
                cam_output_path = osp.join(args.output_dir, f"sample_{i+1}_camera.png")
                visualize_camera_view(frame_info, args.dataset_root, '120_front', cam_output_path)
                print(f"  Camera visualization saved to {cam_output_path}")
    
    print("\nVisualization complete.")


if __name__ == '__main__':
    main()