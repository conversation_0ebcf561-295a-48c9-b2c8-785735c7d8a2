# 3D Lane Detection Visualization Tools

This module provides tools for visualizing 3D lane detection results in different perspectives:

1. **<PERSON>'s Eye View (BEV)** - Top-down view of lane lines
2. **Camera View** - Projection of 3D lanes onto camera images
3. **3D View** - 3D visualization of lane lines with height information

## Usage

### Importing the Module

```python
from visualizer_tool.visualize_3d_lane_results import (
    visualize_bev_lanes,
    visualize_camera_lanes,
    visualize_3d_lanes,
    visualize_lane_detection_results
)
```

### Standalone Usage

The module can be used as a standalone script to visualize already generated JSON results:

```bash
python tools/visualizer_tool/visualize_3d_lane_results.py \
    --results /path/to/lane_results.json \
    --img-dir /path/to/image/directory \
    --calib-file /path/to/calibration.json \
    --output-dir vis_results \
    --vis-mode all
```

### Function Documentation

#### visualize_lane_detection_results

This is the main high-level function that produces all types of visualizations:

```python
visualize_lane_detection_results(
    results,               # Dictionary with pred_lanes, pred_lane_types, pred_scores
    img=None,              # Optional camera image
    camera_intrinsics=None,# Required for camera view
    lidar2camera=None,     # Required for camera view
    point_cloud_range=None,# BEV/3D space range
    output_dir=None,       # Directory to save visualizations
    prefix=None,           # Filename prefix
    lane_classes=None,     # Lane class names
    show_score_thr=0.3,    # Score threshold for visualization
    vis_modes=('bev', '3d', 'camera')  # Visualization modes
)
```

#### Individual Visualization Functions

For more fine-grained control, you can use the individual visualization functions:

```python
# BEV Visualization
bev_img = visualize_bev_lanes(
    lanes, lane_types, lane_scores, 
    output_path="lane_bev.png",
    point_cloud_range=[0.0, -48.0, -1.0, 97.6, 48.0, 3.0],
    show_score_thr=0.3,
    lane_classes=None,
    title="Bird's Eye View Lane Detection"
)

# Camera View Visualization
cam_img = visualize_camera_lanes(
    lanes, lane_types, lane_scores,
    camera_intrinsics, lidar2camera,
    cam_img=image,
    output_path="lane_camera.png",
    show_score_thr=0.3,
    lane_classes=None
)

# 3D Visualization
vis_3d_img = visualize_3d_lanes(
    lanes, lane_types, lane_scores,
    output_path="lane_3d.png",
    point_cloud_range=[0.0, -48.0, -1.0, 97.6, 48.0, 3.0],
    show_score_thr=0.3,
    lane_classes=None,
    elev=20, azim=-70
)
```

## Output Examples

### Bird's Eye View
The BEV visualization shows lane lines from a top-down perspective:
- Ego vehicle is represented at the origin (0,0) with a black marker
- Different lane types are color-coded
- Arrows indicate lane direction
- Grid lines indicate distance in meters

### Camera View
The camera view projects the 3D lane lines onto the original camera image:
- Different lane types are color-coded
- Each lane is labeled with its type and confidence score
- Only lanes visible in the camera's field of view are shown

### 3D View
The 3D visualization shows the complete 3D lane geometry:
- Height information is visualized on the Z-axis
- Different lane types are color-coded
- View angle can be customized via elev and azim parameters 