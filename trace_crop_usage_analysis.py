#!/usr/bin/env python3
"""
Trace Crop Usage Analysis
追踪task_area_scope在整个代码库中的使用情况
"""

import os
import re
from pathlib import Path

def analyze_task_area_scope_usage():
    """
    分析task_area_scope在代码中的使用情况
    """
    print("="*80)
    print("TASK_AREA_SCOPE USAGE ANALYSIS")
    print("="*80)
    
    # 1. 在BEVLaneHeatmapHead中的使用
    print("\n1. BEVLaneHeatmapHead中的task_area_scope使用：")
    print("-" * 50)
    
    bev_head_file = "mmdet3d/models/heads/lane/bev_lane_heatmap_head.py"
    if os.path.exists(bev_head_file):
        with open(bev_head_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找task_area_scope相关代码
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if 'task_area_scope' in line.lower():
                print(f"  Line {i}: {line.strip()}")
                
        # 查找enable_crop相关代码
        print("\n  enable_crop相关代码：")
        for i, line in enumerate(lines, 1):
            if 'enable_crop' in line.lower():
                print(f"  Line {i}: {line.strip()}")
                
        # 查找_crop_bev_features方法
        print("\n  _crop_bev_features方法：")
        in_crop_method = False
        for i, line in enumerate(lines, 1):
            if 'def _crop_bev_features' in line:
                in_crop_method = True
                print(f"  Line {i}: {line.strip()}")
            elif in_crop_method and line.strip().startswith('def '):
                break
            elif in_crop_method and line.strip():
                print(f"  Line {i}: {line.strip()}")
    
    # 2. 在GenerateBEVLaneHeatmapTargets中的使用（应该没有）
    print("\n\n2. GenerateBEVLaneHeatmapTargets中的task_area_scope使用：")
    print("-" * 50)
    
    lane_processing_file = "mmdet3d/datasets/pipelines/lane_processing.py"
    if os.path.exists(lane_processing_file):
        with open(lane_processing_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找task_area_scope相关代码
        lines = content.split('\n')
        task_area_found = False
        for i, line in enumerate(lines, 1):
            if 'task_area_scope' in line.lower():
                print(f"  Line {i}: {line.strip()}")
                task_area_found = True
                
        if not task_area_found:
            print("  ❌ 未找到task_area_scope相关代码")
            print("  ❌ GenerateBEVLaneHeatmapTargets完全不支持task_area_scope")
            
        # 查找grid_size的计算
        print("\n  grid_size计算相关代码：")
        for i, line in enumerate(lines, 1):
            if 'grid_size' in line.lower() and ('=' in line or 'self.grid_size' in line):
                print(f"  Line {i}: {line.strip()}")
    
    # 3. 在配置文件中的使用
    print("\n\n3. 配置文件中的task_area_scope：")
    print("-" * 50)
    
    config_file = "configs/lane/bevfusion_lane_detection_embadding_config.yaml"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        lines = content.split('\n')
        in_bev_head = False
        in_gt_generator = False
        
        for i, line in enumerate(lines, 1):
            if 'BEVLaneHeatmapHead' in line:
                in_bev_head = True
                in_gt_generator = False
                print(f"\n  BEV Head配置 (Line {i}):")
            elif 'GenerateBEVLaneHeatmapTargets' in line:
                in_bev_head = False
                in_gt_generator = True
                print(f"\n  GT Generator配置 (Line {i}):")
            elif line.strip().startswith('- type:') and 'BEVLaneHeatmapHead' not in line and 'GenerateBEVLaneHeatmapTargets' not in line:
                in_bev_head = False
                in_gt_generator = False
                
            if 'task_area_scope' in line.lower():
                if in_bev_head:
                    print(f"    ✅ BEV Head Line {i}: {line.strip()}")
                elif in_gt_generator:
                    print(f"    ✅ GT Generator Line {i}: {line.strip()}")
                else:
                    print(f"    Line {i}: {line.strip()}")
                    
            elif (in_bev_head or in_gt_generator) and ('enable_crop' in line.lower() or 'x_range' in line.lower() or 'y_range' in line.lower()):
                prefix = "BEV Head" if in_bev_head else "GT Generator"
                print(f"    {prefix} Line {i}: {line.strip()}")
    
    # 4. 数据流分析
    print("\n\n4. 数据流分析：")
    print("-" * 50)
    print("  配置文件 → 模型初始化 → 前向传播 → 损失计算")
    print("")
    print("  BEV Head路径：")
    print("    config.yaml → BEVLaneHeatmapHead.__init__(task_area_scope) → forward() → _crop_bev_features()")
    print("    ✅ 支持task_area_scope裁剪")
    print("")
    print("  GT Generator路径：")
    print("    config.yaml → GenerateBEVLaneHeatmapTargets.__init__() → __call__()")
    print("    ❌ 不接收task_area_scope参数")
    print("    ❌ 始终生成全尺寸GT目标")
    print("")
    print("  损失计算：")
    print("    loss_function(pred_cropped, gt_full) → 维度不匹配 → 训练崩溃")
    
    # 5. 根本原因总结
    print("\n\n5. 根本原因总结：")
    print("-" * 50)
    print("  ❌ 架构不对称：")
    print("     - BEV Head: 支持task_area_scope，可以裁剪输出")
    print("     - GT Generator: 不支持task_area_scope，始终全尺寸输出")
    print("")
    print("  ❌ 配置传递缺失：")
    print("     - task_area_scope只传递给BEV Head")
    print("     - GT Generator的配置中没有task_area_scope字段")
    print("")
    print("  ❌ 维度计算不一致：")
    print("     - BEV Head: 根据task_area_scope动态计算输出尺寸")
    print("     - GT Generator: 根据grid_conf固定计算尺寸")
    
    print("\n" + "="*80)
    print("CONCLUSION: GT Generator需要添加task_area_scope支持")
    print("="*80)

if __name__ == "__main__":
    analyze_task_area_scope_usage()