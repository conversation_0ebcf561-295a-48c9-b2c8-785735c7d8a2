#!/bin/bash

# Configuration
GPUS=4
PORT=12098
CONFIG1=configs/mogo/v29_B2_second1231_3dseg_cls21_cam5lidar_intensityraw.yaml
RUN_DIR1=experiments/v29_B2_second1231_3dseg_cls21_cam5lidar_intensityraw
MODEL1=$RUN_DIR1/latest.pth

# Environment optimization
export CUDA_VISIBLE_DEVICES=0,1,2,3
export OMP_NUM_THREADS=4
export NCCL_DEBUG=INFO
export NCCL_IB_DISABLE=0
export NCCL_IB_HCA=mlx5
export NCCL_SOCKET_IFNAME=eth0
export NCCL_MIN_NCHANNELS=4
export CUDA_LAUNCH_BLOCKING=0
export CUDA_DEVICE_MAX_CONNECTIONS=1
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Function to check GPU availability and memory
check_gpus() {
    if ! command -v nvidia-smi &> /dev/null; then
        echo "Error: NVIDIA driver not found"
        exit 1
    fi

    local required_gpus=$1
    local available_gpus=$(nvidia-smi -L | wc -l)
    if [ $available_gpus -lt $required_gpus ]; then
        echo "Error: Required $required_gpus GPUs but only $available_gpus available"
        exit 1
    fi

    # Check GPU memory (minimum 32GB required)
    local min_memory=32000
    for i in $(seq 0 $((required_gpus-1))); do
        local memory=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits -i $i)
        if [ $memory -lt $min_memory ]; then
            echo "Error: GPU $i has insufficient memory (${memory}MB < ${min_memory}MB)"
            exit 1
        fi
    done
}

# Function to setup logging
setup_logging() {
    local version=$1
    LOG_DIR="logs/${version}"
    mkdir -p ${LOG_DIR}
    exec &> >(tee -a "${LOG_DIR}/training_$(date +%Y%m%d_%H%M%S).log")
}

# Main execution
handle_error() {
    echo "ERROR: $1" >&2
    echo "Training failed at $(date)" >> logs/training_errors.log
    echo "$1" >> logs/training_errors.log
    exit 1
}

check_gpus ${GPUS} || handle_error "GPU check failed"
setup_logging "v29_B2"

echo "Starting distributed training with configuration:"
echo "Config path: ${CONFIG1}"
echo "Number of GPUs: ${GPUS}"
echo "Version: v29_B2"
echo "$(date): Training started with config ${CONFIG1}" | tee -a logs/training_history.log

# Training command with PyTorch 1.12.1 optimizations
torchrun \
    --nproc_per_node=${GPUS} \
    --master_port=${PORT} \
    tools/train.py \
    --config ${CONFIG1} \
    --run_dir ${RUN_DIR1} \
    --launcher pytorch

TRAIN_EXIT_CODE=$?

if [ $TRAIN_EXIT_CODE -ne 0 ]; then
    echo "$(date): Training failed with exit code $TRAIN_EXIT_CODE" | tee -a logs/training_errors.log
    exit $TRAIN_EXIT_CODE
else
    echo "$(date): Training completed successfully" | tee -a logs/training_history.log
fi
