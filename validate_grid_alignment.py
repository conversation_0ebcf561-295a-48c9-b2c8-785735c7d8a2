#!/usr/bin/env python3
"""
Validate grid alignment between global BEV and task area scope.
"""

def validate_grid_alignment():
    """Check if the task area aligns properly with the global grid"""
    
    # Global grid configuration
    global_x_min, global_x_max, x_res = -81.6, 97.6, 0.4
    global_y_min, global_y_max, y_res = -48.0, 48.0, 0.4
    
    # Task area configuration (from your changes)
    task_x_min, task_x_max = 0.0, 60.0
    task_y_min, task_y_max = -15.0, 15.0
    
    print("=== Grid Alignment Validation ===")
    print(f"Global range: x[{global_x_min}, {global_x_max}], y[{global_y_min}, {global_y_max}]")
    print(f"Task range:   x[{task_x_min}, {task_x_max}], y[{task_y_min}, {task_y_max}]")
    print(f"Resolution:   {x_res}m x {y_res}m")
    
    # Check if task area is within global bounds
    x_within = global_x_min <= task_x_min and task_x_max <= global_x_max
    y_within = global_y_min <= task_y_min and task_y_max <= global_y_max
    
    print(f"\nBounds check:")
    print(f"  X-axis within global: {'✓' if x_within else '✗'}")
    print(f"  Y-axis within global: {'✓' if y_within else '✗'}")
    
    # Check grid alignment (should align to pixel boundaries)
    x_start_aligned = (task_x_min - global_x_min) % x_res == 0
    x_end_aligned = (task_x_max - global_x_min) % x_res == 0
    y_start_aligned = (task_y_min - global_y_min) % y_res == 0
    y_end_aligned = (task_y_max - global_y_min) % y_res == 0
    
    print(f"\nPixel alignment check:")
    print(f"  X-start aligned: {'✓' if x_start_aligned else '✗'}")
    print(f"  X-end aligned:   {'✓' if x_end_aligned else '✗'}")
    print(f"  Y-start aligned: {'✓' if y_start_aligned else '✗'}")
    print(f"  Y-end aligned:   {'✓' if y_end_aligned else '✗'}")
    
    # Calculate grid indices
    crop_x_start = int((task_x_min - global_x_min) / x_res)
    crop_x_end = int((task_x_max - global_x_min) / x_res)
    crop_y_start = int((task_y_min - global_y_min) / y_res)
    crop_y_end = int((task_y_max - global_y_min) / y_res)
    
    print(f"\nCrop indices:")
    print(f"  X: [{crop_x_start}:{crop_x_end}] (size: {crop_x_end - crop_x_start})")
    print(f"  Y: [{crop_y_start}:{crop_y_end}] (size: {crop_y_end - crop_y_start})")
    
    # Calculate efficiency gain
    global_pixels = int((global_x_max - global_x_min) / x_res) * int((global_y_max - global_y_min) / y_res)
    task_pixels = (crop_x_end - crop_x_start) * (crop_y_end - crop_y_start)
    efficiency_gain = (1 - task_pixels / global_pixels) * 100
    
    print(f"\nEfficiency analysis:")
    print(f"  Global pixels: {global_pixels:,}")
    print(f"  Task pixels:   {task_pixels:,}")
    print(f"  Reduction:     {efficiency_gain:.1f}%")
    
    return x_within and y_within and x_start_aligned and x_end_aligned and y_start_aligned and y_end_aligned

if __name__ == "__main__":
    is_valid = validate_grid_alignment()
    print(f"\n{'✓ Configuration is valid!' if is_valid else '✗ Configuration needs adjustment!'}")