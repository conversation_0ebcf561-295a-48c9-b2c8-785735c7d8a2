#!/bin/bash

CONFIG=$1
CHECKPOINT=$2
WORK_DIR=$3

if [ -z "$CONFIG" ]; then
    echo "Error: Config file not specified"
    echo "Usage: ./validate_lane_grouping.sh CONFIG CHECKPOINT [WORK_DIR]"
    exit 1
fi

if [ -z "$CHECKPOINT" ]; then
    echo "Error: Checkpoint file not specified"
    echo "Usage: ./validate_lane_grouping.sh CONFIG CHECKPOINT [WORK_DIR]"
    exit 1
fi

if [ -z "$WORK_DIR" ]; then
    # Use default work_dir derived from config file name
    WORK_DIR="./work_dirs/$(basename ${CONFIG%.*})"
    echo "Using default work directory: $WORK_DIR"
fi

# Create work directory if it doesn't exist
mkdir -p "$WORK_DIR"

# Define visualization directory
SHOW_DIR="${WORK_DIR}/lane_grouping_visualization"
mkdir -p "$SHOW_DIR"

# Run with comparison mode to visualize both grouped and ungrouped lanes
python tools/utils/test_lane_grouping.py \
    "$CONFIG" \
    "$CHECKPOINT" \
    --work-dir "$WORK_DIR" \
    --show-dir "$SHOW_DIR" \
    --compare

echo "Lane grouping validation complete. Visualizations saved to: $SHOW_DIR" 