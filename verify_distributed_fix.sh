#!/bin/bash

# === Verification Script for Distributed Training Fix ===
# This script tests the fix for the "unused parameters" error in distributed training

echo "=== BEVFusion Distributed Training Fix Verification ==="
echo "Timestamp: $(date)"
echo

# Configuration
CONFIG=configs/lane/config_3d_lane_detection_task_based_bevfusion_embadding_new_params_distributed_training.yaml
RUN_DIR=experiments/distributed_fix_test
GPUS=4
PORT=29501

# Create test run directory
echo "Creating test run directory: $RUN_DIR"
mkdir -p $RUN_DIR

# Check configuration file
echo "Checking configuration file: $CONFIG"
if [ ! -f "$CONFIG" ]; then
    echo "ERROR: Configuration file not found: $CONFIG"
    exit 1
fi

# Verify find_unused_parameters setting
echo "Verifying find_unused_parameters setting..."
grep -n "find_unused_parameters" $CONFIG
if grep -q "find_unused_parameters: true" $CONFIG; then
    echo "✓ find_unused_parameters is set to true"
else
    echo "✗ find_unused_parameters is not set to true"
    echo "Please ensure find_unused_parameters: true in the config file"
    exit 1
fi

# Check GPU availability
echo
echo "Checking GPU availability..."
nvidia-smi --query-gpu=index,name,memory.total,memory.used --format=csv,noheader,nounits
echo

# Check if GPUs are available
GPU_COUNT=$(nvidia-smi --query-gpu=count --format=csv,noheader,nounits | head -1)
echo "Available GPUs: $GPU_COUNT"
if [ "$GPU_COUNT" -lt "$GPUS" ]; then
    echo "WARNING: Requested $GPUS GPUs but only $GPU_COUNT available"
    echo "Adjusting GPUS to $GPU_COUNT"
    GPUS=$GPU_COUNT
fi

# Set environment variables for debugging
echo "Setting environment variables for debugging..."
export CUDA_VISIBLE_DEVICES=0,1,2,3
export NCCL_DEBUG=INFO
export NCCL_SOCKET_IFNAME=eth0
export TORCH_DISTRIBUTED_DEBUG=DETAIL

echo "Environment variables set:"
echo "  CUDA_VISIBLE_DEVICES=$CUDA_VISIBLE_DEVICES"
echo "  NCCL_DEBUG=$NCCL_DEBUG"
echo "  TORCH_DISTRIBUTED_DEBUG=$TORCH_DISTRIBUTED_DEBUG"
echo

# Check if port is available
echo "Checking if port $PORT is available..."
if netstat -tuln | grep -q ":$PORT "; then
    echo "WARNING: Port $PORT is in use. Trying alternative ports..."
    for alt_port in 29502 29503 29504 29505; do
        if ! netstat -tuln | grep -q ":$alt_port "; then
            PORT=$alt_port
            echo "Using alternative port: $PORT"
            break
        fi
    done
else
    echo "✓ Port $PORT is available"
fi

# Start distributed training test
echo
echo "=== Starting Distributed Training Test ==="
echo "Configuration: $CONFIG"
echo "Output directory: $RUN_DIR"
echo "GPUs: $GPUS"
echo "Master port: $PORT"
echo

echo "Command to execute:"
echo "torchrun --nproc_per_node=$GPUS --master_port=$PORT tools/train.py $CONFIG $RUN_DIR --launcher pytorch"
echo

# Ask for confirmation
read -p "Do you want to proceed with the test? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Test cancelled by user."
    exit 0
fi

# Execute the training command
echo "Starting distributed training..."
torchrun --nproc_per_node=$GPUS \
    --master_port=$PORT \
    tools/train.py \
    $CONFIG \
    $RUN_DIR \
    --launcher pytorch

TRAIN_EXIT_CODE=$?

echo
echo "=== Training Test Results ==="
if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    echo "✓ Distributed training started successfully!"
    echo "✓ No 'unused parameters' error encountered"
    echo "✓ Fix appears to be working"
else
    echo "✗ Distributed training failed with exit code: $TRAIN_EXIT_CODE"
    echo "✗ Please check the error logs above"
fi

echo
echo "Test completed at: $(date)"
echo "Log files should be available in: $RUN_DIR"
echo

# Cleanup suggestion
echo "To clean up test files, run:"
echo "rm -rf $RUN_DIR"

exit $TRAIN_EXIT_CODE