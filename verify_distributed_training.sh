#!/bin/bash

# === Verification Script for Distributed Training Configuration ===
# This script performs a quick test of the distributed training setup

# Configuration - using minimal settings for verification
GPUS=2  # Use fewer GPUs for quick testing
PORT=$(shuf -i 20000-65000 -n 1)  # Random port to avoid conflicts
CONFIG=configs/lane/config_3d_lane_detection_task_based_bevfusion_embadding_new_params_distributed_training.yaml
TEST_DIR=experiments/verify_distributed_test

# Create test directory
mkdir -p $TEST_DIR

# Basic environment setup for testing
export CUDA_VISIBLE_DEVICES=0,1  # Only use first 2 GPUs for test
export NCCL_DEBUG=INFO
export TORCH_DISTRIBUTED_DEBUG=DETAIL

# Verify config file exists
if [ ! -f "$CONFIG" ]; then
    echo "Error: Config file $CONFIG not found!"
    exit 1
fi

# Check for find_unused_parameters setting in config
echo "Checking 'find_unused_parameters' setting in config..."
if grep -q "find_unused_parameters" "$CONFIG"; then
    echo "Found 'find_unused_parameters' setting in config:"
    grep -n "find_unused_parameters" "$CONFIG"
else
    echo "Warning: 'find_unused_parameters' setting not found in config."
    echo "You may need to add it if you encounter DDP unused parameter errors."
fi

# Check GPU availability
echo "Verifying GPU availability..."
if ! command -v nvidia-smi &> /dev/null; then
    echo "Error: NVIDIA driver not found"
    exit 1
fi

GPU_COUNT=$(nvidia-smi -L | wc -l)
if [ $GPU_COUNT -lt $GPUS ]; then
    echo "Error: Required $GPUS GPUs but only $GPU_COUNT available"
    exit 1
fi

echo "GPU check passed: $GPU_COUNT GPUs available, using $GPUS for test"

# Print test configuration
echo "Starting distributed training verification with:"
echo "Config: $CONFIG"
echo "GPUs: $GPUS"
echo "Port: $PORT"
echo "Test directory: $TEST_DIR"

# Run a short training test (1 iteration only)
echo "Running short distributed training test..."
torchrun \
    --nproc_per_node=$GPUS \
    --master_port=$PORT \
    tools/train.py \
    --config $CONFIG \
    --run_dir $TEST_DIR \
    --launcher pytorch \
    --cfg-options runner.max_epochs=1 runner.max_iters=1

TEST_EXIT_CODE=$?

# Check test results
if [ $TEST_EXIT_CODE -ne 0 ]; then
    echo "Verification FAILED with exit code $TEST_EXIT_CODE"
    echo "Please check the error messages above for troubleshooting."
    exit $TEST_EXIT_CODE
else
    echo "Verification PASSED! Distributed training configuration appears to be working correctly."
    echo "You can now run the full training with: ./multi_gpu_distributed_training.sh"
fi